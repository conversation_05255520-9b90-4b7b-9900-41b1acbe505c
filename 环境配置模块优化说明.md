# 环境配置模块优化说明

## 优化内容

本次对环境配置模块进行了两个重要优化：

### 1. 添加保存成功提示

**问题描述：**
- Token自动获取配置和验证码配置tab页面点击确定时没有保存成功提示
- 用户无法确认配置是否已成功保存

**解决方案：**
在每个tab页面添加了独立的保存按钮，并在保存成功后显示相应的提示信息。

**修改文件：**
- `web/src/views/project/env_config/index.vue`

**具体修改：**

1. **基本信息tab**：添加了"保存基本信息"按钮
2. **Token自动获取配置tab**：添加了"保存Token配置"按钮，保存成功后显示"Token配置保存成功"
3. **验证码配置tab**：添加了"保存验证码配置"按钮，保存成功后显示"验证码配置保存成功"

**新增方法：**
```javascript
// 保存基本信息
const handleSaveBasicInfo = async () => {
  await customHandleSave()
}

// 保存Token配置
const handleSaveTokenConfig = async () => {
  await customHandleSave()
  window.$message?.success('Token配置保存成功')
}

// 保存验证码配置
const handleSaveCaptchaConfig = async () => {
  await customHandleSave()
  window.$message?.success('验证码配置保存成功')
}
```

### 2. 支持URL占位符

**问题描述：**
- Token自动获取配置的"请求链接"字段不支持占位符替换
- 无法在URL中使用验证码相关的占位符如 `${code}` 和 `${codekey}`

**解决方案：**
在后端Token服务中添加了URL占位符替换功能，支持在Token获取URL中使用验证码占位符。

**修改文件：**
- `web/src/views/project/env_config/index.vue` - 前端界面提示
- `app/services/token_service.py` - 后端占位符替换逻辑

**前端修改：**
1. 更新了"请求链接"字段的placeholder提示：`支持占位符: ${code}, ${codekey}`
2. 添加了占位符使用说明：`💡 支持验证码占位符：${code}, ${codekey} 等`

**后端修改：**
1. 在`fetch_token`方法中添加了URL占位符替换逻辑
2. 新增了`_replace_url_placeholders`方法来处理URL中的占位符替换

**新增方法：**
```python
def _replace_url_placeholders(self, url: str, recognized_code: str, code_key: str) -> str:
    """
    替换URL中的验证码占位符
    
    Args:
        url: 原始URL字符串
        recognized_code: 识别出的验证码
        code_key: 验证码Key
        
    Returns:
        替换后的URL字符串
    """
    # 支持多种占位符格式
    placeholders = {
        '${code}': recognized_code,
        '${codekey}': code_key,
        '${codeKey}': code_key,  # 支持驼峰命名
        '${CODE}': recognized_code,  # 支持大写
        '${CODEKEY}': code_key,
        # 也支持不带$的格式
        '{code}': recognized_code,
        '{codekey}': code_key,
        '{codeKey}': code_key,
    }
    
    # 执行替换并返回结果
    updated_url = url
    for placeholder, value in placeholders.items():
        if placeholder in updated_url:
            updated_url = updated_url.replace(placeholder, str(value))
    
    return updated_url
```

## 支持的占位符格式

系统现在支持以下占位符格式：

### URL和请求体中都支持：
- `${code}` - 识别出的验证码
- `${codekey}` - 验证码Key（小写）
- `${codeKey}` - 验证码Key（驼峰命名）
- `${CODE}` - 识别出的验证码（大写）
- `${CODEKEY}` - 验证码Key（大写）
- `{code}` - 识别出的验证码（不带$符号）
- `{codekey}` - 验证码Key（不带$符号）
- `{codeKey}` - 验证码Key（不带$符号，驼峰命名）

## 使用示例

### URL占位符使用示例：
```
http://10.162.22.59:29001/api/upms/auth/login?key=${codekey}&captcha=${code}
```

### 请求体占位符使用示例：
```json
{
  "code": "${code}",
  "codeKey": "${codekey}",
  "password": "2C6i4jZhS7U97Jj1uB4Biw==",
  "username": "tanxi",
  "grantType": "password"
}
```

## 工作流程

1. **验证码获取**：系统首先调用验证码API获取验证码图片和Key
2. **验证码识别**：使用ddddocr库识别验证码内容
3. **占位符替换**：
   - 替换Token获取URL中的占位符
   - 替换请求体中的占位符
4. **Token获取**：使用替换后的URL和请求体获取Token
5. **保存Token**：将获取到的Token保存到环境配置中

## 注意事项

1. **占位符替换顺序**：URL占位符替换在请求体占位符替换之后进行
2. **错误处理**：如果占位符替换失败，系统会记录错误日志并使用原始URL
3. **兼容性**：新功能完全向后兼容，不影响现有的环境配置
4. **日志记录**：所有占位符替换操作都会记录详细的日志信息，便于调试

## 测试建议

1. **基本功能测试**：验证保存按钮和成功提示是否正常工作
2. **URL占位符测试**：在Token获取URL中使用占位符，验证是否正确替换
3. **请求体占位符测试**：在请求体中使用占位符，验证是否正确替换
4. **混合使用测试**：同时在URL和请求体中使用占位符，验证是否都能正确替换
5. **错误处理测试**：测试占位符格式错误或验证码获取失败的情况
