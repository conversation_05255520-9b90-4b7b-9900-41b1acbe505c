# 验证码占位符替换功能说明

## 功能概述

在**环境管理**的Token自动获取配置中，支持在请求体中使用占位符，系统会在Token刷新时自动获取并识别验证码，然后替换请求体中的占位符。这样可以实现完全自动化的Token刷新，无需手动处理验证码。

## 使用方法

### 1. 配置环境的Token自动获取

在环境管理页面中：
1. 编辑环境配置
2. 启用"自动刷新Token"
3. 配置Token获取URL、请求方式等基本信息
4. 在"请求体"字段中使用占位符

### 2. 在请求体中使用占位符

在环境配置的Token获取请求体中，可以使用以下占位符：

```json
{
  "code": "${code}",
  "codeKey": "${codekey}",
  "password": "2C6i4jZhS7U97Jj1uB4Biw==",
  "username": "tanxi",
  "grantType": "password"
}
```

### 2. 支持的占位符格式

系统支持多种占位符格式：

- `${code}` - 验证码内容
- `${codekey}` - 验证码Key（小写）
- `${codeKey}` - 验证码Key（驼峰命名）
- `${CODE}` - 验证码内容（大写）
- `${CODEKEY}` - 验证码Key（大写）
- `{code}` - 验证码内容（不带$符号）
- `{codekey}` - 验证码Key（不带$符号）
- `{codeKey}` - 验证码Key（不带$符号，驼峰命名）

### 3. 配置验证码参数

在环境配置中启用验证码功能：
1. 勾选"启用验证码"
2. 配置验证码获取URL
3. 设置请求方式（GET/POST/PUT/DELETE）
4. 配置请求头和请求体（如需要）
5. 设置图片字段路径和Key字段路径

### 4. 自动替换流程

1. 用户在环境配置的Token请求体中使用占位符
2. 启用验证码功能并配置相关参数
3. 当需要刷新Token时，系统自动：
   - 调用验证码API获取图片和Key
   - 使用ddddocr识别验证码
   - 替换请求体中的占位符
   - 使用替换后的请求体调用Token获取API
   - 更新环境中的Token

## 配置示例

### 示例1：简单GET请求
```
验证码地址: http://example.com/api/captcha
请求方式: GET
图片字段路径: content.imageBase64
Key字段路径: content.codeKey
```

### 示例2：POST请求带参数
```
验证码地址: http://example.com/api/captcha
请求方式: POST
请求头: {"Content-Type": "application/json"}
请求体: {"type": "login"}
图片字段路径: data.image
Key字段路径: data.key
```

## 替换效果示例

### 替换前：
```json
{
  "code": "${code}",
  "codeKey": "${codekey}",
  "password": "2C6i4jZhS7U97Jj1uB4Biw==",
  "username": "tanxi",
  "grantType": "password"
}
```

### 替换后：
```json
{
  "code": "1234",
  "codeKey": "f2225d3728304665be02ed874c7370ad",
  "password": "2C6i4jZhS7U97Jj1uB4Biw==",
  "username": "tanxi",
  "grantType": "password"
}
```

## 技术实现

### 后端实现
- 在 `app/services/token_service.py` 中实现占位符替换逻辑
- 在 `app/api/v1/api_requests/api_request.py` 中提供验证码获取API
- 支持多种占位符格式的识别和替换

### 前端实现
- 在API请求界面添加"获取验证码"按钮
- 提供验证码配置对话框
- 自动替换请求体中的占位符并更新显示

## 注意事项

1. **占位符格式**：确保占位符格式正确，系统会严格匹配
2. **JSON格式**：请求头和请求体必须是有效的JSON格式
3. **网络延迟**：验证码获取和识别需要时间，请耐心等待
4. **识别准确率**：ddddocr对简单验证码识别率较高，复杂验证码可能失败
5. **重试机制**：系统会自动重试3次以提高成功率

## 错误处理

- **验证码获取失败**：检查验证码URL和网络连接
- **识别失败**：可能是验证码过于复杂，建议重试
- **占位符未替换**：检查占位符格式是否正确
- **JSON格式错误**：检查请求头和请求体的JSON格式

## 扩展功能

未来可以考虑添加：
- 支持更多占位符变量
- 手动输入验证码功能
- 验证码识别历史记录
- 自定义占位符格式
