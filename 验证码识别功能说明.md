# 验证码识别功能实现说明

## 功能概述

在环境管理模块中，当测试验证码地址时，系统会自动获取验证码图片并使用ddddocr库进行识别，然后在界面上显示识别结果。

## 实现的功能

### 1. 后端API增强

#### 修改的文件：
- `app/api/v1/environment/captcha.py` - 添加了验证码识别功能

#### 新增功能：
- 在测试验证码地址时自动调用ddddocr进行验证码识别
- 返回识别结果和识别状态信息

#### API响应格式：
```json
{
  "code": 200,
  "data": {
    "imageBase64": "data:image/png;base64,iVBORw0KGgo...",
    "codeKey": "f2225d3728304665be02ed874c7370ad",
    "recognizedCode": "1234",
    "recognitionMessage": "验证码识别成功: 1234",
    "status": "success",
    "message": "验证码获取成功"
  },
  "msg": "验证码地址测试成功"
}
```

### 2. 前端界面增强

#### 修改的文件：
- `web/src/views/project/env_config/index.vue` - 环境配置界面

#### 新增功能：
- 在验证码测试成功后显示识别结果
- 根据识别成功/失败显示不同的消息和样式
- 在验证码预览区域显示识别出的验证码内容

#### 界面效果：
1. **识别成功时**：
   - 显示绿色背景的识别结果框
   - 大字体显示识别出的验证码
   - 显示"✓ 识别成功"标识

2. **识别失败时**：
   - 显示橙色背景的错误信息框
   - 显示具体的失败原因

## 使用方法

### 1. 配置验证码参数
在环境管理页面中：
1. 启用验证码功能
2. 配置验证码获取URL
3. 设置请求方式（GET/POST/PUT/DELETE）
4. 配置请求头和请求体（如需要）
5. 设置JSON路径用于提取图片和Key字段

### 2. 测试验证码识别
1. 点击"测试验证码地址"按钮
2. 系统会自动：
   - 调用验证码API获取图片
   - 使用ddddocr识别验证码
   - 在界面上显示识别结果

### 3. 查看识别结果
- 如果识别成功，会在消息提示中显示：`验证码地址测试成功，识别结果: 1234`
- 在验证码预览区域会显示：
  - 验证码图片
  - codeKey值
  - 识别结果（绿色背景框显示）

## 技术实现细节

### 1. 验证码识别流程
```
1. 用户点击测试按钮
2. 前端调用 /environment/captcha/test API
3. 后端调用验证码API获取图片和Key
4. 后端使用ddddocr识别验证码
5. 返回识别结果给前端
6. 前端显示识别结果
```

### 2. 错误处理
- 验证码API调用失败：显示网络错误信息
- 图片解析失败：显示解析错误信息
- ddddocr识别失败：显示识别失败信息
- 最多重试3次，提高识别成功率

### 3. 依赖库
- `ddddocr`：用于验证码识别
- `httpx`：用于HTTP请求
- `jsonpath_ng`：用于JSON路径解析

## 配置示例

### 常见验证码API配置：

#### 示例1：GET请求
```
验证码地址: http://example.com/api/captcha
请求方式: GET
图片字段路径: data.image
Key字段路径: data.key
```

#### 示例2：POST请求
```
验证码地址: http://example.com/api/captcha
请求方式: POST
请求头: {"Content-Type": "application/json"}
请求体: {"type": "captcha"}
图片字段路径: content.imageBase64
Key字段路径: content.codeKey
```

## 注意事项

1. **识别准确率**：ddddocr对简单的数字和字母验证码识别率较高，复杂的图形验证码可能识别失败
2. **网络延迟**：验证码识别需要网络请求，可能需要几秒钟时间
3. **重试机制**：系统会自动重试3次以提高成功率
4. **安全性**：验证码识别仅用于测试，实际使用时请确保符合相关服务的使用条款

## 后续扩展

可以考虑添加以下功能：
1. 支持更多验证码识别库
2. 添加验证码识别历史记录
3. 支持手动输入验证码进行对比
4. 添加识别准确率统计
