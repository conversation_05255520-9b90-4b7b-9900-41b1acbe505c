# Vue-FastAPI 测试平台技术架构文档

## 1. 系统概述

### 1.1 项目简介
Vue-FastAPI 测试平台是一个基于现代化技术栈的全栈测试管理系统，采用前后端分离架构，融合了 RBAC 权限管理、动态路由和 JWT 鉴权机制。系统专注于API测试、功能测试、测试计划管理，并集成了AI智能测试用例生成功能。

### 1.2 核心特性
- **现代化技术栈**：Python 3.11 + FastAPI + Vue3 + Naive UI
- **前后端分离**：RESTful API 设计，支持多端接入
- **权限管理**：基于 RBAC 的细粒度权限控制
- **智能测试**：集成 LangChain 的 AI 测试用例生成
- **测试管理**：完整的测试用例、测试计划、执行报告管理
- **环境配置**：多环境配置管理，支持环境切换
- **实时监控**：API 执行历史、审计日志记录

## 2. 技术栈架构

### 2.1 后端技术栈
```
Python 3.11
├── Web框架: FastAPI 0.111.0
├── ORM框架: Tortoise-ORM 0.20.1
├── 数据验证: Pydantic 2.9.0
├── 身份认证: PyJWT 2.9.0
├── 密码加密: Passlib + Argon2
├── 异步服务器: Uvicorn 0.30.6
├── 数据库迁移: Aerich 0.7.2
├── 日志管理: Loguru 0.7.2
├── 任务调度: APScheduler 3.10.4
├── AI集成: LangChain (通过自定义模块)
├── 数据处理: Pandas 2.0.3
├── Excel处理: OpenPyXL 3.1.2
├── 中文处理: PyPinyin 0.51.0
└── JSON处理: JSONPath-ng 1.6.1
```

### 2.2 前端技术栈
```
Vue 3.5.17
├── 构建工具: Vite 4.5.14
├── UI框架: Naive UI 2.42.0
├── 状态管理: Pinia 2.3.1
├── 路由管理: Vue Router 4.5.1
├── HTTP客户端: Axios 1.10.0
├── 工具库: Lodash-es 4.17.21
├── 日期处理: Day.js 1.11.13
├── 图标库: @iconify/vue 4.3.0
├── CSS框架: UnoCSS 0.55.7
├── 国际化: Vue-i18n 9.14.4
├── 组合式API: @vueuse/core 10.11.1
└── 类型支持: TypeScript 5.8.3
```

### 2.3 数据库
- **主数据库**: MySQL (生产环境)
- **开发数据库**: SQLite (开发测试)
- **连接池**: Tortoise-ORM 内置连接池管理

### 2.4 开发工具
```
代码质量
├── 代码格式化: Black 23.12.1
├── 导入排序: isort 5.13.2
├── 代码检查: Ruff 0.0.281
├── 前端检查: ESLint 8.57.1
└── 包管理: Poetry (后端) + pnpm (前端)
```

## 3. 系统架构设计

### 3.1 整体架构图
```mermaid
graph TB
    subgraph "前端层 Frontend"
        A[Vue3 + Naive UI]
        B[Pinia 状态管理]
        C[Vue Router 路由]
        D[Axios HTTP客户端]
    end
    
    subgraph "网关层 Gateway"
        E[Nginx 反向代理]
        F[CORS 跨域处理]
        G[静态资源服务]
    end
    
    subgraph "应用层 Application"
        H[FastAPI 应用服务]
        I[JWT 身份认证]
        J[RBAC 权限控制]
        K[API 路由管理]
    end
    
    subgraph "业务层 Business"
        L[项目管理模块]
        M[测试用例模块]
        N[测试计划模块]
        O[环境配置模块]
        P[AI测试生成模块]
        Q[报告统计模块]
    end
    
    subgraph "数据层 Data"
        R[Tortoise ORM]
        S[MySQL 数据库]
        T[Redis 缓存]
        U[文件存储]
    end
    
    subgraph "外部服务 External"
        V[AI大模型服务]
        W[钉钉通知服务]
        X[邮件服务]
    end
    
    A --> E
    B --> A
    C --> A
    D --> A
    E --> H
    F --> E
    G --> E
    H --> I
    H --> J
    H --> K
    K --> L
    K --> M
    K --> N
    K --> O
    K --> P
    K --> Q
    L --> R
    M --> R
    N --> R
    O --> R
    P --> R
    Q --> R
    R --> S
    R --> T
    P --> V
    Q --> W
    H --> X
```

### 3.2 核心模块架构
```mermaid
graph LR
    subgraph "核心业务模块"
        A[项目管理] --> B[模块管理]
        B --> C[接口管理]
        C --> D[测试用例]
        D --> E[测试计划]
        E --> F[测试执行]
        F --> G[测试报告]
    end
    
    subgraph "支撑模块"
        H[用户管理]
        I[权限管理]
        J[环境配置]
        K[数据生成]
        L[AI助手]
    end
    
    subgraph "系统模块"
        M[审计日志]
        N[定时任务]
        O[通知服务]
        P[文件管理]
    end
    
    A -.-> H
    D -.-> I
    E -.-> J
    D -.-> K
    D -.-> L
    F -.-> M
    E -.-> N
    G -.-> O
    G -.-> P
```

## 4. 核心功能模块

### 4.1 项目管理模块
- **功能**: 项目创建、编辑、删除、状态管理
- **特性**: 层级模块管理、项目成员管理、项目统计
- **技术**: FastAPI + Tortoise ORM + Vue3

### 4.2 测试用例管理
- **API测试用例**: 支持 RESTful API 测试，包含请求参数、断言配置
- **功能测试用例**: 传统功能测试用例管理，包含测试步骤、预期结果
- **用例导入导出**: 支持 Excel 批量导入导出
- **智能生成**: 基于 AI 的测试用例自动生成

### 4.3 测试计划管理
- **计划创建**: 支持 API 和功能测试计划
- **批量执行**: 支持测试用例批量执行
- **报告生成**: 自动生成测试报告，支持 Allure 格式
- **定时任务**: 支持定时执行测试计划
- **通知集成**: 支持钉钉群通知测试结果

### 4.4 环境配置管理
- **多环境支持**: 开发、测试、预发布、生产环境
- **配置管理**: 环境地址、端口、认证信息管理
- **环境切换**: 测试执行时动态切换环境

### 4.5 AI 智能测试
- **模型配置**: 支持多种 AI 大模型配置 (OpenAI、Claude、通义千问等)
- **提示词管理**: 可配置的提示词模板
- **智能生成**: 基于需求描述自动生成测试用例
- **结果优化**: 支持生成结果的编辑和优化

## 5. 数据库设计

### 5.1 核心表结构
```sql
-- 用户表
users (id, username, email, password, is_active, is_superuser, roles)

-- 项目表  
projects (id, name, description, status, start_date, end_date, manager, budget)

-- 项目模块表
project_modules (id, name, description, project_id, parent_id, order, status)

-- API测试用例表
api_test_cases (id, case_number, case_name, method, url, params, body, expected_result)

-- 功能测试用例表
test_cases (id, case_number, case_name, case_level, precondition, test_steps, expected_result)

-- 测试计划表
api_test_plans (id, plan_name, project_id, status, environment_id, pass_rate)

-- 环境配置表
environments (id, name, project_id, env_type, host, port, token)

-- AI模型配置表
ai_model_configs (id, name, model_type, api_key, base_url, model_name, is_enabled)
```

### 5.2 权限设计
```sql
-- 角色表
roles (id, name, desc)

-- 菜单表
menus (id, name, path, component, menu_type, parent_id, order, icon)

-- API表
apis (id, path, method, summary, tags)

-- 角色菜单关联表
role_menus (role_id, menu_id)

-- 角色API关联表  
role_apis (role_id, api_id)

-- 用户角色关联表
user_roles (user_id, role_id)
```

## 6. 安全机制

### 6.1 身份认证
- **JWT Token**: 基于 JSON Web Token 的无状态认证
- **Token 刷新**: 支持 Access Token 和 Refresh Token 机制
- **密码加密**: 使用 Argon2 算法加密存储密码

### 6.2 权限控制
- **RBAC 模型**: 基于角色的访问控制
- **菜单权限**: 动态菜单生成，基于用户角色
- **API 权限**: 接口级别的权限控制
- **按钮权限**: 前端按钮级别的权限控制

### 6.3 数据安全
- **SQL 注入防护**: ORM 框架自动防护
- **XSS 防护**: 前端输入验证和转义
- **CORS 配置**: 跨域请求安全配置
- **审计日志**: 完整的操作日志记录

## 7. 性能优化

### 7.1 后端优化
- **异步处理**: FastAPI 异步框架，支持高并发
- **连接池**: 数据库连接池管理
- **缓存机制**: Redis 缓存热点数据
- **分页查询**: 大数据量分页处理

### 7.2 前端优化
- **组件懒加载**: 路由级别的代码分割
- **虚拟滚动**: 大列表性能优化
- **状态管理**: Pinia 轻量级状态管理
- **构建优化**: Vite 快速构建和热更新

## 8. 部署架构

### 8.1 开发环境
```
开发环境
├── 后端: uvicorn --reload
├── 前端: vite dev server
├── 数据库: SQLite
└── 依赖: 虚拟环境 venv
```

### 8.2 生产环境
```
生产环境
├── 负载均衡: Nginx
├── 应用服务: Gunicorn + Uvicorn Workers
├── 数据库: MySQL 主从复制
├── 缓存: Redis Cluster
├── 监控: Prometheus + Grafana
└── 容器化: Docker + Docker Compose
```

### 8.3 CI/CD 流程
```mermaid
graph LR
    A[代码提交] --> B[代码检查]
    B --> C[单元测试]
    C --> D[构建镜像]
    D --> E[部署测试环境]
    E --> F[自动化测试]
    F --> G[部署生产环境]
    G --> H[健康检查]
```

## 9. 监控与运维

### 9.1 应用监控
- **性能监控**: 接口响应时间、吞吐量监控
- **错误监控**: 异常日志收集和告警
- **业务监控**: 测试执行成功率、用户活跃度

### 9.2 基础设施监控
- **服务器监控**: CPU、内存、磁盘使用率
- **数据库监控**: 连接数、查询性能、慢查询
- **网络监控**: 带宽使用、延迟监控

## 10. 扩展性设计

### 10.1 水平扩展
- **无状态设计**: 应用服务无状态，支持水平扩展
- **数据库分片**: 支持数据库读写分离和分片
- **缓存集群**: Redis 集群支持

### 10.2 功能扩展
- **插件机制**: 支持第三方插件扩展
- **API 开放**: 提供开放 API 供第三方集成
- **多租户**: 支持多租户架构扩展

## 11. 技术选型理由

### 11.1 后端选型
- **FastAPI**: 高性能、自动文档生成、类型提示支持
- **Tortoise ORM**: 异步 ORM，性能优秀，Django-like API
- **Pydantic**: 数据验证和序列化，与 FastAPI 完美集成

### 11.2 前端选型  
- **Vue3**: 组合式 API，更好的 TypeScript 支持
- **Naive UI**: 现代化设计，组件丰富，TypeScript 友好
- **Pinia**: 轻量级状态管理，更好的 DevTools 支持

## 12. 未来规划

### 12.1 短期规划
- 完善 AI 测试用例生成功能
- 增加更多测试报告模板
- 优化测试执行性能

### 12.2 长期规划
- 支持移动端测试
- 集成更多第三方工具
- 构建测试生态系统

---

**文档版本**: v1.0  
**更新时间**: 2025-01-23  
**维护团队**: 测试平台开发团队
