from tortoise import BaseD<PERSON>syncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `projects` ADD `item_type` VARCHAR(20) NOT NULL  COMMENT '类型：project/module' DEFAULT 'project';
        ALTER TABLE `projects` ADD `order` INT NOT NULL  COMMENT '排序' DEFAULT 0;
        ALTER TABLE `projects` ADD `parent_id` INT NOT NULL  COMMENT '父项目ID' DEFAULT 0;
        ALTER TABLE `projects` DROP COLUMN `start_date`;
        ALTER TABLE `projects` DROP COLUMN `end_date`;
        ALTER TABLE `projects` ALTER COLUMN `status` SET DEFAULT 'active';
        ALTER TABLE `projects` MODIFY COLUMN `status` VARCHAR(20) NOT NULL  COMMENT '状态' DEFAULT 'active';
        ALTER TABLE `projects` MODIFY COLUMN `description` LONGTEXT   COMMENT '项目/模块描述';
        ALTER TABLE `projects` MODIFY COLUMN `name` VARCHAR(100) NOT NULL  COMMENT '项目/模块名称';
        ALTER TABLE `projects` ADD INDEX `idx_projects_order_966b86` (`order`);
        ALTER TABLE `projects` ADD INDEX `idx_projects_parent__11886e` (`parent_id`);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `projects` DROP INDEX `idx_projects_parent__11886e`;
        ALTER TABLE `projects` DROP INDEX `idx_projects_order_966b86`;
        ALTER TABLE `projects` ADD `start_date` DATETIME(6)   COMMENT '开始日期';
        ALTER TABLE `projects` ADD `end_date` DATETIME(6)   COMMENT '结束日期';
        ALTER TABLE `projects` DROP COLUMN `item_type`;
        ALTER TABLE `projects` DROP COLUMN `order`;
        ALTER TABLE `projects` DROP COLUMN `parent_id`;
        ALTER TABLE `projects` ALTER COLUMN `status` DROP DEFAULT;
        ALTER TABLE `projects` MODIFY COLUMN `status` VARCHAR(20) NOT NULL  COMMENT '项目状态';
        ALTER TABLE `projects` MODIFY COLUMN `description` LONGTEXT   COMMENT '项目描述';
        ALTER TABLE `projects` MODIFY COLUMN `name` VARCHAR(100) NOT NULL  COMMENT '项目名称';"""
