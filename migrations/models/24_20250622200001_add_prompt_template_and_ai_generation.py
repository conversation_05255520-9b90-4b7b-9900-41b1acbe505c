from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `prompt_templates` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL  COMMENT '模板名称',
    `category` VARCHAR(50) NOT NULL  COMMENT '模板分类',
    `description` LONGTEXT   COMMENT '模板描述',
    `prompt_content` LONGTEXT NOT NULL  COMMENT '提示词内容',
    `variables` JSON   COMMENT '变量定义(JSON格式)',
    `is_active` BOOL NOT NULL  COMMENT '是否启用' DEFAULT 1,
    `is_default` BOOL NOT NULL  COMMENT '是否默认模板' DEFAULT 0,
    `usage_count` INT NOT NULL  COMMENT '使用次数' DEFAULT 0,
    `user_id` INT NOT NULL  COMMENT '创建用户ID',
    KEY `idx_prompt_temp_created_4ba46b` (`created_at`),
    KEY `idx_prompt_temp_updated_c9d346` (`updated_at`),
    KEY `idx_prompt_temp_name_3f0aae` (`name`),
    KEY `idx_prompt_temp_category_ca70fd` (`category`),
    KEY `idx_prompt_temp_is_active_64adfe` (`is_active`),
    KEY `idx_prompt_temp_is_default_f5ff3d` (`is_default`),
    KEY `idx_prompt_temp_user_id_47150c` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='提示词模板表';
        CREATE TABLE IF NOT EXISTS `ai_test_case_generations` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `task_name` VARCHAR(200) NOT NULL  COMMENT '任务名称',
    `requirement_description` LONGTEXT NOT NULL  COMMENT '需求描述',
    `prompt_template_id` INT   COMMENT '使用的提示词模板ID',
    `ai_model_config_id` INT NOT NULL  COMMENT '使用的AI模型配置ID',
    `project_id` INT NOT NULL  COMMENT '所属项目ID',
    `module_id` INT   COMMENT '所属模块ID',
    `generated_count` INT NOT NULL  COMMENT '生成的测试用例数量' DEFAULT 0,
    `status` VARCHAR(20) NOT NULL  COMMENT '生成状态：pending-待生成，generating-生成中，completed-已完成，failed-失败' DEFAULT 'pending',
    `error_message` LONGTEXT   COMMENT '错误信息',
    `generation_time` INT   COMMENT '生成耗时(秒)',
    `user_id` INT NOT NULL  COMMENT '创建用户ID',
    KEY `idx_ai_test_case_created_4ba46b` (`created_at`),
    KEY `idx_ai_test_case_updated_c9d346` (`updated_at`),
    KEY `idx_ai_test_case_task_name_3f0aae` (`task_name`),
    KEY `idx_ai_test_case_prompt_template_id_ca70fd` (`prompt_template_id`),
    KEY `idx_ai_test_case_ai_model_config_id_64adfe` (`ai_model_config_id`),
    KEY `idx_ai_test_case_project_id_f5ff3d` (`project_id`),
    KEY `idx_ai_test_case_module_id_47150c` (`module_id`),
    KEY `idx_ai_test_case_status_abc123` (`status`),
    KEY `idx_ai_test_case_user_id_def456` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='AI测试用例生成记录表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `ai_test_case_generations`;
        DROP TABLE IF EXISTS `prompt_templates`;"""
