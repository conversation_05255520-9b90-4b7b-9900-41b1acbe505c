from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api` MODIFY COLUMN `method` VARCHAR(7) NOT NULL  COMMENT '请求方法';
        ALTER TABLE `api_import` MODIFY COLUMN `method` VARCHAR(7) NOT NULL  COMMENT '请求方式';
        ALTER TABLE `api_request` MODIFY COLUMN `method` VARCHAR(7) NOT NULL  COMMENT '请求方法';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api` MODIFY COLUMN `method` VARCHAR(6) NOT NULL  COMMENT '请求方法';
        ALTER TABLE `api_import` MODIFY COLUMN `method` VARCHAR(6) NOT NULL  COMMENT '请求方式';
        ALTER TABLE `api_request` MODIFY COLUMN `method` VARCHAR(6) NOT NULL  COMMENT '请求方法';"""
