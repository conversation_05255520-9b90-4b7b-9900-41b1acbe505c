from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `environments` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL  COMMENT '环境名称',
    `env_type` VARCHAR(11) NOT NULL  COMMENT '环境类型',
    `host` VARCHAR(255) NOT NULL  COMMENT '主机地址',
    `port` INT NOT NULL  COMMENT '端口号' DEFAULT 80,
    `token` LONGTEXT   COMMENT '访问令牌',
    `description` LONGTEXT   COMMENT '环境描述',
    `project_id` INT NOT NULL  COMMENT '所属项目ID',
    `is_active` BOOL NOT NULL  COMMENT '是否启用' DEFAULT 1,
    KEY `idx_environment_created_395723` (`created_at`),
    KEY `idx_environment_updated_0164e1` (`updated_at`),
    KEY `idx_environment_name_d35794` (`name`),
    KEY `idx_environment_env_typ_bdbb9c` (`env_type`),
    KEY `idx_environment_host_ffa44b` (`host`),
    KEY `idx_environment_project_8f00f8` (`project_id`),
    KEY `idx_environment_is_acti_49034c` (`is_active`)
) CHARACTER SET utf8mb4 COMMENT='环境配置表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `environments`;"""
