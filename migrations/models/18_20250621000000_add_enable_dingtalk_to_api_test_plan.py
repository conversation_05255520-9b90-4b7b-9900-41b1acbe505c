from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api_test_plans` 
        ADD COLUMN `enable_dingtalk` BOOL NOT NULL DEFAULT 0 COMMENT '是否启用钉钉通知';
        
        ALTER TABLE `api_test_plans` 
        ADD KEY `idx_api_test_pl_enable__dingtalk` (`enable_dingtalk`);
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api_test_plans` 
        DROP COLUMN `enable_dingtalk`;
    """
