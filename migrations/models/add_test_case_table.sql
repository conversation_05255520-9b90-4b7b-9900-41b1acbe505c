-- 创建功能测试用例表
CREATE TABLE IF NOT EXISTS `test_cases` (
    `id` int NOT NULL AUTO_INCREMENT,
    `case_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用例编号',
    `case_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用例名称',
    `case_level` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'medium' COMMENT '用例等级：low-低，medium-中，high-高',
    `precondition` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '前置条件',
    `test_steps` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用例步骤',
    `expected_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '预期结果',
    `is_smoke` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否冒烟用例',
    `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待审核，approved-已审核',
    `source` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'manual' COMMENT '来源：manual-人工，ai-AI',
    `project_id` int NOT NULL COMMENT '所属项目ID',
    `module_id` int DEFAULT NULL COMMENT '所属模块ID',
    `user_id` int DEFAULT NULL COMMENT '创建用户ID',
    `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `case_number` (`case_number`),
    KEY `idx_test_cases_case_name` (`case_name`),
    KEY `idx_test_cases_case_level` (`case_level`),
    KEY `idx_test_cases_is_smoke` (`is_smoke`),
    KEY `idx_test_cases_status` (`status`),
    KEY `idx_test_cases_source` (`source`),
    KEY `idx_test_cases_project_id` (`project_id`),
    KEY `idx_test_cases_module_id` (`module_id`),
    KEY `idx_test_cases_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功能测试用例表';
