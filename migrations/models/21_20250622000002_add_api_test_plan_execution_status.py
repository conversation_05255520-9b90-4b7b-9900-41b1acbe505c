from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api_test_plan_cases` ADD COLUMN `execution_status` VARCHAR(20) NOT NULL DEFAULT 'normal' COMMENT '执行状态：normal-正常，skipped-跳过';
        ALTER TABLE `api_test_plan_cases` ADD INDEX `idx_api_test_plan_cases_execution_status` (`execution_status`);
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api_test_plan_cases` DROP INDEX `idx_api_test_plan_cases_execution_status`;
        ALTER TABLE `api_test_plan_cases` DROP COLUMN `execution_status`;
    """
