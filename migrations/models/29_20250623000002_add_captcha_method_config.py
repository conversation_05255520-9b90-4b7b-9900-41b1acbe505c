from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `environments` ADD COLUMN `captcha_method` VARCHAR(10) NOT NULL DEFAULT 'GET' COMMENT '验证码获取请求方式';
        ALTER TABLE `environments` ADD COLUMN `captcha_headers` JSON NULL COMMENT '验证码获取请求头';
        ALTER TABLE `environments` ADD COLUMN `captcha_body` JSON NULL COMMENT '验证码获取请求体';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `environments` DROP COLUMN `captcha_method`;
        ALTER TABLE `environments` DROP COLUMN `captcha_headers`;
        ALTER TABLE `environments` DROP COLUMN `captcha_body`;"""
