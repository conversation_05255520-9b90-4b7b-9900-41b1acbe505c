from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `ai_model_configs` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL  COMMENT '模型名称',
    `model_type` VARCHAR(8) NOT NULL  COMMENT '模型类型',
    `api_key` VARCHAR(500) NOT NULL  COMMENT 'API密钥',
    `api_url` VARCHAR(500)   COMMENT 'API地址',
    `model_name` VARCHAR(100)   COMMENT '具体模型名称',
    `max_tokens` INT NOT NULL  COMMENT '最大token数' DEFAULT 4096,
    `temperature` DOUBLE NOT NULL  COMMENT '温度参数' DEFAULT 0.7,
    `timeout` INT NOT NULL  COMMENT '超时时间(秒)' DEFAULT 30,
    `status` VARCHAR(8) NOT NULL  COMMENT '状态' DEFAULT 'inactive',
    `is_default` BOOL NOT NULL  COMMENT '是否默认模型' DEFAULT 0,
    `description` LONGTEXT   COMMENT '模型描述',
    `config_json` JSON   COMMENT '额外配置参数(JSON格式)',
    `user_id` INT NOT NULL  COMMENT '创建用户ID',
    KEY `idx_ai_model_co_created_4ba46b` (`created_at`),
    KEY `idx_ai_model_co_updated_c9d346` (`updated_at`),
    KEY `idx_ai_model_co_name_3f0aae` (`name`),
    KEY `idx_ai_model_co_model_t_ca70fd` (`model_type`),
    KEY `idx_ai_model_co_status_64adfe` (`status`),
    KEY `idx_ai_model_co_is_defa_f5ff3d` (`is_default`),
    KEY `idx_ai_model_co_user_id_47150c` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='AI大模型配置表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `ai_model_configs`;"""
