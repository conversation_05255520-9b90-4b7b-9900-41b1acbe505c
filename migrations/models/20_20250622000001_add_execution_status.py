from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `functional_test_plan_cases` ADD COLUMN `execution_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '执行状态：pending-待重试，passed-通过，failed-失败，blocked-阻塞，skipped-跳过';
        ALTER TABLE `functional_test_plan_cases` ADD INDEX `idx_functional_test_plan_cases_execution_status` (`execution_status`);
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `functional_test_plan_cases` DROP INDEX `idx_functional_test_plan_cases_execution_status`;
        ALTER TABLE `functional_test_plan_cases` DROP COLUMN `execution_status`;
    """
