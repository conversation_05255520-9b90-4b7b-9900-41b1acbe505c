from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `api_test_cases` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `case_number` VARCHAR(50) NOT NULL UNIQUE COMMENT '用例编号',
    `case_name` VARCHAR(200) NOT NULL  COMMENT '用例名称',
    `method` VARCHAR(7) NOT NULL  COMMENT '请求方式',
    `url` VARCHAR(500) NOT NULL  COMMENT '请求URL',
    `params` JSON   COMMENT '请求参数',
    `body` LONGTEXT   COMMENT '请求体',
    `expected_result` LONGTEXT   COMMENT '预期结果',
    `is_smoke` BOOL NOT NULL  COMMENT '是否冒烟用例' DEFAULT 0,
    `status` VARCHAR(20) NOT NULL  COMMENT '状态：pending-待审核，approved-已审核' DEFAULT 'pending',
    `source` VARCHAR(20) NOT NULL  COMMENT '来源：manual-人工，ai-AI' DEFAULT 'manual',
    `project_id` INT NOT NULL  COMMENT '所属项目ID',
    `module_id` INT   COMMENT '所属模块ID',
    `user_id` INT   COMMENT '创建用户ID',
    KEY `idx_api_test_ca_created_16492a` (`created_at`),
    KEY `idx_api_test_ca_updated_6089c9` (`updated_at`),
    KEY `idx_api_test_ca_case_nu_c3002d` (`case_number`),
    KEY `idx_api_test_ca_case_na_e603e4` (`case_name`),
    KEY `idx_api_test_ca_method_823cbe` (`method`),
    KEY `idx_api_test_ca_is_smok_366138` (`is_smoke`),
    KEY `idx_api_test_ca_status_21de97` (`status`),
    KEY `idx_api_test_ca_source_1b953b` (`source`),
    KEY `idx_api_test_ca_project_26f45a` (`project_id`),
    KEY `idx_api_test_ca_module__683bc8` (`module_id`),
    KEY `idx_api_test_ca_user_id_2326a5` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='接口测试用例表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `api_test_cases`;"""
