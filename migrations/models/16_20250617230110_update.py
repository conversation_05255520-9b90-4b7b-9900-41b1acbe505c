from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api_test_cases` MODIFY COLUMN `expected_result` LONGTEXT   COMMENT '断言配置（JSON格式）';
        CREATE TABLE IF NOT EXISTS `api_test_plans` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `plan_name` VARCHAR(200) NOT NULL  COMMENT '计划名称',
    `level` VARCHAR(10) NOT NULL  COMMENT '等级：low-低，medium-中，high-高' DEFAULT 'medium',
    `status` VARCHAR(20) NOT NULL  COMMENT '状态：not_started-未开始，in_progress-进行中，completed-已完成' DEFAULT 'not_started',
    `description` LONGTEXT   COMMENT '计划描述',
    `project_id` INT NOT NULL  COMMENT '所属项目ID',
    `environment_id` INT   COMMENT '运行环境ID',
    `execution_result` VARCHAR(20)   COMMENT '执行结果：success-成功，failed-失败，running-执行中',
    `pass_rate` DOUBLE   COMMENT '通过率',
    `last_execution_time` DATETIME(6)   COMMENT '最近执行时间',
    `user_id` INT NOT NULL  COMMENT '创建用户ID',
    KEY `idx_api_test_pl_created_27c6bc` (`created_at`),
    KEY `idx_api_test_pl_updated_0425ee` (`updated_at`),
    KEY `idx_api_test_pl_plan_na_6628ba` (`plan_name`),
    KEY `idx_api_test_pl_level_73dd05` (`level`),
    KEY `idx_api_test_pl_status_9108e5` (`status`),
    KEY `idx_api_test_pl_project_a05296` (`project_id`),
    KEY `idx_api_test_pl_environ_5e8bc1` (`environment_id`),
    KEY `idx_api_test_pl_executi_1bac5a` (`execution_result`),
    KEY `idx_api_test_pl_last_ex_986954` (`last_execution_time`),
    KEY `idx_api_test_pl_user_id_7df334` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='接口测试计划表';
        CREATE TABLE IF NOT EXISTS `api_test_plan_cases` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `plan_id` INT NOT NULL  COMMENT '测试计划ID',
    `case_id` INT NOT NULL  COMMENT '测试用例ID',
    `execution_order` INT NOT NULL  COMMENT '执行顺序' DEFAULT 0,
    `execution_result` VARCHAR(20)   COMMENT '执行结果：success-成功，failed-失败',
    `execution_time` DATETIME(6)   COMMENT '执行时间',
    `response_time` INT   COMMENT '响应时间(毫秒)',
    `error_message` LONGTEXT   COMMENT '错误信息',
    UNIQUE KEY `uid_api_test_pl_plan_id_178dac` (`plan_id`, `case_id`),
    KEY `idx_api_test_pl_created_426f10` (`created_at`),
    KEY `idx_api_test_pl_updated_8fa40f` (`updated_at`),
    KEY `idx_api_test_pl_plan_id_122471` (`plan_id`),
    KEY `idx_api_test_pl_case_id_fcfb16` (`case_id`),
    KEY `idx_api_test_pl_executi_48afe5` (`execution_result`)
) CHARACTER SET utf8mb4 COMMENT='测试计划用例关联表';
        CREATE TABLE IF NOT EXISTS `test_cases` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `case_number` VARCHAR(50) NOT NULL UNIQUE COMMENT '用例编号',
    `case_name` VARCHAR(200) NOT NULL  COMMENT '用例名称',
    `case_level` VARCHAR(10) NOT NULL  COMMENT '用例等级：low-低，medium-中，high-高' DEFAULT 'medium',
    `precondition` LONGTEXT   COMMENT '前置条件',
    `test_steps` LONGTEXT NOT NULL  COMMENT '用例步骤',
    `expected_result` LONGTEXT NOT NULL  COMMENT '预期结果',
    `is_smoke` BOOL NOT NULL  COMMENT '是否冒烟用例' DEFAULT 0,
    `status` VARCHAR(20) NOT NULL  COMMENT '状态：pending-待审核，approved-已审核' DEFAULT 'pending',
    `source` VARCHAR(20) NOT NULL  COMMENT '来源：manual-人工，ai-AI' DEFAULT 'manual',
    `project_id` INT NOT NULL  COMMENT '所属项目ID',
    `module_id` INT   COMMENT '所属模块ID',
    `user_id` INT   COMMENT '创建用户ID',
    KEY `idx_test_cases_created_c2f56b` (`created_at`),
    KEY `idx_test_cases_updated_44d4c2` (`updated_at`),
    KEY `idx_test_cases_case_nu_315e53` (`case_number`),
    KEY `idx_test_cases_case_na_a79fe1` (`case_name`),
    KEY `idx_test_cases_case_le_facdd3` (`case_level`),
    KEY `idx_test_cases_is_smok_7395dd` (`is_smoke`),
    KEY `idx_test_cases_status_90d111` (`status`),
    KEY `idx_test_cases_source_62b403` (`source`),
    KEY `idx_test_cases_project_5c9a88` (`project_id`),
    KEY `idx_test_cases_module__dc4165` (`module_id`),
    KEY `idx_test_cases_user_id_88ddd9` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='功能测试用例表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api_test_cases` MODIFY COLUMN `expected_result` LONGTEXT   COMMENT '预期结果';
        DROP TABLE IF EXISTS `api_test_plans`;
        DROP TABLE IF EXISTS `api_test_plan_cases`;
        DROP TABLE IF EXISTS `test_cases`;"""
