from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api_request` ADD COLUMN `module_id` INT NULL COMMENT '所属模块ID';
        ALTER TABLE `api_request` ADD INDEX `idx_api_request_module_id` (`module_id`);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api_request` DROP INDEX `idx_api_request_module_id`;
        ALTER TABLE `api_request` DROP COLUMN `module_id`;"""
