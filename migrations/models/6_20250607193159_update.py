from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `api_import` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `api_name` VARCHAR(100) NOT NULL  COMMENT '接口名称说明',
    `url_path` VARCHAR(500) NOT NULL  COMMENT '接口路径',
    `method` VARCHAR(6) NOT NULL  COMMENT '请求方式',
    `params_list` LONGTEXT   COMMENT '请求参数',
    `status` VARCHAR(20) NOT NULL  COMMENT '状态',
    `project_id` INT   COMMENT '所属项目ID',
    KEY `idx_api_import_created_7ae5fa` (`created_at`),
    KEY `idx_api_import_updated_fb0e29` (`updated_at`),
    KEY `idx_api_import_api_nam_ea8d9b` (`api_name`),
    KEY `idx_api_import_url_pat_bef211` (`url_path`),
    KEY `idx_api_import_method_c7cf80` (`method`),
    KEY `idx_api_import_project_916826` (`project_id`)
) CHARACTER SET utf8mb4 COMMENT='API导入';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `api_import`;"""
