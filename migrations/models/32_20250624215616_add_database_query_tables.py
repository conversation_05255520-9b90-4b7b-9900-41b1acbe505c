from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `database_connections` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL  COMMENT '连接名称',
    `db_type` VARCHAR(20) NOT NULL  COMMENT '数据库类型：mysql,postgresql,sqlite,oracle',
    `host` VARCHAR(255) NOT NULL  COMMENT '主机地址',
    `port` INT NOT NULL  COMMENT '端口号',
    `database` VARCHAR(100) NOT NULL  COMMENT '数据库名称',
    `username` VARCHA<PERSON>(100) NOT NULL  COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL  COMMENT '密码',
    `charset` VARCHAR(20) NOT NULL  COMMENT '字符集' DEFAULT 'utf8mb4',
    `description` LONGTEXT   COMMENT '连接描述',
    `is_active` BOOL NOT NULL  COMMENT '是否启用' DEFAULT 1,
    `max_connections` INT NOT NULL  COMMENT '最大连接数' DEFAULT 10,
    `connection_timeout` INT NOT NULL  COMMENT '连接超时时间(秒)' DEFAULT 30,
    `user_id` INT NOT NULL  COMMENT '创建用户ID',
    KEY `idx_database_co_created_50bca3` (`created_at`),
    KEY `idx_database_co_updated_47678d` (`updated_at`),
    KEY `idx_database_co_name_820b43` (`name`),
    KEY `idx_database_co_db_type_9e3f8c` (`db_type`),
    KEY `idx_database_co_is_acti_9ccda7` (`is_active`),
    KEY `idx_database_co_user_id_cb7667` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='数据库连接配置表';
        CREATE TABLE IF NOT EXISTS `query_history` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `query_name` VARCHAR(200)   COMMENT '查询名称',
    `sql_content` LONGTEXT NOT NULL  COMMENT 'SQL语句内容',
    `database_connection_id` INT NOT NULL  COMMENT '数据库连接ID',
    `execution_time` INT   COMMENT '执行时间(毫秒)',
    `affected_rows` INT   COMMENT '影响行数',
    `result_count` INT   COMMENT '结果行数',
    `status` VARCHAR(20) NOT NULL  COMMENT '执行状态：success-成功，failed-失败',
    `error_message` LONGTEXT   COMMENT '错误信息',
    `is_favorite` BOOL NOT NULL  COMMENT '是否收藏' DEFAULT 0,
    `user_id` INT NOT NULL  COMMENT '执行用户ID',
    KEY `idx_query_histo_created_00d967` (`created_at`),
    KEY `idx_query_histo_updated_9da728` (`updated_at`),
    KEY `idx_query_histo_query_n_cea764` (`query_name`),
    KEY `idx_query_histo_databas_22614b` (`database_connection_id`),
    KEY `idx_query_histo_status_36b925` (`status`),
    KEY `idx_query_histo_is_favo_714122` (`is_favorite`),
    KEY `idx_query_histo_user_id_58172d` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='SQL查询历史记录表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `database_connections`;
        DROP TABLE IF EXISTS `query_history`;"""
