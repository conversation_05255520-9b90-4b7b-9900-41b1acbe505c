from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `projects` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL  COMMENT '项目名称',
    `description` LONGTEXT   COMMENT '项目描述',
    `status` VARCHAR(20) NOT NULL  COMMENT '项目状态',
    `start_date` DATE NOT NULL  COMMENT '开始日期',
    `end_date` DATE   COMMENT '结束日期',
    `manager` VARCHAR(50)   COMMENT '项目经理',
    `budget` DOUBLE NOT NULL  COMMENT '项目预算' DEFAULT 0,
    KEY `idx_projects_created_f282c7` (`created_at`),
    KEY `idx_projects_updated_514ed4` (`updated_at`)
) CHARACTER SET utf8mb4 COMMENT='项目表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `projects`;"""
