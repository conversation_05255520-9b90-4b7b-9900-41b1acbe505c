from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `api_execution_history` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `user_id` INT NOT NULL  COMMENT '用户ID',
    `method` VARCHAR(7) NOT NULL  COMMENT '请求方法',
    `url` VARCHAR(500) NOT NULL  COMMENT '请求URL',
    `params` JSON   COMMENT '请求参数',
    `headers` JSON   COMMENT '请求头',
    `body` LONGTEXT   COMMENT '请求体',
    `status_code` INT   COMMENT '响应状态码',
    `response_time` INT   COMMENT '响应时间(毫秒)',
    `success` BOOL NOT NULL  COMMENT '是否成功' DEFAULT 1,
    KEY `idx_api_executi_created_6cd6df` (`created_at`),
    KEY `idx_api_executi_updated_a9ccbc` (`updated_at`),
    KEY `idx_api_executi_user_id_a6721a` (`user_id`),
    KEY `idx_api_executi_method_cd2f94` (`method`)
) CHARACTER SET utf8mb4 COMMENT='API执行历史记录表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `api_execution_history`;"""
