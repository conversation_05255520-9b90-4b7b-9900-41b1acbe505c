from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `api_request` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL  COMMENT '接口名称',
    `url` VARCHAR(500) NOT NULL  COMMENT '请求URL',
    `method` VARCHAR(6) NOT NULL  COMMENT '请求方法',
    `params` JSON   COMMENT '请求参数',
    `headers` JSON   COMMENT '请求头',
    `body` LONGTEXT   COMMENT '请求体',
    `description` LONGTEXT   COMMENT '接口描述',
    `user_id` INT   COMMENT '创建用户ID',
    `project_id` INT   COMMENT '所属项目ID',
    `category` VARCHAR(50)   COMMENT '接口分类',
    `is_favorite` BOOL NOT NULL  COMMENT '是否收藏' DEFAULT 0,
    `last_executed` DATETIME(6)   COMMENT '最后执行时间',
    `execution_count` INT NOT NULL  COMMENT '执行次数' DEFAULT 0,
    KEY `idx_api_request_created_d93ff8` (`created_at`),
    KEY `idx_api_request_updated_1d7eca` (`updated_at`),
    KEY `idx_api_request_name_45c1a1` (`name`),
    KEY `idx_api_request_url_7e6420` (`url`),
    KEY `idx_api_request_method_bfa1f5` (`method`),
    KEY `idx_api_request_user_id_5d578d` (`user_id`),
    KEY `idx_api_request_project_643f07` (`project_id`),
    KEY `idx_api_request_categor_93c3ea` (`category`),
    KEY `idx_api_request_is_favo_021029` (`is_favorite`),
    KEY `idx_api_request_last_ex_f6653a` (`last_executed`)
) CHARACTER SET utf8mb4 COMMENT='API请求记录表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `api_request`;"""
