from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `scheduled_task` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `task_name` VARCHAR(200) NOT NULL  COMMENT '任务名称',
    `plan_id` INT NOT NULL  COMMENT '测试计划ID',
    `cron_expression` VARCHAR(100) NOT NULL  COMMENT 'Cron表达式',
    `is_active` BOOL NOT NULL  COMMENT '是否启用' DEFAULT 1,
    `last_run_time` DATETIME(6)   COMMENT '最后执行时间',
    `next_run_time` DATETIME(6)   COMMENT '下次执行时间',
    `run_count` INT NOT NULL  COMMENT '执行次数' DEFAULT 0,
    `description` LONGTEXT   COMMENT '任务描述',
    `creator_id` INT NOT NULL  COMMENT '创建人ID',
    KEY `idx_scheduled_task_created_at` (`created_at`),
    KEY `idx_scheduled_task_updated_at` (`updated_at`),
    KEY `idx_scheduled_task_task_name` (`task_name`),
    KEY `idx_scheduled_task_plan_id` (`plan_id`),
    KEY `idx_scheduled_task_is_active` (`is_active`),
    KEY `idx_scheduled_task_creator_id` (`creator_id`)
) CHARACTER SET utf8mb4;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `scheduled_task`;"""
