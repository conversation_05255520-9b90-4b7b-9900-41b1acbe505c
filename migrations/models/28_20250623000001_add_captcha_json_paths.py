from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `environments` ADD COLUMN `captcha_image_path` VARCHAR(200) NOT NULL DEFAULT 'content.imageBase64' COMMENT '验证码图片字段路径(JSONPath)';
        ALTER TABLE `environments` ADD COLUMN `captcha_key_path` VARCHAR(200) NOT NULL DEFAULT 'content.codeKey' COMMENT '验证码Key字段路径(JSONPath)';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `environments` DROP COLUMN `captcha_image_path`;
        ALTER TABLE `environments` DROP COLUMN `captcha_key_path`;"""
