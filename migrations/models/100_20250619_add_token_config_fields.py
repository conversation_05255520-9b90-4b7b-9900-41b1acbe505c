from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `environments` 
        ADD COLUMN `token_url` VARCHAR(500) NULL COMMENT 'Token获取URL',
        ADD COLUMN `token_method` VARCHAR(10) NOT NULL DEFAULT 'POST' COMMENT 'Token获取请求方式',
        ADD COLUMN `token_headers` JSON NULL COMMENT 'Token获取请求头',
        ADD COLUMN `token_body` JSON NULL COMMENT 'Token获取请求体',
        ADD COLUMN `token_field_name` VARCHAR(100) NOT NULL DEFAULT 'token' COMMENT 'Token字段名称',
        ADD COLUMN `token_field_path` VARCHAR(200) NULL COMMENT 'Token字段路径(JSONPath)',
        ADD COLUMN `auto_refresh_token` BOOL NOT NULL DEFAULT 0 COMMENT '是否自动刷新Token',
        ADD COLUMN `token_refresh_interval` INT NOT NULL DEFAULT 3600 COMMENT 'Token刷新间隔(秒)',
        ADD COLUMN `last_token_refresh` DATETIME(6) NULL COMMENT '最后Token刷新时间';
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `environments` 
        DROP COLUMN `token_url`,
        DROP COLUMN `token_method`,
        DROP COLUMN `token_headers`,
        DROP COLUMN `token_body`,
        DROP COLUMN `token_field_name`,
        DROP COLUMN `token_field_path`,
        DROP COLUMN `auto_refresh_token`,
        DROP COLUMN `token_refresh_interval`,
        DROP COLUMN `last_token_refresh`;
    """
