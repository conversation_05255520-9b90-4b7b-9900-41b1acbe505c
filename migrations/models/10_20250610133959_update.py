from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `api_execution_result` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `api_request_id` INT NOT NULL  COMMENT 'API请求ID',
    `status_code` INT NOT NULL  COMMENT '响应状态码',
    `response_headers` JSON   COMMENT '响应头',
    `response_body` LONGTEXT   COMMENT '响应体',
    `execution_time` INT NOT NULL  COMMENT '执行时间(毫秒)',
    `error_message` LONGTEXT   COMMENT '错误信息',
    `success` BOOL NOT NULL  COMMENT '是否执行成功' DEFAULT 1,
    <PERSON><PERSON>Y `idx_api_executi_created_051c01` (`created_at`),
    KEY `idx_api_executi_updated_75327e` (`updated_at`),
    KEY `idx_api_executi_api_req_5f0d6a` (`api_request_id`)
) CHARACTER SET utf8mb4 COMMENT='API执行结果';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `api_execution_result`;"""
