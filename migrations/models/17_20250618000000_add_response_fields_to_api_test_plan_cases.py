from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api_test_plan_cases` 
        ADD COLUMN `status_code` INT NULL COMMENT '响应状态码',
        ADD COLUMN `response_headers` JSON NULL COMMENT '响应头',
        ADD COLUMN `response_body` LONGTEXT NULL COMMENT '响应体',
        ADD COLUMN `assertion_results` JSON NULL COMMENT '断言结果';
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `api_test_plan_cases` 
        DROP COLUMN `status_code`,
        DROP COLUMN `response_headers`,
        DROP COLUMN `response_body`,
        DROP COLUMN `assertion_results`;
    """
