from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `functional_test_plans` (
            `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
            `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
            `plan_name` VARCHAR(200) NOT NULL COMMENT '计划名称',
            `level` VARCHAR(10) NOT NULL DEFAULT 'medium' COMMENT '等级：low-低，medium-中，high-高',
            `status` VARCHAR(20) NOT NULL DEFAULT 'not_started' COMMENT '状态：not_started-未开始，in_progress-进行中，completed-已完成',
            `description` LONGTEXT COMMENT '计划描述',
            `project_id` INT NOT NULL COMMENT '所属项目ID',
            `user_id` INT NOT NULL COMMENT '创建用户ID',
            K<PERSON>Y `idx_functional_test_plans_created_at` (`created_at`),
            KEY `idx_functional_test_plans_updated_at` (`updated_at`),
            KEY `idx_functional_test_plans_plan_name` (`plan_name`),
            KEY `idx_functional_test_plans_level` (`level`),
            KEY `idx_functional_test_plans_status` (`status`),
            KEY `idx_functional_test_plans_project_id` (`project_id`),
            KEY `idx_functional_test_plans_user_id` (`user_id`)
        ) CHARACTER SET utf8mb4 COMMENT='功能测试计划表';
        
        CREATE TABLE IF NOT EXISTS `functional_test_plan_cases` (
            `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
            `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
            `plan_id` INT NOT NULL COMMENT '测试计划ID',
            `case_id` INT NOT NULL COMMENT '测试用例ID',
            `execution_order` INT NOT NULL DEFAULT 0 COMMENT '执行顺序',
            UNIQUE KEY `uid_functional_test_plan_cases_plan_id_case_id` (`plan_id`, `case_id`),
            KEY `idx_functional_test_plan_cases_created_at` (`created_at`),
            KEY `idx_functional_test_plan_cases_updated_at` (`updated_at`),
            KEY `idx_functional_test_plan_cases_plan_id` (`plan_id`),
            KEY `idx_functional_test_plan_cases_case_id` (`case_id`)
        ) CHARACTER SET utf8mb4 COMMENT='功能测试计划用例关联表';
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `functional_test_plan_cases`;
        DROP TABLE IF EXISTS `functional_test_plans`;
    """
