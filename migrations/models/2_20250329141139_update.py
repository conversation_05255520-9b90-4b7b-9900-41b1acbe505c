from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `projects` MODIFY COLUMN `end_date` DATETIME(6)   COMMENT '结束日期';
        ALTER TABLE `projects` MODIFY COLUMN `start_date` DATETIME(6)   COMMENT '开始日期';
        ALTER TABLE `projects` MODIFY COLUMN `start_date` DATETIME(6)   COMMENT '开始日期';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `projects` MODIFY COLUMN `end_date` DATE   COMMENT '结束日期';
        ALTER TABLE `projects` MODIFY COLUMN `start_date` DATE NOT NULL  COMMENT '开始日期';
        ALTER TABLE `projects` MODIFY COLUMN `start_date` DATE NOT NULL  COMMENT '开始日期';"""
