from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `project_modules` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `created_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL  DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `name` VARCHAR(100) NOT NULL  COMMENT '模块名称',
    `description` LONGTEXT   COMMENT '模块描述',
    `project_id` INT NOT NULL  COMMENT '所属项目ID',
    `parent_id` INT NOT NULL  COMMENT '父模块ID' DEFAULT 0,
    `order` INT NOT NULL  COMMENT '排序' DEFAULT 0,
    `status` VARCHAR(20) NOT NULL  COMMENT '模块状态' DEFAULT 'active',
    KEY `idx_project_mod_created_370f25` (`created_at`),
    KEY `idx_project_mod_updated_e6cf74` (`updated_at`),
    KEY `idx_project_mod_name_e4a24d` (`name`),
    KEY `idx_project_mod_project_8699e5` (`project_id`),
    KEY `idx_project_mod_parent__d40ba8` (`parent_id`),
    KEY `idx_project_mod_order_7b50ee` (`order`)
) CHARACTER SET utf8mb4 COMMENT='项目模块表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `project_modules`;"""
