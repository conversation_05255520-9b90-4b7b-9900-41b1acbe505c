<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作台API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            color: #ff4d4f;
            background: #fff2f0;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ffccc7;
        }
        .recent-executions {
            max-height: 300px;
            overflow-y: auto;
        }
        .execution-item {
            padding: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        .execution-item:last-child {
            border-bottom: none;
        }
        .execution-title {
            font-weight: bold;
            color: #333;
        }
        .execution-subtitle {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        .execution-extra {
            font-size: 12px;
            color: #999;
            float: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>工作台数据展示</h1>
        
        <div class="card">
            <h2>定时任务统计</h2>
            <div id="scheduled-task-loading" class="loading">正在加载定时任务数据...</div>
            <div id="scheduled-task-error" class="error" style="display: none;"></div>
            <div id="scheduled-task-content" style="display: none;">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="total-tasks">0</div>
                        <div class="stat-label">总任务数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="active-tasks">0</div>
                        <div class="stat-label">活跃任务</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="total-executions">0</div>
                        <div class="stat-label">总执行次数</div>
                    </div>
                </div>
                
                <h3>最近执行记录</h3>
                <div class="recent-executions" id="recent-executions">
                    <!-- 动态填充 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取定时任务统计数据
        async function fetchScheduledTaskStats() {
            try {
                const response = await fetch('http://localhost:9999/api/v1/scheduled_task/statistics');
                const result = await response.json();
                
                if (result.code === 200) {
                    displayScheduledTaskStats(result.data);
                } else {
                    showError('scheduled-task', `获取数据失败: ${result.msg}`);
                }
            } catch (error) {
                showError('scheduled-task', `网络错误: ${error.message}`);
            }
        }

        function displayScheduledTaskStats(data) {
            // 隐藏加载状态
            document.getElementById('scheduled-task-loading').style.display = 'none';
            document.getElementById('scheduled-task-content').style.display = 'block';
            
            // 填充统计数据
            document.getElementById('total-tasks').textContent = data.overview.total_tasks;
            document.getElementById('active-tasks').textContent = data.overview.active_tasks;
            document.getElementById('total-executions').textContent = data.overview.total_executions;
            
            // 填充最近执行记录
            const recentExecutionsContainer = document.getElementById('recent-executions');
            recentExecutionsContainer.innerHTML = '';
            
            if (data.recent_executions && data.recent_executions.length > 0) {
                data.recent_executions.forEach(execution => {
                    const item = document.createElement('div');
                    item.className = 'execution-item';
                    item.innerHTML = `
                        <div class="execution-extra">执行${execution.run_count}次</div>
                        <div class="execution-title">${execution.task_name}</div>
                        <div class="execution-subtitle">${execution.project_name} - ${execution.plan_name}</div>
                    `;
                    recentExecutionsContainer.appendChild(item);
                });
            } else {
                recentExecutionsContainer.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">暂无执行记录</div>';
            }
        }

        function showError(prefix, message) {
            document.getElementById(`${prefix}-loading`).style.display = 'none';
            document.getElementById(`${prefix}-error`).style.display = 'block';
            document.getElementById(`${prefix}-error`).textContent = message;
        }

        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', function() {
            fetchScheduledTaskStats();
        });
    </script>
</body>
</html>
