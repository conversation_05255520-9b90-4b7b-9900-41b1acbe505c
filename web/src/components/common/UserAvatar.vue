<template>
  <div 
    class="user-avatar" 
    :style="avatarStyle"
    :class="{ 'avatar-clickable': clickable }"
    @click="handleClick"
  >
    <span class="avatar-text" :style="textStyle">{{ avatarText }}</span>
    <div class="avatar-glow" v-if="glow"></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  username: {
    type: String,
    default: ''
  },
  size: {
    type: Number,
    default: 40
  },
  clickable: {
    type: Boolean,
    default: false
  },
  glow: {
    type: Boolean,
    default: true
  },
  gradient: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['click'])

// 生成用户名头像文本
const avatarText = computed(() => {
  if (!props.username) return 'U'
  // 如果是中文，取第一个字符
  if (/[\u4e00-\u9fa5]/.test(props.username)) {
    return props.username.charAt(0)
  }
  // 如果是英文，取前两个字符的大写
  return props.username.substring(0, 2).toUpperCase()
})

// 生成头像颜色
const getAvatarColor = (username) => {
  if (!username) return '#1890ff'

  const colors = [
    { primary: '#1890ff', secondary: '#40a9ff' },
    { primary: '#52c41a', secondary: '#73d13d' },
    { primary: '#faad14', secondary: '#ffc53d' },
    { primary: '#f5222d', secondary: '#ff4d4f' },
    { primary: '#722ed1', secondary: '#9254de' },
    { primary: '#13c2c2', secondary: '#36cfc9' },
    { primary: '#eb2f96', secondary: '#f759ab' },
    { primary: '#fa541c', secondary: '#ff7a45' },
    { primary: '#a0d911', secondary: '#b7eb8f' },
    { primary: '#2f54eb', secondary: '#597ef7' }
  ]

  // 根据用户名生成一个稳定的颜色索引
  let hash = 0
  for (let i = 0; i < username.length; i++) {
    hash = username.charCodeAt(i) + ((hash << 5) - hash)
  }

  return colors[Math.abs(hash) % colors.length]
}

// 头像样式
const avatarStyle = computed(() => {
  const colorPair = getAvatarColor(props.username)
  const baseStyle = {
    width: `${props.size}px`,
    height: `${props.size}px`,
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden',
    cursor: props.clickable ? 'pointer' : 'default',
    transition: 'all 0.3s ease',
    border: '2px solid rgba(255, 255, 255, 0.8)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
  }

  if (props.gradient) {
    baseStyle.background = `linear-gradient(135deg, ${colorPair.primary} 0%, ${colorPair.secondary} 100%)`
  } else {
    baseStyle.backgroundColor = colorPair.primary
  }

  return baseStyle
})

// 文字样式
const textStyle = computed(() => {
  const fontSize = Math.max(12, props.size * 0.4)
  return {
    color: 'white',
    fontSize: `${fontSize}px`,
    fontWeight: '600',
    textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
    userSelect: 'none'
  }
})

const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}
</script>

<style scoped>
.user-avatar {
  position: relative;
}

.user-avatar::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
}

.avatar-clickable:hover::before {
  opacity: 1;
}

.avatar-clickable:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.avatar-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  pointer-events: none;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-avatar {
    border-width: 1px;
  }
}
</style>
