<template>
  <n-card :title="title" size="small" :segmented="true" rounded-10>
    <template #header-extra>
      <n-button quaternary circle size="small" @click="refresh" :loading="loading">
        <template #icon>
          <n-icon><icon-mdi-refresh /></n-icon>
        </template>
      </n-button>
    </template>
    
    <!-- 饼图样式 -->
    <div v-if="type === 'pie'" class="pie-chart-container">
      <div class="simple-pie-chart">
        <div class="pie-center-info">
          <div class="pie-total">{{ total }}</div>
          <div class="pie-label">{{ centerLabel }}</div>
        </div>
      </div>
      <div class="pie-legend">
        <div
          v-for="(item, index) in pieData"
          :key="index"
          class="legend-item"
        >
          <div
            class="legend-color"
            :style="{ backgroundColor: colors[index % colors.length] }"
          ></div>
          <span class="legend-text">{{ item.name }}: {{ item.value }}</span>
          <span class="legend-percentage">
            ({{ total > 0 ? Math.round((item.value / total) * 100) : 0 }}%)
          </span>
        </div>
      </div>
    </div>

    <!-- 柱状图样式 -->
    <div v-else-if="type === 'bar'" class="bar-chart-container">
      <div class="bar-chart">
        <div 
          v-for="(item, index) in barData" 
          :key="index" 
          class="bar-item"
        >
          <div class="bar-label">{{ item.name }}</div>
          <div class="bar-wrapper">
            <div 
              class="bar-fill" 
              :style="{ 
                height: `${(item.value / maxValue) * 100}%`,
                backgroundColor: colors[index % colors.length]
              }"
            ></div>
          </div>
          <div class="bar-value">{{ item.value }}</div>
        </div>
      </div>
    </div>

    <!-- 统计卡片样式 -->
    <div v-else-if="type === 'stats'" class="stats-container">
      <n-grid :cols="statsData.length" :x-gap="12">
        <n-grid-item v-for="(stat, index) in statsData" :key="index">
          <div class="stat-card">
            <div class="stat-icon" :style="{ backgroundColor: colors[index % colors.length] }">
              <n-icon size="24" :color="'white'">
                <component :is="stat.icon || 'icon-mdi-chart-line'" />
              </n-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </n-grid-item>
      </n-grid>
    </div>

    <!-- 列表样式 -->
    <div v-else-if="type === 'list'" class="list-container">
      <div v-if="listData.length === 0" class="empty-state">
        <n-icon size="48" color="#d9d9d9">
          <icon-mdi-inbox />
        </n-icon>
        <p>暂无数据</p>
      </div>
      <div v-else class="list-items">
        <div 
          v-for="(item, index) in listData.slice(0, maxItems || 10)" 
          :key="index" 
          class="list-item"
        >
          <div class="list-item-content">
            <div class="list-item-title">{{ item.title }}</div>
            <div class="list-item-subtitle">{{ item.subtitle }}</div>
          </div>
          <div class="list-item-extra">{{ item.extra }}</div>
        </div>
      </div>
    </div>
  </n-card>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: true,
    validator: (value) => ['pie', 'bar', 'stats', 'list'].includes(value)
  },
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  centerLabel: {
    type: String,
    default: '总计'
  },
  maxItems: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits(['refresh'])

const colors = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d', 
  '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16'
]

// 饼图数据
const pieData = computed(() => {
  if (props.type !== 'pie') return []
  return props.data.filter(item => item.value > 0)
})

// 柱状图数据
const barData = computed(() => {
  if (props.type !== 'bar') return []
  return props.data
})

// 统计数据
const statsData = computed(() => {
  if (props.type !== 'stats') return []
  return props.data
})

// 列表数据
const listData = computed(() => {
  if (props.type !== 'list') return []
  return props.data
})

// 计算总数
const total = computed(() => {
  if (props.type === 'pie') {
    return pieData.value.reduce((sum, item) => sum + item.value, 0)
  }
  return 0
})

// 柱状图最大值
const maxValue = computed(() => {
  if (props.type === 'bar' && barData.value.length > 0) {
    return Math.max(...barData.value.map(item => item.value))
  }
  return 1
})

// 刷新数据

const refresh = () => {
  emit('refresh')
}
</script>

<style scoped>
/* 饼图样式 */
.pie-chart-container {
  display: flex;
  align-items: center;
  gap: 24px;
}

.simple-pie-chart {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-center-info {
  text-align: center;
  z-index: 2;
}

.pie-total {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.pie-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.pie-legend {
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-text {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.legend-percentage {
  font-size: 12px;
  color: #666;
}

/* 柱状图样式 */
.bar-chart-container {
  padding: 16px 0;
}

.bar-chart {
  display: flex;
  align-items: end;
  gap: 16px;
  height: 120px;
}

.bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.bar-wrapper {
  width: 100%;
  height: 80px;
  background: #f5f5f5;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: end;
}

.bar-fill {
  width: 100%;
  border-radius: 4px;
  transition: height 0.3s ease;
  min-height: 4px;
}

.bar-label, .bar-value {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.bar-value {
  font-weight: bold;
  color: #333;
}

/* 统计卡片样式 */
.stats-container {
  padding: 8px 0;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

/* 列表样式 */
.list-container {
  max-height: 300px;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 32px;
  color: #999;
}

.list-items {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fafafa;
  transition: background 0.2s ease;
}

.list-item:hover {
  background: #f0f0f0;
}

.list-item:first-child {
  border-radius: 6px 6px 0 0;
}

.list-item:last-child {
  border-radius: 0 0 6px 6px;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.list-item-subtitle {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.list-item-extra {
  font-size: 12px;
  color: #999;
}
</style>
