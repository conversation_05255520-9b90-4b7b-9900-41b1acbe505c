import { getToken } from '@/utils'
import { resolveResError } from './helpers'
import { useUserStore } from '@/store'

export function reqResolve(config) {
  // 处理不需要token的请求
  if (config.noNeedToken) {
    return config
  }

  const token = getToken()
  if (token) {
    config.headers.token = config.headers.token || token
  }

  return config
}

export function reqReject(error) {
  return Promise.reject(error)
}

export function resResolve(response) {
  const { data, status, statusText, config } = response

  // 如果是blob类型的响应，直接返回
  if (config.responseType === 'blob' && data instanceof Blob) {
    return Promise.resolve(data)
  }

  // 添加调试信息
  console.log('resResolve - 响应数据:', data)
  console.log('resResolve - 响应状态:', status)
  console.log('resResolve - data.code:', data?.code)

  if (data?.code !== 200) {
    const code = data?.code ?? status
    const msgValue = data?.msg ?? statusText
    console.log('resResolve - 错误消息值:', msgValue, '类型:', typeof msgValue)

    /** 根据code处理对应的操作，并返回处理后的message */
    const message = resolveResError(code, msgValue)
    window.$message?.error(message, { keepAliveOnHover: true })
    return Promise.reject({ code, message, error: data || response })
  }
  return Promise.resolve(data)
}

export async function resReject(error) {
  console.log('resReject - 错误对象:', error)
  console.log('resReject - 错误类型:', typeof error)
  console.log('resReject - 是否有response:', 'response' in error)

  if (!error || !error.response) {
    const code = error?.code
    const msgValue = error?.message
    console.log('resReject - 无response，错误消息:', msgValue, '类型:', typeof msgValue)

    /** 根据code处理对应的操作，并返回处理后的message */
    const message = resolveResError(code, msgValue)
    window.$message?.error(message)
    return Promise.reject({ code, message, error })
  }
  const { data, status } = error.response

  if (data?.code === 401) {
    try {
      const userStore = useUserStore()
      userStore.logout()
    } catch (error) {
      console.log('resReject error', error)
      return
    }
  }
  // 后端返回的response数据
  const code = data?.code ?? status
  const msgValue = data?.msg ?? error.message
  console.log('resReject - 有response，错误消息:', msgValue, '类型:', typeof msgValue)

  const message = resolveResError(code, msgValue)
  window.$message?.error(message, { keepAliveOnHover: true })
  return Promise.reject({ code, message, error: error.response?.data || error.response })
}
