import { useUserStore } from '@/store'

export function addBaseParams(params) {
  if (!params.userId) {
    params.userId = useUserStore().userId
  }
}

export function resolveResError(code, message) {
  // 处理message可能是数组的情况
  if (Array.isArray(message)) {
    console.warn('resolveResError收到数组格式的message:', message)
    // 递归展平数组并转换为字符串
    const flattenArray = (arr) => {
      return arr.reduce((acc, item) => {
        if (Array.isArray(item)) {
          return acc.concat(flattenArray(item))
        } else if (typeof item === 'string') {
          return acc.concat(item)
        } else if (item !== null && item !== undefined) {
          return acc.concat(String(item))
        }
        return acc
      }, [])
    }

    const flatMessages = flattenArray(message)
    message = flatMessages.join('; ') || '未知错误'
  }

  // 确保message是字符串
  if (typeof message !== 'string') {
    console.warn('resolveResError收到非字符串message:', message, typeof message)
    message = String(message || '未知错误')
  }

  switch (code) {
    case 400:
      message = message ?? '请求参数错误'
      break
    case 401:
      message = message ?? '登录已过期'
      break
    case 403:
      message = message ?? '没有权限'
      break
    case 404:
      message = message ?? '资源或接口不存在'
      break
    case 500:
      message = message ?? '服务器异常'
      break
    default:
      message = message ?? `【${code}】: 未知异常!`
      break
  }
  return message
}
