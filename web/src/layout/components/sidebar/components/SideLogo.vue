<template>
  <router-link h-60 f-c-c to="/">
    <!-- 使用img标签替代icon-custom-logo组件 -->
    <img src="/favicon.svg" alt="logo" class="logo-icon" />
    
    <h2
      v-show="!appStore.collapsed"
      ml-2
      mr-8
      max-w-150
      flex-shrink-0
      text-16
      font-bold
      color-primary
    >
      {{ title }}
    </h2>
  </router-link>
</template>

<script setup>
import { useAppStore } from '@/store'
const title = import.meta.env.VITE_TITLE

const appStore = useAppStore()
</script>

<style scoped>
/* 调整logo样式，确保可见性 */
.logo-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
  /* 添加滤镜使图标变为蓝色(#1E90FF) */
  filter: invert(48%) sepia(90%) saturate(1000%) hue-rotate(190deg) brightness(100%) contrast(95%);
}
</style>
