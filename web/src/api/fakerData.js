import request from '@/utils/http'

export default {
  /**
   * 获取Faker支持的数据类型
   * @returns {Promise<Object>} - 返回数据类型配置
   */
  getDataTypes() {
    return request.get('/faker_data/data_types')
  },

  /**
   * 获取Faker示例数据
   * @returns {Promise<Object>} - 返回示例数据
   */
  getSampleData() {
    return request.get('/faker_data/sample_data')
  },

  /**
   * 生成指定类型的随机数据
   * @param {Object} params - 生成参数
   * @param {string} params.data_type - 数据类型
   * @param {string} [params.category] - 数据分类
   * @param {number} [params.count=1] - 生成数量
   * @returns {Promise<Object>} - 返回生成的数据
   */
  generateData(params) {
    return request.post('/faker_data/generate', null, { params })
  },

  /**
   * 替换文本中的Faker变量
   * @param {string} text - 包含Faker变量的文本
   * @returns {Promise<Object>} - 返回替换后的文本
   */
  replaceVariables(text) {
    return request.post('/faker_data/replace_variables', null, {
      params: { text }
    })
  }
}
