import { request } from '@/utils'

const BASE_URL = '/database_query'

// 数据库连接管理
export default {
  // 获取数据库连接列表
  getConnections: (params = {}) => {
    return request.get(`${BASE_URL}/connections`, { params })
  },

  // 创建数据库连接
  createConnection: (data) => {
    return request.post(`${BASE_URL}/connections`, data)
  },

  // 更新数据库连接
  updateConnection: (id, data) => {
    return request.put(`${BASE_URL}/connections/${id}`, data)
  },

  // 删除数据库连接
  deleteConnection: (id) => {
    return request.delete(`${BASE_URL}/connections/${id}`)
  },

  // 获取数据库连接详情
  getConnection: (id) => {
    return request.get(`${BASE_URL}/connections/${id}`)
  },

  // 测试数据库连接
  testConnection: (id) => {
    return request.post(`${BASE_URL}/connections/${id}/test`)
  },

  // 执行SQL查询
  executeQuery: (data) => {
    return request.post(`${BASE_URL}/execute`, data)
  },

  // 获取数据库表结构
  getDatabaseSchema: (connectionId) => {
    return request.get(`${BASE_URL}/connections/${connectionId}/schema`)
  },

  // 获取表详细结构
  getTableSchema: (connectionId, tableName) => {
    return request.get(`${BASE_URL}/connections/${connectionId}/tables/${tableName}/schema`)
  },

  // 获取查询历史
  getQueryHistory: (params = {}) => {
    return request.get(`${BASE_URL}/history`, { params })
  },

  // 切换收藏状态
  toggleFavorite: (historyId) => {
    return request.post(`${BASE_URL}/history/${historyId}/favorite`)
  },

  // 删除查询历史
  deleteQueryHistory: (historyId) => {
    return request.delete(`${BASE_URL}/history/${historyId}`)
  }
}
