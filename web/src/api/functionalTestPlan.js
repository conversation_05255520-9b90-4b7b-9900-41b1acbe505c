import { request } from '@/utils'

export default {
  /**
   * 获取功能测试计划列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试计划列表数据
   */
  getFunctionalTestPlanList(params) {
    return request.get('/functional_test_plan/list', { params })
  },

  /**
   * 获取功能测试计划详情
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试计划详情
   */
  getFunctionalTestPlan(params) {
    return request.get('/functional_test_plan/get', { params })
  },

  /**
   * 创建功能测试计划
   * @param {Object} data - 测试计划数据
   * @returns {Promise<Object>} - 返回创建结果
   */
  createFunctionalTestPlan(data) {
    return request.post('/functional_test_plan/create', data)
  },

  /**
   * 更新功能测试计划
   * @param {Object} data - 测试计划数据
   * @returns {Promise<Object>} - 返回更新结果
   */
  updateFunctionalTestPlan(data) {
    return request.post('/functional_test_plan/update', data)
  },

  /**
   * 删除功能测试计划
   * @param {Object} data - 删除参数
   * @returns {Promise<Object>} - 返回删除结果
   */
  deleteFunctionalTestPlan(data) {
    return request.delete('/functional_test_plan/delete', { params: data })
  },

  /**
   * 复制功能测试计划
   * @param {Object} data - 复制参数
   * @returns {Promise<Object>} - 返回复制结果
   */
  copyFunctionalTestPlan(data) {
    return request.post('/functional_test_plan/copy', data)
  },

  /**
   * 获取测试计划关联的测试用例
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试用例列表
   */
  getTestPlanCases(params) {
    return request.get('/functional_test_plan/cases', { params })
  },

  /**
   * 添加测试用例到测试计划
   * @param {Object} data - 添加数据
   * @returns {Promise<Object>} - 返回添加结果
   */
  addTestCasesToPlan(data) {
    return request.post('/functional_test_plan/add_cases', data)
  },

  /**
   * 从测试计划中移除测试用例
   * @param {Object} data - 移除数据
   * @returns {Promise<Object>} - 返回移除结果
   */
  removeTestCasesFromPlan(data) {
    return request.post('/functional_test_plan/remove_cases', data)
  },

  /**
   * 获取项目下已审核的功能测试用例
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试用例列表
   */
  getApprovedTestCases(params) {
    return request.get('/functional_test_plan/approved_cases', { params })
  },

  /**
   * 更新测试用例执行顺序
   * @param {Object} data - 顺序数据
   * @returns {Promise<Object>} - 返回更新结果
   */
  updateCaseOrder(data) {
    return request.post('/functional_test_plan/update_case_order', data)
  },

  /**
   * 获取功能测试计划统计数据
   * @returns {Promise<Object>} - 返回统计数据
   */
  getFunctionalTestPlanStatistics() {
    return request.get('/functional_test_plan/statistics')
  },

  /**
   * 更新测试用例执行状态
   * @param {Object} data - 状态数据
   * @returns {Promise<Object>} - 返回更新结果
   */
  updateCaseStatus(data) {
    return request.post('/functional_test_plan/update_case_status', data)
  }
}
