import { request } from '@/utils'

export default {
  // 获取AI模型配置列表
  getAIModelConfigList: (params = {}) => request.get('/ai_model_config/list', { params }),
  
  // 获取AI模型配置详情
  getAIModelConfigById: (params = {}) => request.get('/ai_model_config/get', { params }),
  
  // 创建AI模型配置
  createAIModelConfig: (data = {}) => request.post('/ai_model_config/create', data),
  
  // 更新AI模型配置
  updateAIModelConfig: (data = {}) => request.post('/ai_model_config/update', data),
  
  // 删除AI模型配置
  deleteAIModelConfig: (params = {}) => request.delete('/ai_model_config/delete', { params }),
  
  // 获取AI模型类型列表
  getAIModelTypes: () => request.get('/ai_model_config/types'),
  
  // 获取状态选项
  getStatusOptions: () => request.get('/ai_model_config/status_options'),
  
  // 测试AI模型连接
  testAIModelConfig: (data = {}) => request.post('/ai_model_config/test', data),
  
  // 获取默认AI模型配置
  getDefaultAIModelConfig: () => request.get('/ai_model_config/default'),
  
  // 设置默认AI模型
  setDefaultAIModelConfig: (params = {}) => request.post('/ai_model_config/set_default', {}, { params }),
  
  // 根据类型获取AI模型配置
  getAIModelConfigsByType: (params = {}) => request.get('/ai_model_config/by_type', { params }),
}
