import { request } from '@/utils'

export default {
  /**
   * 获取功能测试用例列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试用例列表
   */
  getTestCases(params) {
    return request.get('/test_case/list', { params })
  },

  /**
   * 获取功能测试用例详情
   * @param {number} testCaseId - 测试用例ID
   * @returns {Promise<Object>} - 返回测试用例详情
   */
  getTestCase(testCaseId) {
    return request.get('/test_case/get', { params: { test_case_id: testCaseId } })
  },

  /**
   * 创建功能测试用例
   * @param {Object} data - 测试用例数据
   * @returns {Promise<Object>} - 返回创建结果
   */
  createTestCase(data) {
    return request.post('/test_case/create', data)
  },

  /**
   * 更新功能测试用例
   * @param {Object} data - 测试用例数据
   * @returns {Promise<Object>} - 返回更新结果
   */
  updateTestCase(data) {
    return request.post('/test_case/update', data)
  },

  /**
   * 删除功能测试用例
   * @param {Object} params - 删除参数，包含id字段
   * @returns {Promise<Object>} - 返回删除结果
   */
  deleteTestCase(params) {
    const id = params.id || params
    return request.delete('/test_case/delete', { params: { id } })
  },

  /**
   * 复制功能测试用例
   * @param {Object} data - 复制数据
   * @returns {Promise<Object>} - 返回复制结果
   */
  copyTestCase(data) {
    return request.post('/test_case/copy', data)
  },

  /**
   * 重置功能测试用例编号
   * @param {number} projectId - 项目ID
   * @returns {Promise<Object>} - 返回重置结果
   */
  resetTestCaseNumbers(projectId) {
    return request.post('/test_case/reset', { project_id: projectId })
  },

  /**
   * 下载功能测试用例导入模板
   * @returns {Promise<Blob>} - 返回Excel文件
   */
  downloadTemplate() {
    return request.get('/test_case/download_template', {
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
    })
  },

  /**
   * 导入功能测试用例
   * @param {Object} params - 导入参数
   * @param {File} params.file - Excel文件
   * @param {number} params.project_id - 项目ID
   * @param {number} params.module_id - 模块ID（可选）
   * @returns {Promise<Object>} - 返回导入结果
   */
  importTestCases(params) {
    const formData = new FormData()
    formData.append('file', params.file)
    formData.append('project_id', params.project_id)
    if (params.module_id) {
      formData.append('module_id', params.module_id)
    }

    return request.post('/test_case/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}
