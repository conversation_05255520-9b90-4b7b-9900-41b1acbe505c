import { request } from '@/utils'

export default {
  /**
   * 获取接口测试计划列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试计划列表数据
   */
  getApiTestPlanList(params) {
    return request.get('/api_test_plan/list', { params })
  },

  /**
   * 获取接口测试计划详情
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试计划详情
   */
  getApiTestPlan(params) {
    return request.get('/api_test_plan/get', { params })
  },

  /**
   * 创建接口测试计划
   * @param {Object} data - 测试计划数据
   * @returns {Promise<Object>} - 返回创建结果
   */
  createApiTestPlan(data) {
    return request.post('/api_test_plan/create', data)
  },

  /**
   * 更新接口测试计划
   * @param {Object} data - 测试计划数据
   * @returns {Promise<Object>} - 返回更新结果
   */
  updateApiTestPlan(data) {
    return request.post('/api_test_plan/update', data)
  },

  /**
   * 删除接口测试计划
   * @param {Object} data - 删除参数
   * @returns {Promise<Object>} - 返回删除结果
   */
  deleteApiTestPlan(data) {
    return request.delete('/api_test_plan/delete', { params: data })
  },

  /**
   * 复制接口测试计划
   * @param {Object} data - 复制参数
   * @returns {Promise<Object>} - 返回复制结果
   */
  copyApiTestPlan(data) {
    return request.post('/api_test_plan/copy', data)
  },

  /**
   * 执行接口测试计划
   * @param {Object} data - 执行数据
   * @returns {Promise<Object>} - 返回执行结果
   */
  executeApiTestPlan(data) {
    return request.post('/api_test_plan/execute', data)
  },

  /**
   * 批量执行接口测试计划（pytest）
   * @param {Object} data - 执行数据
   * @returns {Promise<Object>} - 返回执行结果
   */
  batchExecuteApiTestPlan(data) {
    return request.post('/api_test_plan/batch_execute', data)
  },

  /**
   * 获取测试计划关联的测试用例
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试用例列表
   */
  getTestPlanCases(params) {
    return request.get('/api_test_plan/cases', { params })
  },

  /**
   * 添加测试用例到测试计划
   * @param {Object} data - 添加数据
   * @returns {Promise<Object>} - 返回添加结果
   */
  addTestCasesToPlan(data) {
    return request.post('/api_test_plan/add_cases', data)
  },

  /**
   * 从测试计划中移除测试用例
   * @param {Object} data - 移除数据
   * @returns {Promise<Object>} - 返回移除结果
   */
  removeTestCasesFromPlan(data) {
    return request.post('/api_test_plan/remove_cases', data)
  },

  /**
   * 获取项目下已审核的接口测试用例
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试用例列表
   */
  getApprovedTestCases(params) {
    return request.get('/api_test_plan/approved_cases', { params })
  },

  /**
   * 更新测试用例执行顺序
   * @param {Object} data - 顺序数据
   * @returns {Promise<Object>} - 返回更新结果
   */
  updateCaseOrder(data) {
    return request.post('/api_test_plan/update_case_order', data)
  },

  /**
   * 获取接口测试计划统计数据
   * @returns {Promise<Object>} - 返回统计数据
   */
  getApiTestPlanStatistics() {
    return request.get('/api_test_plan/statistics')
  },

  /**
   * 更新测试用例执行状态
   * @param {Object} data - 更新数据
   * @returns {Promise<Object>} - 返回更新结果
   */
  updateCaseExecutionStatus(data) {
    return request.post('/api_test_plan/update_case_status', data)
  }
}
