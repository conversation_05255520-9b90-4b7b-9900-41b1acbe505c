import { request } from '@/utils'

export default {
  /**
   * 获取测试用例列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试用例列表数据
   */
  getApiTestCaseList(params) {
    return request.get('/api_test_case/list', { params })
  },

  /**
   * 获取测试用例详情
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试用例详情
   */
  getApiTestCase(params) {
    return request.get('/api_test_case/get', { params })
  },

  /**
   * 创建测试用例
   * @param {Object} data - 测试用例数据
   * @returns {Promise<Object>} - 返回创建结果
   */
  createApiTestCase(data) {
    return request.post('/api_test_case/create', data)
  },

  /**
   * 更新测试用例
   * @param {Object} data - 测试用例数据
   * @returns {Promise<Object>} - 返回更新结果
   */
  updateApiTestCase(data) {
    return request.post('/api_test_case/update', data)
  },

  /**
   * 删除测试用例
   * @param {Object} data - 删除参数
   * @returns {Promise<Object>} - 返回删除结果
   */
  deleteApiTestCase(data) {
    return request.delete('/api_test_case/delete', { params: data })
  },

  /**
   * 复制测试用例
   * @param {Object} data - 复制参数
   * @returns {Promise<Object>} - 返回复制结果
   */
  copyApiTestCase(data) {
    return request.post('/api_test_case/copy', data)
  },

  /**
   * 执行测试用例
   * @param {Object} data - 执行数据
   * @returns {Promise<Object>} - 返回执行结果
   */
  executeApiTestCase(data) {
    return request.post('/api_test_case/execute', data)
  }
}
