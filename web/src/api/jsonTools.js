import request from '@/utils/http'

export default {
  /**
   * 格式化JSON
   * @param {Object} params - 格式化参数
   * @param {string} params.json_data - JSON数据
   * @param {number} params.indent - 缩进空格数
   * @param {boolean} params.sort_keys - 是否排序键名
   * @returns {Promise<Object>} - 返回格式化结果
   */
  formatJson(params) {
    return request.post('/json_tools/format', params)
  },

  /**
   * 压缩JSON
   * @param {Object} params - 压缩参数
   * @param {string} params.json_data - JSON数据
   * @returns {Promise<Object>} - 返回压缩结果
   */
  compressJson(params) {
    return request.post('/json_tools/compress', params)
  },

  /**
   * 验证JSON
   * @param {Object} params - 验证参数
   * @param {string} params.json_data - JSON数据
   * @returns {Promise<Object>} - 返回验证结果
   */
  validateJson(params) {
    return request.post('/json_tools/validate', params)
  },

  /**
   * 格式转换
   * @param {Object} params - 转换参数
   * @param {string} params.data - 源数据
   * @param {string} params.source_format - 源格式
   * @param {string} params.target_format - 目标格式
   * @param {string} params.csv_delimiter - CSV分隔符
   * @returns {Promise<Object>} - 返回转换结果
   */
  convertFormat(params) {
    return request.post('/json_tools/convert', params)
  },

  /**
   * JSONPath查询
   * @param {Object} params - 查询参数
   * @param {string} params.json_data - JSON数据
   * @param {string} params.json_path - JSONPath表达式
   * @returns {Promise<Object>} - 返回查询结果
   */
  queryJsonPath(params) {
    return request.post('/json_tools/jsonpath', params)
  },

  /**
   * JSON Schema验证
   * @param {Object} params - 验证参数
   * @param {string} params.json_data - JSON数据
   * @param {string} params.schema - JSON Schema
   * @returns {Promise<Object>} - 返回验证结果
   */
  validateSchema(params) {
    return request.post('/json_tools/schema-validate', params)
  },

  /**
   * JSON对比
   * @param {Object} params - 对比参数
   * @param {string} params.json1 - 第一个JSON
   * @param {string} params.json2 - 第二个JSON
   * @returns {Promise<Object>} - 返回对比结果
   */
  compareJson(params) {
    return request.post('/json_tools/compare', params)
  },

  /**
   * JSON统计
   * @param {Object} params - 统计参数
   * @param {string} params.json_data - JSON数据
   * @returns {Promise<Object>} - 返回统计结果
   */
  getJsonStats(params) {
    return request.post('/json_tools/stats', params)
  },

  /**
   * 获取示例数据
   * @returns {Promise<Object>} - 返回示例数据
   */
  getExamples() {
    return request.get('/json_tools/examples')
  }
}
