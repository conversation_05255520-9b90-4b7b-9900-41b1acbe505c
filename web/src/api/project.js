import { request } from '@/utils'

export default {
  /**
   * 获取项目列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回项目列表数据
   */
  getProjects(params) {
    return request.get('/project/list', { params })
  },

  /**
   * 获取项目详情
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回项目详情
   */
  getProject(params) {
    return request.get('/project/get', { params })
  },

  /**
   * 创建项目
   * @param {Object} data - 项目数据
   * @returns {Promise<Object>} - 返回创建结果
   */
  createProject(data) {
    return request.post('/project/create', data)
  },

  /**
   * 更新项目
   * @param {Object} data - 项目数据
   * @returns {Promise<Object>} - 返回更新结果
   */
  updateProject(data) {
    return request.post('/project/update', data)
  },

  /**
   * 删除项目
   * @param {Object} data - 删除参数
   * @returns {Promise<Object>} - 返回删除结果
   */
  deleteProject(data) {
    return request.delete('/project/delete', { params: data })
  },

  /**
   * 获取项目列表（不分页）
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回项目列表数据
   */
  getProjectList(params) {
    return request.get('/project/project_list', { params })
  },

  /**
   * 获取项目模块树
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回模块树数据
   */
  getProjectModuleTree(params) {
    return request.get('/project/module_tree', { params })
  },

  /**
   * 创建模块
   * @param {Object} data - 模块数据
   * @returns {Promise<Object>} - 返回创建结果
   */
  createProjectModule(data) {
    return request.post('/project/module/create', data)
  },

  /**
   * 更新模块
   * @param {Object} data - 模块数据
   * @returns {Promise<Object>} - 返回更新结果
   */
  updateProjectModule(data) {
    return request.post('/project/module/update', data)
  },

  /**
   * 删除模块
   * @param {Object} data - 删除参数
   * @returns {Promise<Object>} - 返回删除结果
   */
  deleteProjectModule(data) {
    return request.delete('/project/module/delete', { params: data })
  },

  /**
   * 检查模块是否可以删除
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回检查结果
   */
  checkCanDeleteModule(params) {
    return request.get('/project/module/check_delete', { params })
  },

}