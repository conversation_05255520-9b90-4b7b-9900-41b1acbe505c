import { request } from '@/utils'

export default {
  /**
   * 获取环境配置列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回环境配置列表数据
   */
  getEnvironments(params) {
    return request.get('/environment/list', { params })
  },

  /**
   * 获取环境配置列表（别名方法）
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回环境配置列表数据
   */
  getEnvironmentList(params) {
    return request.get('/environment/list', { params })
  },

  /**
   * 获取环境配置详情
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回环境配置详情
   */
  getEnvironment(params) {
    return request.get('/environment/get', { params })
  },

  /**
   * 创建环境配置
   * @param {Object} data - 环境配置数据
   * @returns {Promise<Object>} - 返回创建结果
   */
  createEnvironment(data) {
    return request.post('/environment/create', data)
  },

  /**
   * 更新环境配置
   * @param {Object} data - 环境配置数据
   * @returns {Promise<Object>} - 返回更新结果
   */
  updateEnvironment(data) {
    return request.post('/environment/update', data)
  },

  /**
   * 删除环境配置
   * @param {Object} data - 删除参数
   * @returns {Promise<Object>} - 返回删除结果
   */
  deleteEnvironment(data) {
    return request.delete('/environment/delete', { params: data })
  },

  /**
   * 复制环境配置
   * @param {Object} data - 复制参数
   * @returns {Promise<Object>} - 返回复制结果
   */
  copyEnvironment(data) {
    return request.post('/environment/copy', data)
  },

  /**
   * 手动刷新环境Token
   * @param {Object} params - 环境ID参数
   * @returns {Promise<Object>} - 返回刷新结果
   */
  refreshToken(params) {
    return request.post('/environment/token/refresh', null, { params })
  },

  /**
   * 测试Token获取配置
   * @param {Object} params - 环境ID参数
   * @returns {Promise<Object>} - 返回测试结果
   */
  testTokenConfig(params) {
    return request.post('/environment/token/test', null, { params })
  },

  /**
   * 获取Token刷新状态
   * @returns {Promise<Object>} - 返回刷新状态
   */
  getTokenRefreshStatus() {
    return request.get('/environment/token/refresh_status')
  },

  /**
   * 测试验证码地址
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回测试结果
   */
  testCaptchaUrl(params) {
    return request.post('/environment/captcha/test', null, { params })
  }
}
