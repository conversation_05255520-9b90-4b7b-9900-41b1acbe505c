import { request } from '@/utils'

const BASE_URL = '/prompt_template'

export default {
  // 获取提示词模板列表
  getPromptTemplateList: (params) => request.get(`${BASE_URL}/list`, { params }),

  // 获取提示词模板详情
  getPromptTemplate: (id) => request.get(`${BASE_URL}/get`, { params: { id } }),

  // 创建提示词模板
  createPromptTemplate: (data) => request.post(`${BASE_URL}/create`, data),

  // 更新提示词模板
  updatePromptTemplate: (data) => request.post(`${BASE_URL}/update`, data),

  // 删除提示词模板
  deletePromptTemplate: (id) => request.delete(`${BASE_URL}/delete`, { params: { id } }),

  // 复制提示词模板
  copyPromptTemplate: (data) => request.post(`${BASE_URL}/copy`, data),

  // 测试提示词模板
  testPromptTemplate: (data) => request.post(`${BASE_URL}/test`, data),

  // 获取提示词模板分类列表
  getPromptTemplateCategories: () => request.get(`${BASE_URL}/categories`),

  // 根据分类获取提示词模板
  getTemplatesByCategory: (category) => request.get(`${BASE_URL}/by_category`, { params: { category } }),

  // 获取默认提示词模板
  getDefaultTemplate: (category) => request.get(`${BASE_URL}/default`, { params: { category } })
}
