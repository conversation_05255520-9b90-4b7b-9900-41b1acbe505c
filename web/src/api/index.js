import { request } from '@/utils'
import { get } from 'lodash-es'
import aiModelConfigApi from './ai_model_config'
import promptTemplate<PERSON><PERSON> from './prompt_template'

export default {
  login: (data) => request.post('/base/access_token', data, { noNeedToken: true }),
  getUserInfo: () => request.get('/base/userinfo'),
  getUserMenu: () => request.get('/base/usermenu'),
  getUserApi: () => request.get('/base/userapi'),
  // profile
  updatePassword: (data = {}) => request.post('/base/update_password', data),
  // users
  getUserList: (params = {}) => request.get('/user/list', { params }),
  getUserById: (params = {}) => request.get('/user/get', { params }),
  createUser: (data = {}) => request.post('/user/create', data),
  updateUser: (data = {}) => request.post('/user/update', data),
  deleteUser: (params = {}) => request.delete(`/user/delete`, { params }),
  resetPassword: (data = {}) => request.post(`/user/reset_password`, data),
  // role
  getRoleList: (params = {}) => request.get('/role/list', { params }),
  createRole: (data = {}) => request.post('/role/create', data),
  updateRole: (data = {}) => request.post('/role/update', data),
  deleteRole: (params = {}) => request.delete('/role/delete', { params }),
  updateRoleAuthorized: (data = {}) => request.post('/role/authorized', data),
  getRoleAuthorized: (params = {}) => request.get('/role/authorized', { params }),
  // menus
  getMenus: (params = {}) => request.get('/menu/list', { params }),
  createMenu: (data = {}) => request.post('/menu/create', data),
  updateMenu: (data = {}) => request.post('/menu/update', data),
  deleteMenu: (params = {}) => request.delete('/menu/delete', { params }),
  // apis
  getApis: (params = {}) => request.get('/api/list', { params }),
  createApi: (data = {}) => request.post('/api/create', data),
  updateApi: (data = {}) => request.post('/api/update', data),
  deleteApi: (params = {}) => request.delete('/api/delete', { params }),
  refreshApi: (data = {}) => request.post('/api/refresh', data),
  // depts
  getDepts: (params = {}) => request.get('/dept/list', { params }),
  createDept: (data = {}) => request.post('/dept/create', data),
  updateDept: (data = {}) => request.post('/dept/update', data),
  deleteDept: (params = {}) => request.delete('/dept/delete', { params }),
  // auditlog
  getAuditLogList: (params = {}) => request.get('/auditlog/list', { params }),
  // project
  getProjectList: (params = {}) => request.get('/project/project_list', { params }),
  getProjectModuleTree: (params = {}) => request.get('/project/module_tree', { params }),
  getModuleList: (params = {}) => request.get('/project/module_tree', { params }), // 复用模块树接口
  createProjectModule: (data = {}) => request.post('/project/module/create', data),
  updateProjectModule: (data = {}) => request.post('/project/module/update', data),
  deleteProjectModule: (params = {}) => request.delete('/project/module/delete', { params }),

  //api_request
  // getApiRequestList: (params = {}) => request.get('/api_request/list', { params }),
  // getApiRequestById: (params = {}) => request.get('/api_request/get', { params }),
  createApiRequest: (data = {}) => request.post('/api_requests/create', data),
  updateApiRequest: (data = {}) => request.post('/api_requests/update', data),
  deleteApiRequest: (params = {}) => request.delete('/api_requests/delete', { params }),
  getApiRequestList: (params = {}) => request.get('/api_requests/list', { params }),
  executeApiRequest: (data = {}) => request.post('/api_requests/execute', data),
  getRecentApiRequests: () => request.get('/api_requests/recent'),
  // 代理外部API请求
  proxyExternalApi: (data = {}) => request.post('/api_requests/proxy', data),
  // API执行历史
  getApiExecutionHistory: () => request.get('/api_execution_history/recent'),
  createApiExecutionHistory: (data = {}) => request.post('/api_execution_history/create', data),
  // 添加以下接口
  getApiImportList: (params) => request.get('/api_import/list', { params }),
  createApiImport: (data) => request.post('/api_import/create', data),
  updateApiImport: (data) => request.post('/api_import/update', data),
  deleteApiImport: (id) => request.delete('/api_import/delete', { params: { id } }),
  batchDeleteApiImport: (data) => request.post('/api_import/batch_delete', data),
  uploadApiImport: (data) => request.post('/api_import/upload', data),

  // AI生成接口测试用例相关接口
  aiGenerateApiTestCases: (data) => request.post('/api_import/ai_generate', data, { timeout: 30000 }),
  saveGeneratedApiTestCases: (data) => request.post('/api_import/save_test_cases', data),

  // AI模型配置相关接口
  ...aiModelConfigApi,

  // 提示词模板相关接口
  ...promptTemplateApi,

  // 便捷方法
  getPromptTemplatesByCategory: (category) => promptTemplateApi.getTemplatesByCategory(category),
  getAiModelConfigList: (params) => aiModelConfigApi.getAIModelConfigList(params),
}
