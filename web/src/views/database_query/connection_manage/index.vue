<template>
  <CommonPage show-footer title="数据库连接管理">
    <template #action>
      <div class="flex-y-center gap-16px">
        <n-button type="primary" @click="handleAdd">
          <TheIcon icon="material-symbols:add" class="mr-1"/>
          添加连接
        </n-button>
      </div>
    </template>

    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="getTableData"
    />

    <!-- 添加/编辑弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="submitLoading"
      @save="handleSubmit"
    >
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="120px"
        require-mark-placement="right-hanging"
      >
        <n-grid :cols="2" :x-gap="18">
          <n-form-item-gi :span="2" label="连接名称" path="name">
            <n-input v-model:value="formData.name" placeholder="请输入连接名称" />
          </n-form-item-gi>
          <n-form-item-gi label="数据库类型" path="db_type">
            <n-select
              v-model:value="formData.db_type"
              placeholder="请选择数据库类型"
              :options="dbTypeOptions"
            />
          </n-form-item-gi>
          <n-form-item-gi label="字符集" path="charset">
            <n-input v-model:value="formData.charset" placeholder="utf8mb4" />
          </n-form-item-gi>
          <n-form-item-gi label="主机地址" path="host">
            <n-input v-model:value="formData.host" placeholder="请输入主机地址" />
          </n-form-item-gi>
          <n-form-item-gi label="端口号" path="port">
            <n-input-number v-model:value="formData.port" placeholder="请输入端口号" class="w-full" />
          </n-form-item-gi>
          <n-form-item-gi label="数据库名" path="database">
            <n-input v-model:value="formData.database" placeholder="请输入数据库名称" />
          </n-form-item-gi>
          <n-form-item-gi label="用户名" path="username">
            <n-input v-model:value="formData.username" placeholder="请输入用户名" />
          </n-form-item-gi>
          <n-form-item-gi label="密码" path="password">
            <n-input
              v-model:value="formData.password"
              type="password"
              placeholder="请输入密码"
              show-password-on="click"
            />
          </n-form-item-gi>
          <n-form-item-gi label="最大连接数" path="max_connections">
            <n-input-number v-model:value="formData.max_connections" placeholder="10" class="w-full" />
          </n-form-item-gi>
          <n-form-item-gi label="连接超时(秒)" path="connection_timeout">
            <n-input-number v-model:value="formData.connection_timeout" placeholder="30" class="w-full" />
          </n-form-item-gi>
          <n-form-item-gi :span="2" label="描述" path="description">
            <n-input
              v-model:value="formData.description"
              type="textarea"
              placeholder="请输入连接描述"
              :autosize="{ minRows: 3, maxRows: 5 }"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </CrudModal>
  </CommonPage>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { NButton, NTag, NPopconfirm, useMessage } from 'naive-ui'
import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import api from '@/api/database_query'

defineOptions({ name: 'DatabaseConnectionManage' })

const $message = useMessage()
const $table = ref(null)

// 查询参数
const queryItems = ref({
  name: null,
  db_type: null
})

// 表格配置
const columns = [
  { title: '连接名称', key: 'name', width: 150, ellipsis: { tooltip: true } },
  { title: '数据库类型', key: 'db_type', width: 100 },
  { title: '主机地址', key: 'host', width: 150, ellipsis: { tooltip: true } },
  { title: '端口', key: 'port', width: 80 },
  { title: '数据库名', key: 'database', width: 120, ellipsis: { tooltip: true } },
  { title: '用户名', key: 'username', width: 100 },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    render: (row) => {
      return h(NTag, { type: row.is_active ? 'success' : 'error' }, {
        default: () => row.is_active ? '启用' : '禁用'
      })
    }
  },
  { title: '创建时间', key: 'created_at', width: 180 },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right',
    render: (row) => {
      return [
        h(NButton, {
          size: 'small',
          type: 'primary',
          secondary: true,
          onClick: () => handleTest(row)
        }, { default: () => '测试连接' }),
        // h(NButton, {
        //   size: 'small',
        //   type: 'primary',
        //   style: 'margin-left: 8px;',
        //   onClick: () => handleEdit(row)
        // }, { default: () => '编辑' }),
        h(NPopconfirm, {
          onPositiveClick: () => handleDelete(row.id),
          style: 'margin-left: 8px;'
        }, {
          default: () => '确认删除吗？',
          trigger: () => h(NButton, { size: 'small', type: 'error' }, { default: () => '删除' })
        })
      ]
    }
  }
]

const rowKey = (row) => row.id

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref('')
const formRef = ref()
const submitLoading = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  db_type: 'mysql',
  host: '',
  port: 3306,
  database: '',
  username: '',
  password: '',
  charset: 'utf8mb4',
  description: '',
  is_active: true,
  max_connections: 10,
  connection_timeout: 30
})

// 数据库类型选项
const dbTypeOptions = [
  { label: 'MySQL', value: 'mysql' },
  { label: 'PostgreSQL', value: 'postgresql' },
  { label: 'SQLite', value: 'sqlite' }
]

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入连接名称', trigger: 'blur' }],
  db_type: [{ required: true, message: '请选择数据库类型', trigger: 'change' }],
  host: [{ required: true, message: '请输入主机地址', trigger: 'blur' }],
  port: [{ required: true, type: 'number', message: '请输入端口号', trigger: 'blur' }],
  database: [{ required: true, message: '请输入数据库名称', trigger: 'blur' }],
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

// 获取表格数据
async function getTableData(params = {}) {
  try {
    // 注意：响应拦截器已经返回了data，所以response就是后端的响应数据
    const response = await api.getConnections(params)

    // response 结构: {code: 200, msg: null, data: [...], total: 1, page: 1, page_size: 10}
    return {
      data: response.data || [],
      total: response.total || 0
    }
  } catch (error) {
    console.error('获取数据库连接列表失败:', error)
    return { data: [], total: 0 }
  }
}

// 添加
function handleAdd() {
  modalTitle.value = '添加数据库连接'
  resetForm()
  modalVisible.value = true
}

// 编辑
async function handleEdit(row) {
  modalTitle.value = '编辑数据库连接'
  try {
    const response = await api.getConnection(row.id)
    Object.assign(formData, response.data)
    modalVisible.value = true
  } catch (error) {
    $message.error('获取连接信息失败')
  }
}

// 测试连接
async function handleTest(row) {
  try {
    const response = await api.testConnection(row.id)
    if (response.data.success) {
      $message.success(`连接成功！服务器版本: ${response.data.server_version || '未知'}`)
    } else {
      $message.error(`连接失败: ${response.data.message}`)
    }
  } catch (error) {
    $message.error('测试连接失败')
  }
}

// 删除连接
async function handleDelete(id) {
  try {
    await api.deleteConnection(id)
    $message.success('删除成功')
    $table.value?.handleSearch()
  } catch (error) {
    $message.error('删除失败')
  }
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value?.validate()
    submitLoading.value = true
    
    if (formData.id) {
      await api.updateConnection(formData.id, formData)
      $message.success('更新成功')
    } else {
      await api.createConnection(formData)
      $message.success('创建成功')
    }
    
    closeModal()
    $table.value?.handleSearch()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
function resetForm() {
  Object.assign(formData, {
    id: undefined,
    name: '',
    db_type: 'mysql',
    host: '',
    port: 3306,
    database: '',
    username: '',
    password: '',
    charset: 'utf8mb4',
    description: '',
    is_active: true,
    max_connections: 10,
    connection_timeout: 30
  })
}

// 关闭弹窗
function closeModal() {
  modalVisible.value = false
  resetForm()
}

onMounted(() => {
  $table.value?.handleSearch()
})
</script>
