<template>
  <CommonPage show-footer title="AI功能测试用例生成">
    <template #action>
      <NButton type="primary" @click="handleGenerate">
        <TheIcon icon="material-symbols:auto-awesome-outline" :size="18" class="mr-5" />生成测试用例
      </NButton>
    </template>

    <!-- 生成历史表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getGenerationHistory"
    >
      <template #queryBar>
        <QueryBarItem label="任务名称" :label-width="80">
          <NInput
            v-model:value="queryItems.task_name"
            type="text"
            placeholder="请输入任务名称"
            clearable
          />
        </QueryBarItem>
        <QueryBarItem label="项目" :label-width="50">
          <NSelect
            v-model:value="queryItems.project_id"
            placeholder="请选择项目"
            style="width: 150px"
            clearable
            :options="projectOptions"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 生成测试用例弹窗 -->
    <NModal v-model:show="generateModalVisible" preset="dialog" title="生成功能测试用例" style="width: 800px">
      <NForm
        ref="generateFormRef"
        label-placement="left"
        label-align="left"
        :label-width="120"
        :model="generateForm"
        :rules="generateRules"
      >
        <NFormItem label="任务名称" path="task_name">
          <NInput v-model:value="generateForm.task_name" placeholder="请输入任务名称" />
        </NFormItem>
        <NFormItem label="项目" path="project_id">
          <NSelect
            v-model:value="generateForm.project_id"
            placeholder="请选择项目"
            :options="projectOptions"
            clearable
            filterable
            @update:value="handleProjectChange"
            @focus="() => console.log('项目选择器获得焦点，当前值:', generateForm.project_id, '选项:', projectOptions)"
          />
        </NFormItem>
        <NFormItem label="模块" path="module_id">
          <NSelect
            v-model:value="generateForm.module_id"
            placeholder="请选择模块（可选）"
            clearable
            :options="moduleOptions"
          />
        </NFormItem>
        <NFormItem label="需求描述" path="requirement_description">
          <NInput
            v-model:value="generateForm.requirement_description"
            type="textarea"
            placeholder="请详细描述功能需求，包括功能点、业务流程、验证要求等"
            :rows="8"
          />
        </NFormItem>
        <!-- <NFormItem label="提示词模板" path="prompt_template_id">
          <NSelect
            v-model:value="generateForm.prompt_template_id"
            placeholder="请选择提示词模板（不选择则使用默认模板）"
            clearable
            :options="templateOptions"
          />
        </NFormItem>
        <NFormItem label="AI模型" path="ai_model_config_id">
          <NSelect
            v-model:value="generateForm.ai_model_config_id"
            placeholder="请选择AI模型（不选择则使用默认模型）"
            clearable
            :options="aiModelOptions"
          />
        </NFormItem> -->
        <NFormItem label="生成数量" path="generate_count">
          <NInputNumber
            v-model:value="generateForm.generate_count"
            :min="1"
            :max="20"
            placeholder="期望生成的测试用例数量"
            style="width: 100%"
            @focus="() => console.log('生成数量获得焦点，当前值:', generateForm.generate_count)"
            @update:value="(value) => console.log('生成数量更新:', value)"
          />
        </NFormItem>
      </NForm>

      <template #action>
        <NSpace>
          <NButton @click="generateModalVisible = false">取消</NButton>
          <NButton type="primary" :loading="generateLoading" @click="handleSubmitGenerate">生成</NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 生成结果弹窗 -->
    <NModal v-model:show="resultModalVisible" preset="dialog" title="生成结果" style="width: 1400px">
      <NSpace vertical v-if="generateResult.success">
        <NAlert type="success" title="生成成功">
          成功生成 {{ generateResult.generated_cases.length }} 个测试用例，
          耗时 {{ generateResult.generation_time?.toFixed(2) }} 秒
        </NAlert>

        <!-- 测试用例列表 -->
        <NCard size="small">
          <template #header>
            <NSpace align="center" justify="space-between">
              <span>生成的测试用例</span>
              <span class="text-gray-500">
                已选择 {{ selectedCases.length }} / {{ generateResult.generated_cases.length }} 个用例
              </span>
            </NSpace>
          </template>

          <!-- 使用表格形式展示，类似功能测试用例模块 -->
          <NDataTable
            :columns="testCaseColumns"
            :data="generateResult.generated_cases"
            :row-key="(row) => row.id"
            :checked-row-keys="selectedCases"
            @update:checked-row-keys="handleCheckedRowKeysChange"
            size="small"
            :scroll-x="1200"
            :pagination="false"
          />
        </NCard>
      </NSpace>

      <NAlert v-else type="error" title="生成失败">
        {{ generateResult.error_message }}
      </NAlert>

      <template #action>
        <NSpace>
          <NButton @click="resultModalVisible = false">关闭</NButton>
          <NButton
            v-if="generateResult.success && selectedCases.length > 0"
            type="primary"
            :loading="saveLoading"
            @click="handleSaveTestCases"
          >
            保存选中的 {{ selectedCases.length }} 个用例
          </NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 查看任务详情弹窗 -->
    <NModal v-model:show="taskDetailModalVisible" preset="dialog" title="任务详情" style="width: 1400px">
      <NSpace vertical v-if="taskDetail">
        <!-- 基本信息 -->
<!--        <NCard title="基本信息" size="small">-->
<!--          <NDescriptions :column="2" size="small">-->
<!--            <NDescriptionsItem label="任务名称">-->
<!--              {{ taskDetail.task_name }}-->
<!--            </NDescriptionsItem>-->
<!--            <NDescriptionsItem label="生成状态">-->
<!--              <NTag :type="getStatusType(taskDetail.status)">{{ getStatusText(taskDetail.status) }}</NTag>-->
<!--            </NDescriptionsItem>-->
<!--            <NDescriptionsItem label="项目">-->
<!--              {{ taskDetail.project_name || '未知项目' }}-->
<!--            </NDescriptionsItem>-->
<!--            <NDescriptionsItem label="模块">-->
<!--              {{ taskDetail.module_name || '无' }}-->
<!--            </NDescriptionsItem>-->
<!--            <NDescriptionsItem label="生成数量">-->
<!--              {{ taskDetail.generated_count }} 个-->
<!--            </NDescriptionsItem>-->
<!--            <NDescriptionsItem label="生成耗时">-->
<!--              {{ taskDetail.generation_time ? taskDetail.generation_time + ' 秒' : '无' }}-->
<!--            </NDescriptionsItem>-->
<!--            <NDescriptionsItem label="创建时间" :span="2">-->
<!--              {{ formatDateTime(taskDetail.created_at) }}-->
<!--            </NDescriptionsItem>-->
<!--            <NDescriptionsItem label="需求描述" :span="2">-->
<!--              <pre style="white-space: pre-wrap; font-family: inherit;">{{ taskDetail.requirement_description }}</pre>-->
<!--            </NDescriptionsItem>-->
<!--          </NDescriptions>-->
<!--        </NCard>-->

        <!-- 生成的测试用例 -->
        <NCard v-if="taskDetail.generated_cases && taskDetail.generated_cases.length > 0" title="生成的测试用例" size="small">
          <!-- 使用表格形式展示，与生成结果弹窗保持一致 -->
          <NDataTable
            :columns="viewTestCaseColumns"
            :data="taskDetail.generated_cases"
            :row-key="(row, index) => `view_case_${index}`"
            size="small"
            :scroll-x="1200"
            :pagination="false"
          />
        </NCard>

        <NAlert v-else-if="taskDetail.status === 'failed'" type="error" title="生成失败">
          {{ taskDetail.error_message || '生成过程中出现未知错误' }}
        </NAlert>

        <NAlert v-else-if="taskDetail.status === 'pending'" type="info" title="任务状态">
          任务尚未开始生成
        </NAlert>

        <NAlert v-else-if="taskDetail.status === 'generating'" type="info" title="任务状态">
          任务正在生成中...
        </NAlert>
      </NSpace>

      <template #action>
        <NButton @click="taskDetailModalVisible = false">关闭</NButton>
      </template>
    </NModal>
  </CommonPage>
</template>

<script setup>
import { h, onMounted, ref, computed } from 'vue'
import { NButton, NTag, useMessage } from 'naive-ui'
import { formatDateTime, renderIcon, getToken } from '@/utils'
import api from '@/api/ai_test_case_generation'
import projectApi from '@/api/project'
import promptTemplateApi from '@/api/prompt_template'
import aiModelApi from '@/api/ai_model_config'

defineOptions({ name: 'AiTestCaseGeneration' })

const $message = useMessage()
const $table = ref(null)
const generateModalVisible = ref(false)
const resultModalVisible = ref(false)
const taskDetailModalVisible = ref(false)
const generateLoading = ref(false)
const saveLoading = ref(false)
const generateFormRef = ref(null)

// 选择相关
const selectedCases = ref([])

// 任务详情
const taskDetail = ref(null)

// 查询参数
const queryItems = ref({
  task_name: '',
  project_id: null
})

// 表格列定义
const columns = [
  { title: 'ID', key: 'id', width: 60 },
  { title: '任务名称', key: 'task_name', width: 200, ellipsis: { tooltip: true } },
  { title: '需求描述', key: 'requirement_description', width: 300, ellipsis: { tooltip: true } },
  { title: '生成数量', key: 'generated_count', width: 100 },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        pending: { type: 'default', text: '待生成' },
        generating: { type: 'info', text: '生成中' },
        completed: { type: 'success', text: '已完成' },
        failed: { type: 'error', text: '失败' }
      }
      const status = statusMap[row.status] || { type: 'default', text: row.status }
      return h(NTag, { type: status.type }, { default: () => status.text })
    }
  },
  { title: '生成耗时(秒)', key: 'generation_time', width: 120 },
  { title: '创建人', key: 'creator_name', width: 100 },
  { title: '创建时间', key: 'created_at', width: 180, render: (row) => formatDateTime(row.created_at) },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center',
    fixed: 'right',
    render: (row) => {
      return h('div', { style: 'display: flex; gap: 8px; justify-content: center;' }, [
        h(
          NButton,
          { size: 'small', type: 'primary', onClick: () => handleViewTask(row) },
          { default: () => '查看', icon: renderIcon('material-symbols:visibility', { size: 14 }) }
        ),
        h(
          NButton,
          { size: 'small', type: 'error', onClick: () => handleDeleteTask(row.id) },
          { default: () => '删除', icon: renderIcon('material-symbols:delete-outline', { size: 14 }) }
        )
      ])
    }
  }
]

// 测试用例表格列定义（用于生成结果弹窗）
const testCaseColumns = [
  {
    type: 'selection',
    width: 50
  },
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (row, index) => index + 1
  },
  {
    title: '用例名称',
    key: 'case_name',
    width: 200,
    ellipsis: { tooltip: true }
  },
  {
    title: '用例等级',
    key: 'case_level',
    width: 100,
    render: (row) => h(NTag, { type: getLevelType(row.case_level) }, { default: () => getLevelText(row.case_level) })
  },
  {
    title: '冒烟用例',
    key: 'is_smoke',
    width: 100,
    render: (row) => h(NTag, { type: row.is_smoke ? 'warning' : 'default' }, { default: () => row.is_smoke ? '是' : '否' })
  },
  {
    title: '前置条件',
    key: 'precondition',
    width: 100,
    ellipsis: { tooltip: true },
    render: (row) => row.precondition || '无'
  },
  {
    title: '测试步骤',
    key: 'test_steps',
    width: 300,
    ellipsis: { tooltip: true }
  },
  {
    title: '预期结果',
    key: 'expected_result',
    width: 300,
    ellipsis: { tooltip: true }
  }
]

// 查看测试用例表格列定义（用于任务详情弹窗）
const viewTestCaseColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (row, index) => index + 1
  },
  {
    title: '用例名称',
    key: 'case_name',
    width: 200,
    ellipsis: { tooltip: true }
  },
  {
    title: '用例等级',
    key: 'case_level',
    width: 100,
    render: (row) => h(NTag, { type: getLevelType(row.case_level) }, { default: () => getLevelText(row.case_level) })
  },
  {
    title: '冒烟用例',
    key: 'is_smoke',
    width: 100,
    render: (row) => h(NTag, { type: row.is_smoke ? 'warning' : 'default' }, { default: () => row.is_smoke ? '是' : '否' })
  },
  {
    title: '前置条件',
    key: 'precondition',
    width: 150,
    ellipsis: { tooltip: true },
    render: (row) => row.precondition || '无'
  },
  {
    title: '测试步骤',
    key: 'test_steps',
    width: 300,
    ellipsis: { tooltip: true }
  },
  {
    title: '预期结果',
    key: 'expected_result',
    width: 300,
    ellipsis: { tooltip: true }
  }
]

// 生成表单
const generateForm = ref({
  task_name: '',
  requirement_description: '',
  project_id: null,
  module_id: null,
  prompt_template_id: null,
  ai_model_config_id: null,
  generate_count: 5
})

// 表单验证规则
const generateRules = {
  task_name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  requirement_description: [{ required: true, message: '请输入需求描述', trigger: 'blur' }],
  project_id: [
    {
      required: true,
      message: '请选择项目',
      trigger: ['change', 'blur'],
      validator: (rule, value) => {
        console.log('项目验证 - 值:', value, '类型:', typeof value)
        if (value === null || value === undefined || value === '') {
          return new Error('请选择项目')
        }
        return true
      }
    }
  ],
  generate_count: [
    {
      required: true,
      message: '请输入生成数量',
      trigger: ['blur', 'change'],
      validator: (rule, value) => {
        console.log('生成数量验证 - 值:', value, '类型:', typeof value)
        if (value === null || value === undefined || value === '' || value <= 0) {
          return new Error('请输入有效的生成数量')
        }
        return true
      }
    }
  ]
}

// 生成结果
const generateResult = ref({
  success: false,
  task_id: null,
  generated_cases: [],
  saved_count: 0,
  error_message: '',
  generation_time: 0
})

// 选项数据
const projectOptions = ref([])
const moduleOptions = ref([])
const templateOptions = ref([])
const aiModelOptions = ref([])

// 初始化
onMounted(async () => {
  await loadProjects()
  await loadTemplates()
  await loadAiModels()
})

// 加载项目选项
async function loadProjects() {
  try {
    console.log('开始加载项目列表...')
    const response = await projectApi.getProjectList()
    console.log('项目API响应:', response)

    const data = response.data || response
    console.log('项目数据:', data)

    if (Array.isArray(data)) {
      projectOptions.value = data.map(project => ({
        label: project.name,
        value: project.id
      }))
      console.log('项目选项:', projectOptions.value)
    } else {
      console.error('项目数据格式错误:', data)
      projectOptions.value = []
    }
  } catch (error) {
    console.error('加载项目失败:', error)
    projectOptions.value = []
  }
}

// 加载模块选项
async function loadModules(projectId) {
  try {
    const { data } = await projectApi.getProjectModuleTree({ project_id: projectId })
    moduleOptions.value = flattenModules(data).map(module => ({
      label: module.name,
      value: module.id
    }))
  } catch (error) {
    console.error('加载模块失败:', error)
  }
}

// 扁平化模块树
function flattenModules(modules) {
  const result = []
  function traverse(items, prefix = '') {
    items.forEach(item => {
      result.push({
        id: item.id,
        name: prefix + item.name
      })
      if (item.children && item.children.length > 0) {
        traverse(item.children, prefix + item.name + ' / ')
      }
    })
  }
  traverse(modules)
  return result
}

// 加载提示词模板选项
async function loadTemplates() {
  try {
    const { data } = await promptTemplateApi.getTemplatesByCategory('functional_test_case')
    templateOptions.value = data.map(template => ({
      label: template.name,
      value: template.id
    }))
  } catch (error) {
    console.error('加载提示词模板失败:', error)
  }
}

// 加载AI模型选项
async function loadAiModels() {
  try {
    const response = await aiModelApi.getAIModelConfigList({ page: 1, page_size: 100 })
    console.log('AI模型API响应:', response)

    // 根据实际API响应结构处理数据
    // API返回格式: { code: 200, data: [...], total: 2, page: 1, page_size: 10 }
    const models = response.data || []
    console.log('AI模型列表:', models)

    if (Array.isArray(models)) {
      aiModelOptions.value = models.map(model => ({
        label: model.name,
        value: model.id
      }))
      console.log('AI模型选项:', aiModelOptions.value)
    } else {
      console.error('AI模型数据格式错误:', models)
      aiModelOptions.value = []
    }
  } catch (error) {
    console.error('加载AI模型失败:', error)
    aiModelOptions.value = []
  }
}

// 项目变化处理
async function handleProjectChange(projectId) {
  console.log('项目变化处理 - 新项目ID:', projectId)
  console.log('当前表单项目ID:', generateForm.value.project_id)

  // 确保项目ID正确设置
  generateForm.value.project_id = projectId
  generateForm.value.module_id = null

  if (projectId) {
    await loadModules(projectId)
  } else {
    moduleOptions.value = []
  }

  console.log('项目变化处理完成 - 最终项目ID:', generateForm.value.project_id)
}

// 生成测试用例
function handleGenerate() {
  console.log('打开生成对话框')
  console.log('当前项目选项:', projectOptions.value)

  generateForm.value = {
    task_name: '',
    requirement_description: '',
    project_id: null,
    module_id: null,
    prompt_template_id: null,
    ai_model_config_id: null,
    generate_count: 5
  }

  // 重置选择状态
  selectedCases.value = []

  console.log('初始化表单数据:', generateForm.value)
  generateModalVisible.value = true
}

// 提交生成
async function handleSubmitGenerate() {
  try {
    console.log('=== 开始生成流程 ===')
    console.log('表单数据:', generateForm.value)

    // 表单验证前检查数据
    console.log('验证前的表单数据:')
    console.log('- task_name:', generateForm.value.task_name)
    console.log('- project_id:', generateForm.value.project_id)
    console.log('- requirement_description:', generateForm.value.requirement_description)
    console.log('- generate_count:', generateForm.value.generate_count)
    console.log('- 项目选项:', projectOptions.value)

    // 表单验证
    try {
      await generateFormRef.value?.validate()
      console.log('✅ 表单验证通过')
    } catch (validationErrors) {
      console.log('❌ 表单验证失败:', validationErrors)
      console.log('验证错误详细信息:', JSON.stringify(validationErrors, null, 2))

      // Naive UI的验证错误是一个数组，包含所有验证失败的字段
      if (Array.isArray(validationErrors)) {
        const errorMessages = validationErrors.map(error => {
          console.log('处理验证错误项:', error)
          if (Array.isArray(error)) {
            return error.map(e => {
              console.log('子错误项:', e)
              return e.message || e
            }).join('; ')
          }
          return error.message || error
        }).join('; ')
        console.log('最终错误消息:', errorMessages)
        $message.error(`表单验证失败: ${errorMessages}`)
      } else {
        $message.error('表单验证失败，请检查输入')
      }
      return
    }

    // 检查登录状态
    const token = getToken()
    if (!token) {
      $message.error('请先登录')
      return
    }

    // 清空之前的结果
    generateResult.value = {
      success: false,
      task_id: null,
      generated_cases: [],
      saved_count: 0,
      error_message: '',
      generation_time: 0
    }

    generateLoading.value = true
    console.log('发送生成请求:', generateForm.value)

    console.log('=== 开始API调用 ===')
    console.log('请求数据:', generateForm.value)

    // 添加随机延迟避免缓存问题
    await new Promise(resolve => setTimeout(resolve, 100))

    const response = await api.generateTestCases({
      ...generateForm.value,
      _timestamp: Date.now() // 添加时间戳避免缓存
    })

    console.log('=== API调用完成 ===')
    console.log('原始响应对象:', response)
    console.log('响应数据类型:', typeof response)
    console.log('响应是否有data属性:', 'data' in response)

    if (response && response.data) {
      console.log('response.data类型:', typeof response.data)
      console.log('response.data内容:', JSON.stringify(response.data, null, 2))
    } else {
      console.log('响应结构异常:', response)
    }

    const data = response.data
    console.log('提取的data:', data)
    console.log('data.success:', data?.success)
    console.log('data.error_message:', data?.error_message)

    generateResult.value = data
    generateModalVisible.value = false
    resultModalVisible.value = true

    if (data.success) {
      // 验证生成的用例数据
      console.log('生成的用例数据验证:')
      console.log('- 用例数量:', data.generated_cases.length)
      console.log('- 用例数据:', data.generated_cases)

      // 过滤掉无效的用例数据
      const validCases = data.generated_cases.filter(testCase => {
        const isValid = testCase &&
                       typeof testCase === 'object' &&
                       testCase.case_name &&
                       testCase.test_steps &&
                       testCase.expected_result
        if (!isValid) {
          console.warn('发现无效的测试用例数据:', testCase)
        }
        return isValid
      })

      console.log('有效用例数量:', validCases.length)

      // 更新生成结果，只保留有效的用例
      generateResult.value.generated_cases = validCases

      // 清空选择状态，让用户手动选择
      selectedCases.value = []
      console.log('生成成功，清空选择状态，用例数量:', validCases.length)
      $message.success(`生成成功，共 ${validCases.length} 个有效测试用例`)
    } else {
      // 处理错误信息，可能是数组格式
      console.log('进入错误处理分支')
      console.log('错误信息类型:', typeof data.error_message)
      console.log('错误信息内容:', data.error_message)
      console.log('错误信息详细:', JSON.stringify(data.error_message, null, 2))

      let errorMsg = data.error_message || '未知错误'

      // 更智能的错误信息处理
      if (Array.isArray(errorMsg)) {
        console.log('错误信息是数组，长度:', errorMsg.length)
        // 递归展平所有嵌套数组并提取字符串
        const flattenErrors = (arr) => {
          return arr.reduce((acc, item) => {
            if (Array.isArray(item)) {
              return acc.concat(flattenErrors(item))
            } else if (typeof item === 'string') {
              return acc.concat(item)
            } else if (typeof item === 'object' && item !== null) {
              return acc.concat(JSON.stringify(item))
            } else {
              return acc.concat(String(item))
            }
          }, [])
        }

        const flatErrors = flattenErrors(errorMsg)
        errorMsg = flatErrors.join('; ') || '生成过程中出现错误'
      }

      $message.error(`生成失败: ${errorMsg}`)
    }
  } catch (error) {
    console.error('=== 进入catch块 ===')
    console.error('错误对象:', error)
    console.error('错误类型:', typeof error)
    console.error('错误构造函数:', error.constructor?.name)
    console.error('错误是否为数组:', Array.isArray(error))

    // 处理错误对象是数组的异常情况
    if (Array.isArray(error)) {
      console.error('错误对象是数组，这是异常情况!')
      console.error('数组长度:', error.length)
      console.error('数组内容:', error)

      // 尝试从数组中提取有用信息
      let errorMessage = '生成失败'
      const flattenArray = (arr) => {
        return arr.reduce((acc, item) => {
          if (Array.isArray(item)) {
            return acc.concat(flattenArray(item))
          } else if (typeof item === 'string') {
            return acc.concat(item)
          } else if (item && typeof item === 'object') {
            if (item.message) return acc.concat(item.message)
            if (item.msg) return acc.concat(item.msg)
            return acc.concat(JSON.stringify(item))
          } else if (item !== null && item !== undefined) {
            return acc.concat(String(item))
          }
          return acc
        }, [])
      }

      const messages = flattenArray(error)
      if (messages.length > 0) {
        errorMessage = `生成失败: ${messages.join('; ')}`
      }

      console.error('从数组提取的错误信息:', errorMessage)
      $message.error(errorMessage)
      return
    }

    console.error('错误是否有response:', 'response' in error)
    console.error('错误是否有message:', 'message' in error)
    console.error('错误的所有属性:', Object.keys(error))

    if (error.response) {
      console.error('error.response:', error.response)
      console.error('error.response.data:', error.response.data)
      console.error('error.response.status:', error.response.status)
    }

    if (error.message) {
      console.error('error.message:', error.message)
    }

    // 更详细的错误信息
    let errorMessage = '生成失败'

    // 检查是否是我们自定义的错误格式（来自HTTP拦截器）
    if (error.code && error.message && error.error) {
      console.error('检测到自定义错误格式:', error)
      errorMessage = error.message
    } else if (error.response) {
      // 服务器响应了错误状态码
      const status = error.response.status
      const data = error.response.data

      if (status === 401) {
        errorMessage = '请先登录'
      } else if (status === 403) {
        errorMessage = '权限不足'
      } else if (status === 422) {
        errorMessage = '请求参数错误'
        if (data && data.msg) {
          // 后端返回的错误信息格式: "RequestValidationError, [{'type': '...', 'msg': '...'}]"
          let msg = data.msg
          if (msg.includes('RequestValidationError')) {
            // 提取有用的错误信息
            const match = msg.match(/\[.*\]/)
            if (match) {
              try {
                const errors = JSON.parse(match[0].replace(/'/g, '"'))
                const errorMsgs = errors.map(err => err.msg || err.type).join('; ')
                errorMessage += `: ${errorMsgs}`
              } catch (e) {
                errorMessage += `: ${msg}`
              }
            } else {
              errorMessage += `: ${msg}`
            }
          } else {
            errorMessage += `: ${msg}`
          }
        }
      } else if (data && data.msg) {
        errorMessage = data.msg
      } else {
        errorMessage = `请求失败 (${status})`
      }
    } else if (error.request) {
      // 请求发出但没有收到响应
      errorMessage = '网络连接失败'
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }

    console.error('最终错误信息:', errorMessage)
    $message.error(errorMessage)
  } finally {
    generateLoading.value = false
  }
}

// 保存测试用例
async function handleSaveTestCases() {
  try {
    if (selectedCases.value.length === 0) {
      $message.warning('请选择要保存的测试用例')
      return
    }

    console.log('=== 开始保存测试用例 ===')
    console.log('选中的ID列表:', selectedCases.value)
    console.log('生成的用例总数:', generateResult.value.generated_cases.length)
    console.log('生成的用例数据:', generateResult.value.generated_cases)

    saveLoading.value = true

    // 只保存选中的测试用例，selectedCases存储的是测试用例的ID
    const selectedTestCases = selectedCases.value
      .map(selectedId => {
        // 根据ID查找对应的测试用例
        const testCase = generateResult.value.generated_cases.find(tc => tc.id === selectedId)
        if (!testCase) {
          console.log(`未找到ID为 ${selectedId} 的测试用例`)
          return null
        }
        console.log(`找到ID为 ${selectedId} 的测试用例:`, testCase)
        return testCase
      })
      .filter(testCase => {
        const isValid = testCase != null && testCase.case_name
        console.log('测试用例是否有效:', isValid, testCase)
        return isValid
      })

    console.log('过滤后的选中用例:', selectedTestCases)

    if (selectedTestCases.length === 0) {
      $message.warning('没有有效的测试用例可以保存')
      return
    }

    const requestData = {
      task_id: generateResult.value.task_id,
      cases: selectedTestCases
    }

    console.log('发送保存请求:', requestData)

    const { data } = await api.saveGeneratedTestCases(requestData)

    $message.success(`成功保存 ${data.saved_count} 个测试用例`)
    resultModalVisible.value = false

    // 刷新任务列表
    await $table.value?.handleSearch()

    // 自动打开任务详情，显示刚才保存的任务
    setTimeout(async () => {
      try {
        const { data: taskData } = await api.getGenerationTask(generateResult.value.task_id)
        taskDetail.value = taskData
        // taskDetailModalVisible.value = true
      } catch (error) {
        console.error('获取任务详情失败:', error)
      }
    }, 500) // 延迟500ms确保列表刷新完成
  } catch (error) {
    console.error('保存失败:', error)
    console.error('错误详情:', error.response?.data || error.message)
    $message.error(`保存失败: ${error.response?.data?.msg || error.message || '未知错误'}`)
  } finally {
    saveLoading.value = false
  }
}

// 处理选择变化
function handleCheckedRowKeysChange(keys) {
  console.log('选择变化:', keys)
  console.log('当前总用例数:', generateResult.value.generated_cases.length)
  selectedCases.value = keys
  console.log('更新后选中数量:', selectedCases.value.length)
}



// 查看任务
async function handleViewTask(row) {
  try {
    const { data } = await api.getGenerationTask(row.id)
    console.log('任务详情:', data)
    taskDetail.value = data
    taskDetailModalVisible.value = true
  } catch (error) {
    console.error('查看任务失败:', error)
    $message.error('获取任务详情失败')
  }
}

// 删除任务
async function handleDeleteTask(id) {
  try {
    await api.deleteGenerationTask(id)
    $message.success('删除成功')
    $table.value?.handleSearch()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 获取等级类型
function getLevelType(level) {
  const typeMap = {
    high: 'error',
    medium: 'warning',
    low: 'info'
  }
  return typeMap[level] || 'default'
}

// 获取等级文本
function getLevelText(level) {
  const textMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[level] || level
}

// 获取状态类型
function getStatusType(status) {
  const typeMap = {
    pending: 'default',
    generating: 'info',
    completed: 'success',
    failed: 'error'
  }
  return typeMap[status] || 'default'
}

// 获取状态文本
function getStatusText(status) {
  const textMap = {
    pending: '待生成',
    generating: '生成中',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[status] || status
}
</script>

<style scoped>
.test-case-item {
  margin-bottom: 16px;
}

.test-case-item:last-child {
  margin-bottom: 0;
}

pre {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}
</style>
