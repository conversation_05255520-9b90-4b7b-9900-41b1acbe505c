<template>
  <div class="json-tools-page">
    <n-card title="JSON工具箱" size="small">
      <template #header-extra>
        <n-space>
          <n-button type="primary" @click="loadExamples" :loading="loading">
            <component :is="renderIcon('mdi:lightbulb-outline', { size: 16 })" />
            加载示例
          </n-button>
          <n-button @click="clearAll">
            <component :is="renderIcon('mdi:broom', { size: 16 })" />
            清空所有
          </n-button>
        </n-space>
      </template>

      <n-tabs v-model:value="activeTab" type="line" animated>
        <!-- JSON格式化 -->
        <n-tab-pane name="format" tab="JSON格式化">
          <n-grid :cols="2" :x-gap="16" style="height: calc(100vh - 200px);">
            <n-grid-item>
              <n-card title="输入JSON" size="small" style="height: 100%;">
                <template #header-extra>
                  <n-space>
                    <n-checkbox v-model:checked="formatOptions.sort_keys">排序键名</n-checkbox>
                    <n-input-number
                      v-model:value="formatOptions.indent"
                      :min="0"
                      :max="8"
                      placeholder="缩进"
                      style="width: 80px"
                    />
                    <n-button type="primary" @click="formatJson" :loading="formatLoading">
                      <component :is="renderIcon('mdi:code-braces', { size: 16 })" />
                      格式化
                    </n-button>
                    <n-button @click="compressJson" :loading="compressLoading">
                      <component :is="renderIcon('mdi:compress', { size: 16 })" />
                      压缩
                    </n-button>
                  </n-space>
                </template>
                <n-input
                  v-model:value="jsonInput"
                  type="textarea"
                  placeholder="请输入JSON数据..."
                  style="height: calc(100vh - 280px); overflow-y: auto;"
                />
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card title="输出结果" size="small" style="height: 100%;">
                <template #header-extra>
                  <n-space v-if="formatResult">
                    <n-tag type="info">
                      原始: {{ formatResult.original_size }} 字符
                    </n-tag>
                    <n-tag type="success">
                      格式化: {{ formatResult.formatted_size }} 字符
                    </n-tag>
                    <n-tag :type="formatResult.compression_ratio > 0 ? 'warning' : 'default'">
                      压缩率: {{ formatResult.compression_ratio }}%
                    </n-tag>
                    <n-button size="small" @click="copyToClipboard(formatResult.formatted_json)">
                      <component :is="renderIcon('mdi:content-copy', { size: 14 })" />
                      复制
                    </n-button>
                  </n-space>
                </template>
                <n-input
                  :value="formatResult?.formatted_json || ''"
                  type="textarea"
                  readonly
                  placeholder="格式化结果将显示在这里..."
                  style="height: calc(100vh - 280px); overflow-y: auto;"
                />
              </n-card>
            </n-grid-item>
          </n-grid>
        </n-tab-pane>

        <!-- JSON验证 -->
        <n-tab-pane name="validate" tab="JSON验证">
          <n-grid :cols="2" :x-gap="16" style="height: calc(100vh - 200px);">
            <n-grid-item>
              <n-card title="JSON验证" size="small" style="height: 100%;">
                <template #header-extra>
                  <n-button type="primary" @click="validateJson" :loading="validateLoading">
                    <component :is="renderIcon('mdi:check-circle-outline', { size: 16 })" />
                    验证
                  </n-button>
                </template>
                <n-input
                  v-model:value="validateInput"
                  type="textarea"
                  placeholder="请输入要验证的JSON数据..."
                  style="height: calc(100vh - 280px); overflow-y: auto;"
                />
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card title="验证结果" size="small" style="height: 100%;">
                <div style="height: calc(100vh - 280px); display: flex; align-items: center; justify-content: center;">
                  <n-result
                    v-if="validateResult"
                    :status="validateResult.is_valid ? 'success' : 'error'"
                    :title="validateResult.is_valid ? 'JSON格式正确' : 'JSON格式错误'"
                  >
                    <template #footer v-if="!validateResult.is_valid">
                      <n-alert type="error" :show-icon="false">
                        <div>错误信息: {{ validateResult.error_message }}</div>
                        <div v-if="validateResult.error_line">
                          错误位置: 第 {{ validateResult.error_line }} 行，第 {{ validateResult.error_column }} 列
                        </div>
                      </n-alert>
                    </template>
                  </n-result>
                  <n-empty v-else description="验证结果将显示在这里" />
                </div>
              </n-card>
            </n-grid-item>
          </n-grid>
        </n-tab-pane>

        <!-- 格式转换 -->
        <n-tab-pane name="convert" tab="格式转换">
          <n-grid :cols="2" :x-gap="16" style="height: calc(100vh - 200px);">
            <n-grid-item>
              <n-card title="格式转换" size="small" style="height: 100%;">
                <template #header-extra>
                  <n-space>
                    <n-select
                      v-model:value="convertOptions.source_format"
                      :options="formatTypeOptions"
                      placeholder="源格式"
                      style="width: 100px"
                    />
                    <component :is="renderIcon('mdi:arrow-right', { size: 16 })" />
                    <n-select
                      v-model:value="convertOptions.target_format"
                      :options="formatTypeOptions"
                      placeholder="目标格式"
                      style="width: 100px"
                    />
                    <n-input
                      v-model:value="convertOptions.csv_delimiter"
                      placeholder="CSV分隔符"
                      style="width: 80px"
                      v-if="convertOptions.source_format === 'csv' || convertOptions.target_format === 'csv'"
                    />
                    <n-button type="primary" @click="convertFormat" :loading="convertLoading">
                      <component :is="renderIcon('mdi:swap-horizontal', { size: 16 })" />
                      转换
                    </n-button>
                  </n-space>
                </template>
                <n-input
                  v-model:value="convertInput"
                  type="textarea"
                  placeholder="请输入要转换的数据..."
                  style="height: calc(100vh - 280px); overflow-y: auto;"
                />
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card title="转换结果" size="small" style="height: 100%;">
                <template #header-extra>
                  <n-space v-if="convertResult">
                    <n-tag type="info">
                      {{ convertResult.source_format }} → {{ convertResult.target_format }}
                    </n-tag>
                    <n-button size="small" @click="copyToClipboard(convertResult.converted_data)">
                      <component :is="renderIcon('mdi:content-copy', { size: 14 })" />
                      复制
                    </n-button>
                  </n-space>
                </template>
                <n-input
                  :value="convertResult?.converted_data || ''"
                  type="textarea"
                  readonly
                  placeholder="转换结果将显示在这里..."
                  style="height: calc(100vh - 280px); overflow-y: auto;"
                />
              </n-card>
            </n-grid-item>
          </n-grid>
        </n-tab-pane>

        <!-- JSONPath查询 -->
        <n-tab-pane name="jsonpath" tab="JSONPath查询">
          <n-grid :cols="2" :x-gap="16" style="height: calc(100vh - 200px);">
            <n-grid-item>
              <n-card title="JSONPath查询" size="small" style="height: 100%;">
                <template #header-extra>
                  <n-button type="primary" @click="queryJsonPath" :loading="jsonPathLoading">
                    <component :is="renderIcon('mdi:magnify', { size: 16 })" />
                    查询
                  </n-button>
                </template>
                <n-space vertical style="height: calc(100vh - 280px);">
                  <n-input
                    v-model:value="jsonPathOptions.json_path"
                    placeholder="请输入JSONPath表达式，如: $.name 或 $.hobbies[*]"
                  />
                  <n-input
                    v-model:value="jsonPathInput"
                    type="textarea"
                    placeholder="请输入JSON数据..."
                    style="flex: 1; overflow-y: auto;"
                  />
                </n-space>
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card title="查询结果" size="small" style="height: 100%;">
                <template #header-extra>
                  <n-tag v-if="jsonPathResult" type="success">
                    找到 {{ jsonPathResult.count }} 个结果
                  </n-tag>
                </template>
                <div style="height: calc(100vh - 280px); overflow-y: auto;">
                  <n-list v-if="jsonPathResult && jsonPathResult.results.length > 0">
                    <n-list-item v-for="(result, index) in jsonPathResult.results" :key="index">
                      <n-thing>
                        <template #header>结果 {{ index + 1 }}</template>
                        <n-code :code="JSON.stringify(result, null, 2)" language="json" />
                      </n-thing>
                    </n-list-item>
                  </n-list>
                  <n-empty v-else description="查询结果将显示在这里" />
                </div>
              </n-card>
            </n-grid-item>
          </n-grid>
        </n-tab-pane>

        <!-- JSON对比 -->
        <n-tab-pane name="compare" tab="JSON对比">
          <n-grid :cols="2" :x-gap="16" style="height: calc(100vh - 200px);">
            <n-grid-item>
              <n-card title="JSON 1" size="small" style="height: 100%;">
                <template #header-extra>
                  <n-space>
                    <n-button
                      v-if="compareResult"
                      text
                      :type="compareResult.is_equal ? 'success' : 'warning'"
                      @click="showCompareDetails = true"
                      style="padding: 4px;"
                    >
                      <template #icon>
                        <n-icon size="20">
                          <component :is="renderIcon(compareResult.is_equal ? 'mdi:check-circle' : 'mdi:alert-circle')" />
                        </n-icon>
                      </template>
                      {{ compareResult.is_equal ? '相同' : '不同' }}
                    </n-button>
                    <n-button type="primary" @click="compareJson" :loading="compareLoading">
                      <template #icon>
                        <n-icon>
                          <component :is="renderIcon('mdi:compare')" />
                        </n-icon>
                      </template>
                      对比
                    </n-button>
                  </n-space>
                </template>
                <n-input
                  v-model:value="compareOptions.json1"
                  type="textarea"
                  placeholder="请输入第一个JSON..."
                  style="height: calc(100vh - 280px); overflow-y: auto;"
                />
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card title="JSON 2" size="small" style="height: 100%;">
                <n-input
                  v-model:value="compareOptions.json2"
                  type="textarea"
                  placeholder="请输入第二个JSON..."
                  style="height: calc(100vh - 280px); overflow-y: auto;"
                />
              </n-card>
            </n-grid-item>
          </n-grid>

          <!-- 对比结果详情弹窗 -->
          <n-modal v-model:show="showCompareDetails" preset="card" title="对比结果详情" style="width: 800px;">
            <div style="max-height: 500px; overflow-y: auto;">
              <n-result
                v-if="compareResult"
                :status="compareResult.is_equal ? 'success' : 'warning'"
                :title="compareResult.is_equal ? 'JSON内容相同' : 'JSON内容不同'"
              >
                <template #footer v-if="!compareResult.is_equal && compareResult.differences.length > 0">
                  <n-list>
                    <n-list-item v-for="(diff, index) in compareResult.differences" :key="index">
                      <n-thing>
                        <template #header>差异 {{ index + 1 }}</template>
                        <template #description>
                          <n-tag type="warning">{{ diff.type }}</n-tag>
                          <span v-if="diff.path"> - {{ diff.path }}</span>
                        </template>
                        <div>{{ diff.change }}</div>
                      </n-thing>
                    </n-list-item>
                  </n-list>
                </template>
              </n-result>
            </div>
          </n-modal>
        </n-tab-pane>

        <!-- JSON统计 -->
        <n-tab-pane name="stats" tab="JSON统计">
          <n-grid :cols="2" :x-gap="16" style="height: calc(100vh - 200px);">
            <n-grid-item>
              <n-card title="JSON统计分析" size="small" style="height: 100%;">
                <template #header-extra>
                  <n-button type="primary" @click="getJsonStats" :loading="statsLoading">
                    <component :is="renderIcon('mdi:chart-bar', { size: 16 })" />
                    分析
                  </n-button>
                </template>
                <n-input
                  v-model:value="statsInput"
                  type="textarea"
                  placeholder="请输入要分析的JSON数据..."
                  style="height: calc(100vh - 280px); overflow-y: auto;"
                />
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card title="统计结果" size="small" style="height: 100%;">
                <div style="height: calc(100vh - 280px); overflow-y: auto;">
                  <div v-if="statsResult">
                    <n-grid :cols="2" :x-gap="12" :y-gap="12" style="margin-bottom: 16px;">
                      <n-grid-item>
                        <n-statistic label="总键数" :value="statsResult.total_keys" />
                      </n-grid-item>
                      <n-grid-item>
                        <n-statistic label="总值数" :value="statsResult.total_values" />
                      </n-grid-item>
                      <n-grid-item>
                        <n-statistic label="最大深度" :value="statsResult.max_depth" />
                      </n-grid-item>
                      <n-grid-item>
                        <n-statistic label="字符数" :value="statsResult.size_info.characters" />
                      </n-grid-item>
                    </n-grid>

                    <n-divider />

                    <n-space vertical size="large">
                      <n-card title="数据类型分布" size="small">
                        <n-list>
                          <n-list-item v-for="(count, type) in statsResult.data_types" :key="type">
                            <n-thing>
                              <template #header>{{ type }}</template>
                              <template #description>{{ count }} 个</template>
                            </n-thing>
                          </n-list-item>
                        </n-list>
                      </n-card>

                      <n-card title="大小信息" size="small">
                        <n-list>
                          <n-list-item>
                            <n-thing>
                              <template #header>字符数</template>
                              <template #description>{{ statsResult.size_info.characters }}</template>
                            </n-thing>
                          </n-list-item>
                          <n-list-item>
                            <n-thing>
                              <template #header>字节数</template>
                              <template #description>{{ statsResult.size_info.bytes }}</template>
                            </n-thing>
                          </n-list-item>
                          <n-list-item>
                            <n-thing>
                              <template #header>行数</template>
                              <template #description>{{ statsResult.size_info.lines }}</template>
                            </n-thing>
                          </n-list-item>
                        </n-list>
                      </n-card>
                    </n-space>
                  </div>
                  <n-empty v-else description="统计结果将显示在这里" />
                </div>
              </n-card>
            </n-grid-item>
          </n-grid>
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useMessage } from 'naive-ui'
import { renderIcon } from '@/utils'
import api from '@/api/jsonTools'

const message = useMessage()

// 响应式数据
const loading = ref(false)
const activeTab = ref('format')

// JSON格式化
const jsonInput = ref('')
const formatResult = ref(null)
const formatLoading = ref(false)
const compressLoading = ref(false)
const formatOptions = reactive({
  indent: 2,
  sort_keys: false
})

// JSON验证
const validateInput = ref('')
const validateResult = ref(null)
const validateLoading = ref(false)

// 格式转换
const convertInput = ref('')
const convertResult = ref(null)
const convertLoading = ref(false)
const convertOptions = reactive({
  source_format: 'json',
  target_format: 'xml',
  csv_delimiter: ','
})

const formatTypeOptions = [
  { label: 'JSON', value: 'json' },
  { label: 'XML', value: 'xml' },
  { label: 'YAML', value: 'yaml' },
  { label: 'CSV', value: 'csv' }
]

// JSONPath查询
const jsonPathInput = ref('')
const jsonPathResult = ref(null)
const jsonPathLoading = ref(false)
const jsonPathOptions = reactive({
  json_path: '$.name'
})

// JSON对比
const compareResult = ref(null)
const compareLoading = ref(false)
const showCompareDetails = ref(false)
const compareOptions = reactive({
  json1: '',
  json2: ''
})

// JSON统计
const statsInput = ref('')
const statsResult = ref(null)
const statsLoading = ref(false)

// 方法
const formatJson = async () => {
  if (!jsonInput.value.trim()) {
    message.warning('请输入JSON数据')
    return
  }

  formatLoading.value = true
  try {
    const { data } = await api.formatJson({
      json_data: jsonInput.value,
      indent: formatOptions.indent,
      sort_keys: formatOptions.sort_keys
    })
    formatResult.value = data
    message.success('JSON格式化成功')
  } catch (error) {
    message.error(error.message || 'JSON格式化失败')
  } finally {
    formatLoading.value = false
  }
}

const compressJson = async () => {
  if (!jsonInput.value.trim()) {
    message.warning('请输入JSON数据')
    return
  }

  compressLoading.value = true
  try {
    const { data } = await api.compressJson({
      json_data: jsonInput.value
    })
    formatResult.value = data
    message.success('JSON压缩成功')
  } catch (error) {
    message.error(error.message || 'JSON压缩失败')
  } finally {
    compressLoading.value = false
  }
}

const validateJson = async () => {
  if (!validateInput.value.trim()) {
    message.warning('请输入要验证的JSON数据')
    return
  }

  validateLoading.value = true
  try {
    const { data } = await api.validateJson({
      json_data: validateInput.value
    })
    validateResult.value = data
    message.success('JSON验证完成')
  } catch (error) {
    message.error(error.message || 'JSON验证失败')
  } finally {
    validateLoading.value = false
  }
}

const convertFormat = async () => {
  if (!convertInput.value.trim()) {
    message.warning('请输入要转换的数据')
    return
  }

  if (!convertOptions.source_format || !convertOptions.target_format) {
    message.warning('请选择源格式和目标格式')
    return
  }

  convertLoading.value = true
  try {
    const { data } = await api.convertFormat({
      data: convertInput.value,
      source_format: convertOptions.source_format,
      target_format: convertOptions.target_format,
      csv_delimiter: convertOptions.csv_delimiter
    })
    convertResult.value = data
    message.success('格式转换成功')
  } catch (error) {
    message.error(error.message || '格式转换失败')
  } finally {
    convertLoading.value = false
  }
}

const queryJsonPath = async () => {
  if (!jsonPathInput.value.trim()) {
    message.warning('请输入JSON数据')
    return
  }

  if (!jsonPathOptions.json_path.trim()) {
    message.warning('请输入JSONPath表达式')
    return
  }

  jsonPathLoading.value = true
  try {
    const { data } = await api.queryJsonPath({
      json_data: jsonPathInput.value,
      json_path: jsonPathOptions.json_path
    })
    jsonPathResult.value = data
    message.success('JSONPath查询成功')
  } catch (error) {
    message.error(error.message || 'JSONPath查询失败')
  } finally {
    jsonPathLoading.value = false
  }
}

const compareJson = async () => {
  if (!compareOptions.json1.trim() || !compareOptions.json2.trim()) {
    message.warning('请输入两个JSON数据')
    return
  }

  compareLoading.value = true
  try {
    const { data } = await api.compareJson({
      json1: compareOptions.json1,
      json2: compareOptions.json2
    })
    compareResult.value = data
    message.success('JSON对比完成')
  } catch (error) {
    message.error(error.message || 'JSON对比失败')
  } finally {
    compareLoading.value = false
  }
}

const getJsonStats = async () => {
  if (!statsInput.value.trim()) {
    message.warning('请输入JSON数据')
    return
  }

  statsLoading.value = true
  try {
    const { data } = await api.getJsonStats({
      json_data: statsInput.value
    })
    statsResult.value = data
    message.success('JSON统计完成')
  } catch (error) {
    message.error(error.message || 'JSON统计失败')
  } finally {
    statsLoading.value = false
  }
}

const loadExamples = async () => {
  loading.value = true
  try {
    const { data } = await api.getExamples()

    // 根据当前标签页加载对应示例
    switch (activeTab.value) {
      case 'format':
      case 'validate':
      case 'stats':
        jsonInput.value = data.json.data
        validateInput.value = data.json.data
        statsInput.value = data.json.data
        break
      case 'convert':
        convertInput.value = data.json.data
        break
      case 'jsonpath':
        jsonPathInput.value = data.json.data
        jsonPathOptions.json_path = data.jsonpath.expressions[0]
        break
      case 'compare':
        compareOptions.json1 = data.json.data
        compareOptions.json2 = data.json.data.replace('张三', '李四').replace('30', '25')
        break
    }

    message.success('示例数据加载成功')
  } catch (error) {
    message.error(error.message || '加载示例数据失败')
  } finally {
    loading.value = false
  }
}

const clearAll = () => {
  jsonInput.value = ''
  validateInput.value = ''
  convertInput.value = ''
  jsonPathInput.value = ''
  statsInput.value = ''
  compareOptions.json1 = ''
  compareOptions.json2 = ''

  formatResult.value = null
  validateResult.value = null
  convertResult.value = null
  jsonPathResult.value = null
  compareResult.value = null
  statsResult.value = null

  message.success('已清空所有数据')
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    message.success('已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}
</script>

<style scoped>
.json-tools-page {
  padding: 16px;
}

.json-tools-page :deep(.n-card) {
  margin-bottom: 16px;
}

.json-tools-page :deep(.n-input) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.json-tools-page :deep(.n-code) {
  max-height: 300px;
  overflow-y: auto;
}

.json-tools-page :deep(.n-statistic) {
  text-align: center;
}

.json-tools-page :deep(.n-thing-header) {
  font-weight: 600;
}
</style>
