<template>
  <AppPage :show-footer="false">
    <div flex-1>
      <!-- 用户信息卡片 -->
      <n-card rounded-10>
        <div flex items-center justify-between>
          <div flex items-center>
            <!-- 使用用户名头像 -->
            <UserAvatarComponent
              :username="userStore.name"
              :size="60"
              :glow="true"
              :gradient="true"
            />
            <div ml-10>
              <p text-20 font-semibold>
                {{ $t('views.workbench.text_hello', { username: userStore.name }) }}
              </p>
              <p mt-5 text-14 op-60>{{ $t('views.workbench.text_welcome') }}</p>
            </div>
          </div>
          <n-space :size="12" :wrap="false">
            <n-statistic v-for="item in overviewStats" :key="item.id" v-bind="item"></n-statistic>
          </n-space>
        </div>
      </n-card>

      <!-- 定时任务统计 -->
      <n-grid :cols="1" :x-gap="16" mt-15>
        <!-- 定时任务状态分布 -->
<!--        <n-grid-item>-->
<!--          <ChartCard-->
<!--            title="定时任务状态分布"-->
<!--            type="pie"-->
<!--            :data="taskStatusData"-->
<!--            :loading="loading"-->
<!--            center-label="任务"-->
<!--            @refresh="fetchScheduledTaskStats"-->
<!--          />-->
<!--        </n-grid-item>-->

        <!-- 定时任务概览统计 -->
        <n-grid-item>
          <ChartCard
            title="定时任务概览"
            type="stats"
            :data="taskOverviewData"
            :loading="loading"
            @refresh="fetchScheduledTaskStats"
          />
        </n-grid-item>

        <!-- 最近执行记录 -->
        <n-grid-item>
          <ChartCard
            title="最近执行记录"
            type="list"
            :data="recentExecutionsData"
            :loading="loading"
            :max-items="8"
            @refresh="fetchScheduledTaskStats"
          />
        </n-grid-item>
      </n-grid>

      <!-- 测试计划统计 -->
      <n-grid :cols="2" :x-gap="16" mt-15>
        <!-- 接口测试计划统计 -->
        <n-grid-item>
          <n-card
            :title="$t('views.workbench.label_api_test_plan')"
            size="small"
            :segmented="true"
            rounded-10
          >
            <template #header-extra>
              <n-button quaternary circle size="small" @click="fetchApiTestPlanStats" :loading="loading">
                <template #icon>
                  <n-icon><icon-mdi-refresh /></n-icon>
                </template>
              </n-button>
            </template>

            <n-spin :show="loading">
              <!-- 项目统计图表 -->
              <div v-if="apiTestPlanStats.length > 0" mb-4>
                <ChartCard
                  title="项目执行统计"
                  type="bar"
                  :data="apiTestPlanChartData"
                  :loading="loading"
                />
              </div>

              <!-- 详细项目信息 -->
              <div v-if="apiTestPlanStats.length > 0">
                <div
                  v-for="project in apiTestPlanStats"
                  :key="project.project_id"
                  class="project-card"
                >
                  <div flex items-center justify-between mb-3>
                    <h3 text-lg font-semibold>{{ project.project_name }}</h3>
                    <n-tag :type="project.total_plans > 0 ? 'info' : 'default'" size="small">
                      {{ $t('views.workbench.label_total_plans') }}: {{ project.total_plans }}
                    </n-tag>
                  </div>

                  <n-grid :cols="4" :x-gap="12" mb-3>
                    <n-grid-item>
                      <n-statistic
                        :label="$t('views.workbench.label_executed_plans')"
                        :value="project.executed_plans"
                        :value-style="{ color: '#1890ff' }"
                      />
                    </n-grid-item>
                    <n-grid-item>
                      <n-statistic
                        :label="$t('views.workbench.label_success_plans')"
                        :value="project.success_plans"
                        :value-style="{ color: '#52c41a' }"
                      />
                    </n-grid-item>
                    <n-grid-item>
                      <n-statistic
                        :label="$t('views.workbench.label_failed_plans')"
                        :value="project.failed_plans"
                        :value-style="{ color: '#ff4d4f' }"
                      />
                    </n-grid-item>
                    <n-grid-item>
                      <n-statistic
                        :label="$t('views.workbench.label_avg_pass_rate')"
                        :value="project.avg_pass_rate + '%'"
                        :value-style="{ color: project.avg_pass_rate >= 80 ? '#52c41a' : project.avg_pass_rate >= 60 ? '#faad14' : '#ff4d4f' }"
                      />
                    </n-grid-item>
                  </n-grid>

                  <div text-sm op-60>
                    {{ $t('views.workbench.label_last_execution') }}:
                    {{ project.last_execution || $t('views.workbench.text_no_execution') }}
                  </div>
                </div>
              </div>
              <div v-else text-center py-8 op-60>
                暂无接口测试计划数据
              </div>
            </n-spin>
          </n-card>
        </n-grid-item>

        <!-- 功能测试计划统计 -->
        <n-grid-item>
          <n-card
            :title="$t('views.workbench.label_functional_test_plan')"
            size="small"
            :segmented="true"
            rounded-10
          >
            <template #header-extra>
              <n-button quaternary circle size="small" @click="fetchFunctionalTestPlanStats" :loading="functionalLoading">
                <template #icon>
                  <n-icon><icon-mdi-refresh /></n-icon>
                </template>
              </n-button>
            </template>

            <n-spin :show="functionalLoading">
              <div v-if="functionalTestPlanStats.length > 0">
                <!-- 状态分布图表 -->
                <div mb-4>
                  <ChartCard
                    title="状态分布"
                    type="pie"
                    :data="functionalTestPlanChartData"
                    :loading="functionalLoading"
                    center-label="计划"
                  />
                </div>

                <!-- 项目详情 -->
                <div
                  v-for="project in functionalTestPlanStats"
                  :key="project.project_id"
                  class="project-card"
                >
                  <div flex items-center justify-between mb-3>
                    <h3 text-lg font-semibold>{{ project.project_name }}</h3>
                    <n-tag :type="project.total_plans > 0 ? 'info' : 'default'" size="small">
                      总计划: {{ project.total_plans }}
                    </n-tag>
                  </div>

                  <n-grid :cols="3" :x-gap="12" mb-3>
                    <n-grid-item>
                      <n-statistic
                        label="未开始"
                        :value="project.not_started_plans"
                        :value-style="{ color: '#d9d9d9' }"
                      />
                    </n-grid-item>
                    <n-grid-item>
                      <n-statistic
                        label="进行中"
                        :value="project.in_progress_plans"
                        :value-style="{ color: '#1890ff' }"
                      />
                    </n-grid-item>
                    <n-grid-item>
                      <n-statistic
                        label="已完成"
                        :value="project.completed_plans"
                        :value-style="{ color: '#52c41a' }"
                      />
                    </n-grid-item>
                  </n-grid>
                </div>
              </div>
              <div v-else text-center py-8 op-60>
                暂无功能测试计划数据
              </div>
            </n-spin>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>
  </AppPage>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store'
import { useI18n } from 'vue-i18n'
import apiTestPlanApi from '@/api/apiTestPlan'
import functionalTestPlanApi from '@/api/functionalTestPlan'
import scheduledTaskApi from '@/api/scheduledTask'
import UserAvatarComponent from '@/components/common/UserAvatar.vue'
import ChartCard from '@/components/common/ChartCard.vue'

const { t } = useI18n({ useScope: 'global' })
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const functionalLoading = ref(false)
const apiTestPlanStats = ref([])
const functionalTestPlanStats = ref([])
const scheduledTaskStats = ref({
  overview: {
    total_tasks: 0,
    active_tasks: 0,
    inactive_tasks: 0,
    total_executions: 0
  },
  project_stats: [],
  recent_executions: [],
  task_status_distribution: {
    active: 0,
    inactive: 0
  }
})

// 概览统计数据
const overviewStats = computed(() => {
  const totalProjects = apiTestPlanStats.value.length
  const totalPlans = apiTestPlanStats.value.reduce((sum, project) => sum + project.total_plans, 0)
  const totalExecuted = apiTestPlanStats.value.reduce((sum, project) => sum + project.executed_plans, 0)
  const totalTasks = scheduledTaskStats.value.overview.total_tasks

  return [
    {
      id: 0,
      label: t('views.workbench.label_number_of_items'),
      value: totalProjects.toString(),
    },
    {
      id: 1,
      label: t('views.workbench.label_total_plans'),
      value: totalPlans.toString(),
    },
    {
      id: 2,
      label: t('views.workbench.label_executed_plans'),
      value: totalExecuted.toString(),
    },
    {
      id: 3,
      label: '定时任务',
      value: totalTasks.toString(),
    },
  ]
})

// 定时任务状态分布数据
const taskStatusData = computed(() => {
  const { active, inactive } = scheduledTaskStats.value.task_status_distribution
  return [
    { name: '活跃任务', value: active },
    { name: '非活跃任务', value: inactive }
  ].filter(item => item.value > 0)
})

// 定时任务概览数据
const taskOverviewData = computed(() => {
  const overview = scheduledTaskStats.value.overview
  return [
    {
      label: '总任务数',
      value: overview.total_tasks,
      icon: 'icon-mdi-clock-outline'
    },
    {
      label: '活跃任务',
      value: overview.active_tasks,
      icon: 'icon-mdi-play-circle-outline'
    },
    {
      label: '总执行次数',
      value: overview.total_executions,
      icon: 'icon-mdi-chart-line'
    }
  ]
})

// 最近执行记录数据
const recentExecutionsData = computed(() => {
  return scheduledTaskStats.value.recent_executions.map(execution => ({
    title: execution.task_name,
    subtitle: `${execution.project_name} - ${execution.plan_name}`,
    extra: execution.run_count ? `执行${execution.run_count}次` : '未执行'
  }))
})

// 接口测试计划图表数据
const apiTestPlanChartData = computed(() => {
  return apiTestPlanStats.value.map(project => ({
    name: project.project_name,
    value: project.executed_plans
  }))
})

// 功能测试计划图表数据
const functionalTestPlanChartData = computed(() => {
  const totalStats = functionalTestPlanStats.value.reduce((acc, project) => {
    acc.not_started += project.not_started_plans
    acc.in_progress += project.in_progress_plans
    acc.completed += project.completed_plans
    return acc
  }, { not_started: 0, in_progress: 0, completed: 0 })

  return [
    { name: '未开始', value: totalStats.not_started },
    { name: '进行中', value: totalStats.in_progress },
    { name: '已完成', value: totalStats.completed }
  ].filter(item => item.value > 0)
})

// 获取定时任务统计数据
const fetchScheduledTaskStats = async () => {
  try {
    loading.value = true
    const response = await scheduledTaskApi.getScheduledTaskStatistics()
    if (response.code === 200) {
      scheduledTaskStats.value = response.data || scheduledTaskStats.value
    }
  } catch (error) {
    console.error('获取定时任务统计失败:', error)
    $message.error('获取定时任务统计数据失败')
  } finally {
    loading.value = false
  }
}

// 获取接口测试计划统计数据
const fetchApiTestPlanStats = async () => {
  try {
    loading.value = true
    const response = await apiTestPlanApi.getApiTestPlanStatistics()
    if (response.code === 200) {
      apiTestPlanStats.value = response.data || []
    }
  } catch (error) {
    console.error('获取接口测试计划统计失败:', error)
    $message.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 获取功能测试计划统计数据
const fetchFunctionalTestPlanStats = async () => {
  try {
    functionalLoading.value = true
    const response = await functionalTestPlanApi.getFunctionalTestPlanStatistics()
    if (response.code === 200) {
      functionalTestPlanStats.value = response.data || []
    }
  } catch (error) {
    console.error('获取功能测试计划统计失败:', error)
    $message.error('获取功能测试计划统计数据失败')
  } finally {
    functionalLoading.value = false
  }
}

// 页面挂载时获取数据
onMounted(() => {
  fetchScheduledTaskStats()
  fetchApiTestPlanStats()
  fetchFunctionalTestPlanStats()
})
</script>

<style scoped>
.project-card {
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: box-shadow 0.3s ease;
}

.project-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>
