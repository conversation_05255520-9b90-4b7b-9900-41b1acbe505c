<script setup>
import { h, ref, onMounted } from 'vue'
import { NButton, NTag, NSpace, NPopconfirm, useMessage, useDialog } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'
import TheIcon from '@/components/icon/TheIcon.vue'

defineOptions({ name: 'AI模型配置' })

const $table = ref(null)
const $message = useMessage()
const $dialog = useDialog()

const queryItems = ref({
  name: null,
  model_type: null,
  status: null,
})

// 模型类型选项
const modelTypeOptions = ref([])
// 状态选项
const statusOptions = ref([])

// 获取选项数据
const loadOptions = async () => {
  try {
    const [typeRes, statusRes] = await Promise.all([
      api.getAIModelTypes(),
      api.getStatusOptions()
    ])
    modelTypeOptions.value = typeRes.data
    statusOptions.value = statusRes.data
  } catch (error) {
    console.error('加载选项失败:', error)
  }
}

const {
  modalVisible,
  modalTitle,
  modalAction,
  modalLoading,
  handleSave: originalHandleSave,
  modalForm,
  modalFormRef,
  handleEdit: originalHandleEdit,
  handleDelete,
  handleAdd,
} = useCRUD({
  name: 'AI模型配置',
  initForm: {
    name: '',
    model_type: 'openai',
    api_key: '',
    api_url: '',
    model_name: '',
    max_tokens: 4096,
    temperature: 0.7,
    timeout: 30,
    status: 'inactive',
    is_default: false,
    description: '',
    config_json: null,
  },
  doCreate: api.createAIModelConfig,
  doUpdate: api.updateAIModelConfig,
  doDelete: api.deleteAIModelConfig,
  refresh: () => $table.value?.handleSearch(),
})

// 存储原始API密钥
const originalApiKey = ref('')

// 测试按钮loading状态
const testingIds = ref(new Set())

// 自定义编辑处理
const handleEdit = async (row) => {
  // 获取完整的配置信息（包含完整的API密钥）
  try {
    const result = await api.getAIModelConfigById({ id: row.id })
    const fullData = result.data

    // 如果API密钥被截断了，保持原样，用户需要重新输入
    if (fullData.api_key && fullData.api_key.endsWith('****')) {
      originalApiKey.value = ''
      fullData.api_key = ''
    } else {
      originalApiKey.value = fullData.api_key
    }

    // 调用原始编辑方法
    await originalHandleEdit(fullData)
  } catch (error) {
    $message.error('获取配置详情失败: ' + error.message)
  }
}

// 自定义保存处理
const handleSave = async () => {
  // 如果API密钥为空且有原始密钥，使用原始密钥
  if (!modalForm.value.api_key && originalApiKey.value) {
    modalForm.value.api_key = originalApiKey.value
  }

  await originalHandleSave()
}

const columns = [
  {
    title: '模型名称',
    key: 'name',
    width: 150,
    ellipsis: { tooltip: true },
  },
  {
    title: '模型类型',
    key: 'model_type',
    width: 100,
    render: (row) => {
      const option = modelTypeOptions.value.find(opt => opt.value === row.model_type)
      return option ? option.label : row.model_type
    }
  },
  {
    title: '具体模型',
    key: 'model_name',
    width: 120,
    ellipsis: { tooltip: true },
  },
  {
    title: 'API地址',
    key: 'api_url',
    width: 150,
    ellipsis: { tooltip: true },
  },
  {
    title: '状态',
    key: 'status',
    width: 60,
    render: (row) => {
      const statusMap = {
        active: { type: 'success', text: '启用' },
        inactive: { type: 'default', text: '禁用' },
        testing: { type: 'warning', text: '测试中' },
      }
      const status = statusMap[row.status] || { type: 'default', text: row.status }
      return h(NTag, { type: status.type }, { default: () => status.text })
    }
  },
  {
    title: '默认模型',
    key: 'is_default',
    width: 80,
    render: (row) => {
      return h(NTag, { 
        type: row.is_default ? 'success' : 'default' 
      }, { 
        default: () => row.is_default ? '是' : '否' 
      })
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render: (row) => formatDate(row.created_at),
  },
  {
    title: '操作',
    key: 'actions',
    width: 280,
    align: 'center',
    fixed: 'right',
    hideInExcel: true,
    render: (row) => {
      return h(NSpace, { justify: 'center' }, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'primary',
            secondary: true,
            loading: testingIds.value.has(row.id),
            disabled: testingIds.value.has(row.id),
            onClick: () => handleTest(row)
          }, { default: () => testingIds.value.has(row.id) ? '测试中...' : '测试' }),
          h(NButton, {
            size: 'small',
            type: 'primary',
            onClick: () => handleEdit(row)
          }, { default: () => '编辑' }),
          h(NButton, {
            size: 'small',
            type: row.is_default ? 'default' : 'warning',
            disabled: row.is_default,
            onClick: () => handleSetDefault(row)
          }, { default: () => row.is_default ? '已默认' : '默认' }),
          h(NPopconfirm, {
            onPositiveClick: () => handleDelete(row.id),
            negativeText: '取消',
            positiveText: '确认'
          }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'error'
            }, { default: () => '删除' }),
            default: () => '确认删除该配置吗？'
          })
        ]
      })
    }
  }
]

// 测试模型连接
const handleTest = async (row) => {
  // 防止重复点击
  if (testingIds.value.has(row.id)) {
    return
  }

  try {
    // 添加到测试中的ID集合
    testingIds.value.add(row.id)

    const result = await api.testAIModelConfig({
      config_id: row.id,
      test_message: '你好啊, 请介绍一下你自己。(20字以内)'
    })

    if (result.data.success) {
      $dialog.success({
        title: '测试成功',
        content: `响应时间: ${result.data.response_time}秒\n\n模型响应:\n${result.data.response}`,
        positiveText: '确定'
      })
    } else {
      $dialog.error({
        title: '测试失败',
        content: `错误信息: ${result.data.error}\n响应时间: ${result.data.response_time}秒`,
        positiveText: '确定'
      })
    }
  } catch (error) {
    $message.error('测试请求失败: ' + error.message)
  } finally {
    // 无论成功失败都要移除loading状态
    testingIds.value.delete(row.id)
  }
}

// 设置默认模型
const handleSetDefault = async (row) => {
  try {
    await api.setDefaultAIModelConfig({ config_id: row.id })
    $message.success('设置默认模型成功')
    $table.value?.handleSearch()
  } catch (error) {
    $message.error('设置默认模型失败: ' + error.message)
  }
}

onMounted(() => {
  loadOptions()
  $table.value?.handleSearch()
})
</script>

<template>
  <!-- 业务页面 -->
  <CommonPage show-footer title="AI模型配置">
    <template #action>
      <NButton
        v-permission="'post/api/v1/ai_model_config/create'"
        type="primary"
        @click="handleAdd"
      >
        <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建配置
      </NButton>
    </template>

    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getAIModelConfigList"
    >
      <template #queryBar>
        <QueryBarItem label="模型名称" :label-width="80">
          <NInput
            v-model:value="queryItems.name"
            clearable
            type="text"
            placeholder="请输入模型名称"
            @keydown.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="模型类型" :label-width="80">
          <NSelect
            v-model:value="queryItems.model_type"
            clearable
            placeholder="请选择模型类型"
            style="min-width: 150px;"
            :options="modelTypeOptions"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="状态" :label-width="50">
          <NSelect
            v-model:value="queryItems.status"
            clearable
            placeholder="请选择状态"
            style="min-width: 120px;"
            :options="statusOptions"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 新增/编辑弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      :show-footer="true"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :disabled="modalLoading"
      >
        <NFormItem label="模型名称" path="name" :rule="{ required: true, message: '请输入模型名称' }">
          <NInput v-model:value="modalForm.name" placeholder="请输入模型名称" />
        </NFormItem>
        <NFormItem label="模型类型" path="model_type" :rule="{ required: true, message: '请选择模型类型' }">
          <NSelect
            v-model:value="modalForm.model_type"
            placeholder="请选择模型类型"
            :options="modelTypeOptions"
          />
        </NFormItem>
        <NFormItem label="API密钥" path="api_key" :rule="{ required: true, message: '请输入API密钥' }">
          <NInput v-model:value="modalForm.api_key" type="password" show-password-on="click" placeholder="请输入API密钥" />
        </NFormItem>
        <NFormItem label="API地址" path="api_url">
          <NInput v-model:value="modalForm.api_url" placeholder="留空使用默认地址" />
        </NFormItem>
        <NFormItem label="具体模型" path="model_name">
          <NInput v-model:value="modalForm.model_name" placeholder="如: gpt-3.5-turbo, claude-3-sonnet等" />
        </NFormItem>
        <NGrid :cols="2" :x-gap="12">
          <NFormItemGi label="最大Token" path="max_tokens">
            <NInputNumber v-model:value="modalForm.max_tokens" :min="1" :max="100000" placeholder="4096" />
          </NFormItemGi>
          <NFormItemGi label="温度参数" path="temperature">
            <NInputNumber v-model:value="modalForm.temperature" :min="0" :max="2" :step="0.1" placeholder="0.7" />
          </NFormItemGi>
        </NGrid>
        <NGrid :cols="2" :x-gap="12">
          <NFormItemGi label="超时时间(秒)" path="timeout">
            <NInputNumber v-model:value="modalForm.timeout" :min="1" :max="300" placeholder="30" />
          </NFormItemGi>
          <NFormItemGi label="状态" path="status">
            <NSelect
              v-model:value="modalForm.status"
              placeholder="请选择状态"
              :options="statusOptions"
            />
          </NFormItemGi>
        </NGrid>
        <NFormItem label="默认模型" path="is_default">
          <NSwitch v-model:value="modalForm.is_default" />
        </NFormItem>
        <NFormItem label="模型描述" path="description">
          <NInput
            v-model:value="modalForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模型描述"
          />
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template>
