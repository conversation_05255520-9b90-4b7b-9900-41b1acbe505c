<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import { useRouter } from 'vue-router'
import { NButton, NForm, NFormItem, NInput, NPopconfirm, NSelect, NDatePicker } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import projectApi from '@/api/project'
defineOptions({ name: '项目管理' })

const router = useRouter()
const $table = ref(null)
// 修改这里，明确初始化查询条件对象
const queryItems = ref({
  name: '',
  status: '',
  manager: ''
})
const vPermission = resolveDirective('permission')

const statusOptions = [
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' },
  { label: '已暂停', value: 'paused' },
  { label: '已取消', value: 'cancelled' }
]


const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleSave,
  modalForm,
  modalFormRef,
  handleEdit,
  handleDelete,
  handleAdd,
} = useCRUD({
  name: '项目',
  initForm: {
    name: '',
    description: '',
    status: 'in_progress',
    start_date: null,
    end_date: null,
    manager: '',
    budget: 0
  },
  doCreate: projectApi.createProject,
  doUpdate: projectApi.updateProject,
  doDelete: projectApi.deleteProject,
  refresh: () => $table.value?.handleSearch(),
})

onMounted(() => {
  $table.value?.handleSearch()
})

const projectRules = {
  name: [
    {
      required: true,
      message: '请输入项目名称',
      trigger: ['input', 'blur', 'change'],
    },
  ],
  status: [
    {
      required: true,
      message: '请选择项目状态',
      trigger: ['blur', 'change'],
    },
  ],
  start_date: [
    {
      required: true,
      message: '请选择开始日期',
      trigger: ['blur', 'change'],
      validator: (rule, value) => {
        return !!value
      }
    },
  ]
}

// 首先在 columns 定义之前添加自定义编辑函数
const customHandleEdit = (row) => {
  // 创建一个新对象，避免直接修改原始数据
  const formData = { ...row }

  // 处理日期格式
  if (formData.start_date) {
    try {
      formData.start_date = new Date(formData.start_date).getTime()
    } catch (e) {
      formData.start_date = null
    }
  }

  if (formData.end_date) {
    try {
      formData.end_date = new Date(formData.end_date).getTime()
    } catch (e) {
      formData.end_date = null
    }
  }

  // 调用原始的编辑函数
  handleEdit(formData)
}

// 跳转到项目详情页面
const handleGoToDetail = (row) => {
  router.push(`/project/detail/${row.id}`)
}

const columns = [
  {
    title: '项目名称',
    key: 'name',
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '项目描述',
    key: 'description',
    width: 'auto',
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '项目状态',
    key: 'status',
    width: 'auto',
    align: 'center',
    render(row) {
      const statusMap = {
        'in_progress': '进行中',
        'completed': '已完成',
        'paused': '已暂停',
        'cancelled': '已取消'
      }
      return statusMap[row.status] || row.status
    }
  },
  {
    title: '开始日期',
    key: 'start_date',
    width: 'auto',
    align: 'center',
  },
  {
    title: '结束日期',
    key: 'end_date',
    width: 'auto',
    align: 'center',
  },
  {
    title: '项目经理',
    key: 'manager',
    width: 'auto',
    align: 'center',
  },
  {
    title: '预算',
    key: 'budget',
    width: 'auto',
    align: 'center',
    render(row) {
      return `¥${row.budget.toLocaleString()}`
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: '180px',
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'info',
            style: 'margin-right: 4px;',
            onClick: () => handleGoToDetail(row),
          },
          {
            default: () => '详情',
          }
        ),
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-right: 4px;',
              onClick: () => {
                customHandleEdit(row)  // 使用自定义的编辑函数
              },
            },
            {
              default: () => '编辑',
            }
          ),
          [[vPermission, 'post/api/v1/project/update']]
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    style: 'margin-right: 4px;',
                  },
                  {
                    default: () => '删除',
                    // icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/project/delete']]
              ),
            default: () => h('div', {}, '确定删除该项目吗?'),
          }
        ),
      ]
    },
  },
]
</script>

<template>
  <!-- 业务页面 -->
  <CommonPage show-footer title="项目管理">
    <template #action>
      <div>
        <NButton
          v-permission="'post/api/v1/project/create'"
          class="float-right mr-15"
          type="primary"
          @click="handleAdd"
        >
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建项目
        </NButton>
      </div>
    </template>
    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="projectApi.getProjects"
    >
      <template #queryBar>
        <QueryBarItem label="项目名称" :label-width="70">
          <NInput
            v-model:value="queryItems.name"
            clearable
            type="text"
            placeholder="请输入项目名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="状态" :label-width="40">
          <NSelect
            v-model:value="queryItems.status"
            clearable
            :options="statusOptions"
            placeholder="请选择项目状态"
            @update:value="$table?.handleSearch()"
            style="min-width: 120px"
          />
        </QueryBarItem>
        <QueryBarItem label="项目经理" :label-width="70">
          <NInput
            v-model:value="queryItems.manager"
            clearable
            type="text"
            placeholder="请输入项目经理"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 新增/编辑 弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="80"
        :model="modalForm"
        :rules="projectRules"
      >
        <NFormItem label="项目名称" path="name">
          <NInput v-model:value="modalForm.name" clearable placeholder="请输入项目名称" />
        </NFormItem>
        <NFormItem label="项目描述" path="description">
          <NInput v-model:value="modalForm.description" clearable placeholder="请输入项目描述" />
        </NFormItem>
        <NFormItem label="项目状态" path="status">
          <NSelect
            v-model:value="modalForm.status"
            :options="statusOptions"
            placeholder="请选择项目状态"
          />
        </NFormItem>
        <NFormItem label="开始日期" path="start_date">
          <NDatePicker
            v-model:value="modalForm.start_date"
            type="date"
            clearable
            placeholder="请选择开始日期"
          />
        </NFormItem>
        <NFormItem label="结束日期" path="end_date">
          <NDatePicker
            v-model:value="modalForm.end_date"
            type="date"
            clearable
            placeholder="请选择结束日期"
          />
        </NFormItem>
        <NFormItem label="项目经理" path="manager">
          <NInput v-model:value="modalForm.manager" clearable placeholder="请输入项目经理" />
        </NFormItem>
        <NFormItem label="预算" path="budget">
          <NInput
            v-model:value="modalForm.budget"
            clearable
            type="number"
            placeholder="请输入项目预算"
          />
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template>
