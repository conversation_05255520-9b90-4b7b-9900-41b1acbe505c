const Layout = () => import('@/layout/index.vue')

export default {
  name: '项目管理',
  path: '/project',
  component: Layout,
  redirect: '/project/project_manage',
  meta: {
    title: '项目管理',
    icon: 'material-symbols:folder-managed',
    order: 2,
  },
  children: [
    {
      name: '项目管理页面',
      path: 'project_manage',
      component: () => import('./project_manage/index.vue'),
      meta: {
        title: '项目管理',
        icon: 'material-symbols:folder-managed',
      },
    },
    {
      name: '环境配置',
      path: 'env',
      component: () => import('./env_config/index.vue'),
      meta: {
        title: '环境配置',
        icon: 'material-symbols:settings-applications',
      },
    },
  ],
}
