<template>
  <NLayout has-sider wh-full>
    <NLayoutSider
      bordered
      content-style="padding: 24px;"
      :collapsed-width="0"
      :width="240"
      show-trigger="arrow-circle"
    >
      <h1>项目列表</h1>
      <br />
      <NTree
        block-line
        :data="projectOption"
        key-field="id"
        label-field="name"
        default-expand-all
        :node-props="nodeProps"
        :selected-keys="selectedKeys"
      >
      </NTree>
    </NLayoutSider>
    <NLayoutContent>
      <CommonPage show-footer :title="`${selectedProjectName} - 模块管理`">
        <template #action>
          <NButton type="primary" @click="handleAdd">
            <TheIcon icon="material-symbols:add" :size="18" class="mr-1" />
            新建模块
          </NButton>
        </template>

        <!-- 搜索区域 -->
        <div class="mb-4">
          <NInputGroup class="flex items-center gap-2">
            <NInput 
              v-model:value="searchName" 
              placeholder="输入模块名称搜索" 
              style="width: 200px"
              @keypress.enter="loadModuleTree"
            />
            <NButton type="primary" @click="loadModuleTree">搜索</NButton>
            <NButton @click="resetSearch">重置</NButton>
          </NInputGroup>
        </div>

        <!-- 模块树形表格 -->
        <NDataTable
          :columns="columns"
          :data="moduleTreeData"
          :loading="loading"
          :pagination="false"
          default-expand-all
          :cascade="false"
          children-key="children"
        />

        <!-- 新增/编辑模块弹窗 -->
        <NModal
          v-model:show="modalVisible"
          preset="card"
          :title="modalTitle"
          style="width: 600px;"
          :mask-closable="false"
        >
          <NForm
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-placement="left"
            label-width="80px"
          >
            <NFormItem label="模块名称" path="name">
              <NInput v-model:value="formData.name" placeholder="请输入模块名称" />
            </NFormItem>
            <NFormItem label="模块描述" path="description">
              <NInput 
                v-model:value="formData.description" 
                type="textarea" 
                placeholder="请输入模块描述"
                :rows="3"
              />
            </NFormItem>
            <NFormItem label="父模块" path="parent_id">
              <NTreeSelect
                v-model:value="formData.parent_id"
                :options="parentModuleOptions"
                placeholder="请选择父模块（不选择则为顶级模块）"
                clearable
                key-field="id"
                label-field="name"
                children-field="children"
              />
            </NFormItem>
            <NFormItem label="排序" path="order">
              <NInputNumber v-model:value="formData.order" placeholder="请输入排序值" />
            </NFormItem>
            <NFormItem label="状态" path="status">
              <NSelect
                v-model:value="formData.status"
                :options="statusOptions"
                placeholder="请选择状态"
              />
            </NFormItem>
          </NForm>

          <template #footer>
            <div class="flex justify-end gap-2">
              <NButton @click="modalVisible = false">取消</NButton>
              <NButton type="primary" :loading="submitLoading" @click="handleSubmit">
                {{ modalAction === 'create' ? '创建' : '更新' }}
              </NButton>
            </div>
          </template>
        </NModal>
      </CommonPage>
    </NLayoutContent>
  </NLayout>
</template>

<script setup>
import { h, ref, reactive, onMounted, computed } from 'vue'
import {
  NButton,
  NDataTable,
  NInput,
  NInputGroup,
  NModal,
  NForm,
  NFormItem,
  NTreeSelect,
  NInputNumber,
  NSelect,
  NTag,
  NPopconfirm,
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NTree,
  useMessage,
  useDialog
} from 'naive-ui'
import CommonPage from '@/components/page/CommonPage.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import { renderIcon } from '@/utils'
import projectApi from '@/api/project'

defineOptions({ name: '项目详情' })

const message = useMessage()
const dialog = useDialog()

// 基础数据
const selectedProjectId = ref(null)
const selectedProjectName = ref('请选择项目')
const selectedKeys = ref([])
const projectOption = ref([])
const loading = ref(false)
const moduleTreeData = ref([])
const searchName = ref('')

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref('')
const modalAction = ref('create') // 'create' | 'edit'
const submitLoading = ref(false)
const formRef = ref(null)

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  description: '',
  project_id: null,
  parent_id: 0,
  order: 0,
  status: 'active'
})

// 状态选项
const statusOptions = [
  { label: '启用', value: 'active' },
  { label: '禁用', value: 'inactive' }
]

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入模块名称', trigger: 'blur' }
  ],
  project_id: [
    { required: true, message: '请选择项目', trigger: 'change' }
  ]
}

// 父模块选项
const parentModuleOptions = computed(() => {
  const options = [{ id: 0, name: '顶级模块', children: [] }]
  
  function addChildren(items, parentOption) {
    items.forEach(item => {
      if (modalAction.value === 'edit' && item.id === formData.id) {
        return // 不能选择自己作为父模块
      }
      
      const option = {
        id: item.id,
        name: item.name,
        children: []
      }
      
      if (item.children && item.children.length > 0) {
        addChildren(item.children, option)
      }
      
      parentOption.children.push(option)
    })
  }
  
  addChildren(moduleTreeData.value, options[0])
  return options
})

// 项目树节点属性
const nodeProps = ({ option }) => {
  return {
    onClick() {
      handleSelectProject(option)
    }
  }
}

// 选择项目
const handleSelectProject = (project) => {
  selectedProjectId.value = project.id
  selectedProjectName.value = project.name
  selectedKeys.value = [project.id]
  formData.project_id = project.id
  loadModuleTree()
}

// 表格列定义
const columns = [
  {
    title: '模块名称',
    key: 'name',
    width: 500,
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      return h(NTag, {
        type: row.status === 'active' ? 'success' : 'error'
      }, {
        default: () => row.status === 'active' ? '启用' : '禁用'
      })
    }
  },
  {
    title: '排序',
    key: 'order',
    width: 80,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render(row) {
      return h('div', { class: 'flex gap-2' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => handleEdit(row)
        }, { default: () => '编辑' }),
        h(NPopconfirm, {
          onPositiveClick: () => handleDelete(row.id)
        }, {
          default: () => '确定删除这个模块吗？',
          trigger: () => h(NButton, {
            size: 'small',
            type: 'error'
          }, { default: () => '删除' })
        })
      ])
    }
  }
]

// 加载项目列表
const loadProjectList = async () => {
  try {
    const response = await projectApi.getProjectList()
    projectOption.value = response.data || []

    // 默认选中第一个项目
    if (projectOption.value.length > 0) {
      handleSelectProject(projectOption.value[0])
    }
  } catch (error) {
    console.error('加载项目列表失败:', error)
    message.error('加载项目列表失败: ' + error.message)
  }
}

// 加载模块树
const loadModuleTree = async () => {
  if (!selectedProjectId.value) return

  try {
    loading.value = true
    const params = {
      project_id: selectedProjectId.value
    }
    if (searchName.value) {
      params.name = searchName.value
    }

    const response = await projectApi.getProjectModuleTree(params)
    moduleTreeData.value = response.data || []
  } catch (error) {
    console.error('加载模块树失败:', error)
    message.error('加载模块树失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchName.value = ''
  loadModuleTree()
}

// 新增模块
const handleAdd = () => {
  if (!selectedProjectId.value) {
    message.warning('请先选择项目')
    return
  }

  modalAction.value = 'create'
  modalTitle.value = '新建模块'
  Object.assign(formData, {
    id: null,
    name: '',
    description: '',
    project_id: selectedProjectId.value,
    parent_id: 0,
    order: 0,
    status: 'active'
  })
  modalVisible.value = true
}

// 编辑模块
const handleEdit = (row) => {
  modalAction.value = 'edit'
  modalTitle.value = '编辑模块'
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    description: row.description || '',
    project_id: row.project_id,
    parent_id: row.parent_id,
    order: row.order,
    status: row.status
  })
  modalVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitLoading.value = true

    if (modalAction.value === 'create') {
      await projectApi.createProjectModule(formData)
      message.success('模块创建成功')
    } else {
      await projectApi.updateProjectModule(formData)
      message.success('模块更新成功')
    }

    modalVisible.value = false
    loadModuleTree()
  } catch (error) {
    console.error('操作失败:', error)
    message.error('操作失败: ' + error.message)
  } finally {
    submitLoading.value = false
  }
}

// 删除模块
const handleDelete = async (moduleId) => {
  try {
    // 先检查是否可以删除
    const checkResponse = await projectApi.checkCanDeleteModule({ module_id: moduleId })
    if (!checkResponse.data.can_delete) {
      message.warning('该模块下存在子模块或关联数据，无法删除')
      return
    }

    await projectApi.deleteProjectModule({ module_id: moduleId })
    message.success('模块删除成功')
    loadModuleTree()
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败: ' + error.message)
  }
}

// 初始化
onMounted(() => {
  loadProjectList()
})
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}

.mr-1 {
  margin-right: 4px;
}

.flex {
  display: flex;
}

.gap-2 {
  gap: 8px;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}
</style>
