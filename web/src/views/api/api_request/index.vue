<script setup>
import { h, ref, reactive, onMounted, watch } from 'vue'
import {
  NButton,
  NInput,
  NSelect,
  NTabs,
  NTabPane,
  NForm,
  NFormItem,
  NCard,
  NSpace,
  NDataTable,
  NDynamicTags,
  NCheckbox,
  NInputGroup,
  NCode,
  NModal,
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NList,
  NListItem,
  NThing,
  NTag,
  NScrollbar,
  useMessage
} from 'naive-ui'
import CommonPage from '@/components/page/CommonPage.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import axios from 'axios'
import api from '@/api'

defineOptions({ name: 'API请求工具' })

const message = useMessage()

// 请求方法选项
const methodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' }
]

// 请求表单
const requestForm = reactive({
  method: 'GET',
  url: '',
  params: [],
  headers: [],
  body: ''
})

// 监听URL变化，自动解析查询参数
const parseUrlParams = (url) => {
  if (!url) return
  
  try {
    const urlObj = new URL(url)
    const searchParams = urlObj.searchParams
    
    // 清空现有参数
    requestForm.params = []
    
    // 添加URL中的查询参数
    searchParams.forEach((value, key) => {
      requestForm.params.push({
        checked: true,
        key,
        value,
        description: ''
      })
    })
    
    // 更新URL，移除查询参数部分
    requestForm.url = `${urlObj.origin}${urlObj.pathname}`
  } catch (e) {
    // URL解析失败，可能是不完整的URL
    console.log('URL解析失败:', e)
  }
}

// 响应数据
const responseData = ref(null)
const responseStatus = ref('')
const responseTime = ref('')
const responseSize = ref('')
const responseHeaders = ref(null) // 添加响应头信息变量
const isLoading = ref(false)

// 验证码相关
const showCaptchaModal = ref(false)
const captchaForm = reactive({
  captcha_url: '',
  captcha_method: 'GET',
  captcha_headers: '{}',
  captcha_body: '{}',
  captcha_image_path: 'content.imageBase64',
  captcha_key_path: 'content.codeKey'
})
const captchaLoading = ref(false)

// 参数表格列
const paramsColumns = [
  {
    title: '',
    key: 'checked',
    width: 50,
    render: (row) => h(NCheckbox, {
      checked: row.checked,
      'onUpdate:checked': (checked) => {
        row.checked = checked
      }
    })
  },
  {
    title: 'Key',
    key: 'key',
    render: (row) => h(NInput, {
      value: row.key,
      'onUpdate:value': (value) => {
        row.key = value
      },
      placeholder: '参数名'
    })
  },
  {
    title: 'Value',
    key: 'value',
    render: (row) => h(NInput, {
      value: row.value,
      'onUpdate:value': (value) => {
        row.value = value
      },
      placeholder: '参数值'
    })
  },
  {
    title: 'Description',
    key: 'description',
    render: (row) => h(NInput, {
      value: row.description,
      'onUpdate:value': (value) => {
        row.description = value
      },
      placeholder: '描述'
    })
  },
  {
    title: '',
    key: 'actions',
    width: 50,
    render: (row, index) => h(NButton, {
      quaternary: true,
      circle: true,
      onClick: () => removeParam(index),
      renderIcon: () => h(TheIcon, { icon: 'material-symbols:delete-outline' })
    })
  }
]

// 头部表格列
const headersColumns = [
  {
    title: '',
    key: 'checked',
    width: 50,
    render: (row) => h(NCheckbox, {
      checked: row.checked,
      'onUpdate:checked': (checked) => {
        row.checked = checked
      }
    })
  },
  {
    title: 'Key',
    key: 'key',
    render: (row) => h(NInput, {
      value: row.key,
      'onUpdate:value': (value) => {
        row.key = value
      },
      placeholder: '头部名'
    })
  },
  {
    title: 'Value',
    key: 'value',
    render: (row) => h(NInput, {
      value: row.value,
      'onUpdate:value': (value) => {
        row.value = value
      },
      placeholder: '头部值'
    })
  },
  {
    title: 'Description',
    key: 'description',
    render: (row) => h(NInput, {
      value: row.description,
      'onUpdate:value': (value) => {
        row.description = value
      },
      placeholder: '描述'
    })
  },
  {
    title: '',
    key: 'actions',
    width: 50,
    render: (row, index) => h(NButton, {
      quaternary: true,
      circle: true,
      onClick: () => removeHeader(index),
      renderIcon: () => h(TheIcon, { icon: 'material-symbols:delete-outline' })
    })
  }
]

// 添加参数
const addParam = () => {
  requestForm.params.push({
    checked: true,
    key: '',
    value: '',
    description: ''
  })
}

// 删除参数
const removeParam = (index) => {
  requestForm.params.splice(index, 1)
}

// 添加头部
const addHeader = () => {
  requestForm.headers.push({
    checked: true,
    key: '',
    value: '',
    description: ''
  })
}

// 删除头部
const removeHeader = (index) => {
  requestForm.headers.splice(index, 1)
}

// 使用后端代理发送外部API请求
const sendExternalRequest = async (config) => {
  // 构建代理请求数据
  const proxyData = {
    method: config.method,
    url: config.url,
    params: config.params || {},
    headers: config.headers || {},
    body: config.data ? (typeof config.data === 'string' ? config.data : JSON.stringify(config.data)) : null
  }

  // 通过后端代理发送请求
  const response = await api.proxyExternalApi(proxyData)

  // 将后端响应转换为axios格式
  return {
    status: response.data.status,
    statusText: response.data.statusText,
    headers: response.data.headers,
    data: response.data.data,
    responseTime: response.data.responseTime
  }
}

// 发送请求
const sendRequest = async () => {
  try {
    isLoading.value = true

    // 构建请求参数
    const config = {
      method: requestForm.method,
      url: requestForm.url,
      headers: {
        // 添加一些常用的请求头以避免CORS问题
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'User-Agent': 'Mozilla/5.0 (compatible; API-Request-Tool/1.0)'
      },
      // 添加noNeedToken标识，避免自动添加token
      noNeedToken: true
    }

    // 添加查询参数
    const params = {}
    requestForm.params.forEach(param => {
      if (param.checked && param.key) {
        params[param.key] = param.value
      }
    })

    if (Object.keys(params).length > 0) {
      config.params = params
    }

    // 添加用户自定义请求头（会覆盖默认的）
    requestForm.headers.forEach(header => {
      if (header.checked && header.key) {
        config.headers[header.key] = header.value
      }
    })

    // 添加请求体
    if (['POST', 'PUT', 'PATCH'].includes(requestForm.method) && requestForm.body) {
      try {
        config.data = JSON.parse(requestForm.body)
      } catch (e) {
        config.data = requestForm.body
      }
    }

    const startTime = Date.now()
    const response = await sendExternalRequest(config)
    const endTime = Date.now()

    responseTime.value = response.responseTime ? `${response.responseTime} ms` : `${endTime - startTime} ms`
    responseStatus.value = `${response.status} ${response.statusText}`
    responseData.value = response.data
    
    // 处理响应头，将其转换为普通对象
    const headersObj = {}
    for (const key in response.headers) {
      if (Object.prototype.hasOwnProperty.call(response.headers, key)) {
        headersObj[key] = response.headers[key]
      }
    }
    responseHeaders.value = headersObj
    
    // 打印完整响应以便调试
    console.log('完整响应:', response)
    console.log('响应头:', headersObj)
    
    // 计算响应大小
    const jsonSize = JSON.stringify(response.data).length
    responseSize.value = jsonSize < 1024 ? `${jsonSize} B` : `${(jsonSize / 1024).toFixed(2)} KB`

    // 创建执行历史记录
    await createExecutionHistory(config, response, endTime - startTime, true)

    message.success('请求成功')
  } catch (error) {
    console.error('请求错误:', error)
    responseStatus.value = error.response ? `${error.response.status} ${error.response.statusText}` : '请求失败'
    responseData.value = error.response ? error.response.data : { error: error.message }
    responseHeaders.value = error.response ? error.response.headers : null // 错误情况下也保存响应头

    // 创建失败的执行历史记录
    await createExecutionHistory(config, error.response, 0, false)

    message.error('请求失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

// 添加项目列表和保存接口相关的状态
const showSaveModal = ref(false)
const projects = ref([])
const modules = ref([])
const saveForm = reactive({
  api_name: '',
  project_id: null,
  module_id: null
})
const formRef = ref(null)
const saveLoading = ref(false)

// 获取项目列表
const fetchProjects = async () => {
  try {
    // 使用api模块中定义的getProjectList方法
    const response = await api.getProjectList({
      page: 1,
      page_size: 100  // 设置较大的页面大小以获取更多项目
    })

    console.log('项目列表响应:', response)

    // 确保返回的数据结构正确
    if (response && response.data) {
      // 从response.data中获取项目列表
      projects.value = response.data.map(item => ({
        label: item.name,
        value: item.id
      }))
    } else {
      console.error('项目数据格式不正确:', response)
      message.error('获取项目列表失败: 数据格式不正确')
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
    console.log('错误详情:', error.response?.data)
    message.warning('无法获取项目列表，已使用测试数据')
  }
}

// 获取项目模块列表
const fetchModules = async (projectId) => {
  if (!projectId) {
    modules.value = []
    return
  }

  try {
    const response = await api.getProjectModuleTree({ project_id: projectId })
    console.log('模块列表响应:', response)

    if (response && response.data) {
      // 将树形结构扁平化为选项列表
      const flattenModules = (moduleList, prefix = '') => {
        let result = []
        moduleList.forEach(module => {
          const label = prefix ? `${prefix} / ${module.name}` : module.name
          result.push({
            label: label,
            value: module.id
          })
          if (module.children && module.children.length > 0) {
            result = result.concat(flattenModules(module.children, label))
          }
        })
        return result
      }

      modules.value = flattenModules(response.data)
    } else {
      modules.value = []
    }
  } catch (error) {
    console.error('获取模块列表失败:', error)
    message.error('获取模块列表失败')
    modules.value = []
  }
}

// 监听项目选择变化
const onProjectChange = (projectId) => {
  saveForm.module_id = null // 重置模块选择
  fetchModules(projectId)
}

// 保存接口
const saveApi = () => {
  // 打开保存对话框
  showSaveModal.value = true
  // 获取项目列表
  fetchProjects()
}

// 确认保存接口
const confirmSave = async () => {
  if (!saveForm.api_name) {
    message.warning('请输入接口名称')
    return
  }

  if (!saveForm.project_id) {
    message.warning('请选择所属项目')
    return
  }

  try {
    saveLoading.value = true

    // 构建要保存的接口数据
    const apiData = {
      api_name: saveForm.api_name,  // 修改字段名为 api_name
      url: requestForm.url,
      method: requestForm.method,
      params: JSON.stringify(requestForm.params.filter(p => p.checked)),  // 转换为字符串
      headers: JSON.stringify(requestForm.headers.filter(h => h.checked)),  // 转换为字符串
      body: requestForm.body,
      user_id: 1,  // 添加用户ID，可以从全局状态或其他地方获取
      project_id: saveForm.project_id,
      module_id: saveForm.module_id
    }

    // 发送保存请求
    const response = await api.createApiRequest(apiData)
    console.log('保存接口响应:', response)
    message.success('接口保存成功，已自动创建接口测试用例')
    showSaveModal.value = false

    // 重置保存表单
    saveForm.api_name = ''
    saveForm.project_id = null
    saveForm.module_id = null
  } catch (error) {
    console.error('保存接口失败:', error)
    message.error('保存接口失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

// 取消保存
const cancelSave = () => {
  showSaveModal.value = false
  saveForm.api_name = ''
  saveForm.project_id = null
  saveForm.module_id = null
}

// 最近调用记录相关
const recentRequests = ref([])
const selectedRequestId = ref(null)
const loadingRecent = ref(false)

// 获取最近调用记录
const fetchRecentRequests = async () => {
  try {
    loadingRecent.value = true
    const response = await api.getApiExecutionHistory()
    recentRequests.value = response.data || []
  } catch (error) {
    console.error('获取最近调用记录失败:', error)
    message.error('获取最近调用记录失败')
  } finally {
    loadingRecent.value = false
  }
}

// 点击最近调用记录，填充表单
const loadRequestFromHistory = (record) => {
  selectedRequestId.value = record.id

  // 填充基本信息
  requestForm.method = record.method
  requestForm.url = record.url
  requestForm.body = record.body || ''

  // 解析并填充参数
  requestForm.params = []
  if (record.params) {
    try {
      const params = record.params
      if (Array.isArray(params)) {
        requestForm.params = params.map(param => ({
          checked: true,
          key: param.key || '',
          value: param.value || '',
          description: param.description || ''
        }))
      } else if (typeof params === 'object') {
        // 如果是对象格式，转换为数组格式
        requestForm.params = Object.entries(params).map(([key, value]) => ({
          checked: true,
          key,
          value: String(value),
          description: ''
        }))
      }
    } catch (e) {
      console.error('解析参数失败:', e)
    }
  }

  // 解析并填充请求头
  requestForm.headers = []
  if (record.headers) {
    try {
      const headers = record.headers
      if (Array.isArray(headers)) {
        requestForm.headers = headers.map(header => ({
          checked: true,
          key: header.key || '',
          value: header.value || '',
          description: header.description || ''
        }))
      } else if (typeof headers === 'object') {
        // 如果是对象格式，转换为数组格式
        requestForm.headers = Object.entries(headers).map(([key, value]) => ({
          checked: true,
          key,
          value: String(value),
          description: ''
        }))
      }
    } catch (e) {
      console.error('解析请求头失败:', e)
    }
  }

  // 加载保存的响应数据
  if (record.response_body) {
    try {
      // 尝试解析JSON响应
      responseData.value = typeof record.response_body === 'string' ? JSON.parse(record.response_body) : record.response_body
    } catch (e) {
      // 如果不是JSON，直接显示文本
      responseData.value = record.response_body
    }

    // 设置响应状态和时间
    responseStatus.value = record.status_code ? `${record.status_code} ${record.response_status_text || ''}` : ''
    responseTime.value = record.response_time ? `${record.response_time} ms` : ''
    responseSize.value = record.response_size || ''
    responseHeaders.value = record.response_headers || null
  } else {
    // 清空响应数据
    responseData.value = null
    responseStatus.value = ''
    responseTime.value = ''
    responseSize.value = ''
    responseHeaders.value = null
  }

  message.success(`已加载请求: ${record.method} ${record.url}`)
}

// 格式化时间显示
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

// 获取请求方法对应的颜色
const getMethodColor = (method) => {
  const colorMap = {
    'GET': 'info',
    'POST': 'success',
    'PUT': 'warning',
    'DELETE': 'error',
    'PATCH': 'default'
  }
  return colorMap[method] || 'default'
}

// 创建执行历史记录
const createExecutionHistory = async (config, response, responseTime, success) => {
  try {
    // 计算响应大小
    let responseSize = ''
    if (response && response.data) {
      const jsonSize = JSON.stringify(response.data).length
      responseSize = jsonSize < 1024 ? `${jsonSize} B` : `${(jsonSize / 1024).toFixed(2)} KB`
    }

    const historyData = {
      method: config.method.toUpperCase(),
      url: config.url,
      params: config.params || {},
      headers: config.headers || {},
      body: config.data ? (typeof config.data === 'string' ? config.data : JSON.stringify(config.data)) : null,
      status_code: response ? response.status : null,
      response_time: responseTime,
      success: success,
      // 新增响应相关字段
      response_headers: response ? response.headers : null,
      response_body: response && response.data ? (typeof response.data === 'string' ? response.data : JSON.stringify(response.data)) : null,
      response_size: responseSize,
      response_status_text: response ? response.statusText : null
    }

    await api.createApiExecutionHistory(historyData)

    // 刷新最近调用列表
    await fetchRecentRequests()
  } catch (error) {
    console.error('创建执行历史记录失败:', error)
    // 不显示错误消息，避免干扰用户体验
  }
}

// 格式化请求体JSON
const formatRequestBody = () => {
  if (!requestForm.body) {
    message.warning('请先输入请求体内容')
    return
  }

  try {
    const parsed = JSON.parse(requestForm.body)
    requestForm.body = JSON.stringify(parsed, null, 2)
    message.success('JSON格式化成功')
  } catch (e) {
    message.error('JSON格式不正确，无法格式化')
  }
}

// 清空表单
const clearForm = () => {
  requestForm.url = ''
  requestForm.params = []
  requestForm.headers = []
  requestForm.body = ''
  responseData.value = null
  responseStatus.value = ''
  responseTime.value = ''
  responseSize.value = ''
  responseHeaders.value = null // 清空响应头信息
  selectedRequestId.value = null
}

// 获取验证码
const getCaptcha = async () => {
  if (!captchaForm.captcha_url) {
    message.error('请输入验证码地址')
    return
  }

  try {
    captchaLoading.value = true

    // 准备请求参数
    let captcha_headers = {}
    let captcha_body = {}

    // 处理请求头
    if (captchaForm.captcha_headers) {
      try {
        captcha_headers = JSON.parse(captchaForm.captcha_headers)
      } catch (e) {
        message.error('请求头格式不正确，请输入有效的JSON')
        return
      }
    }

    // 处理请求体
    if (captchaForm.captcha_body) {
      try {
        captcha_body = JSON.parse(captchaForm.captcha_body)
      } catch (e) {
        message.error('请求体格式不正确，请输入有效的JSON')
        return
      }
    }

    const response = await api.getCaptchaForRequest({
      captcha_url: captchaForm.captcha_url,
      captcha_method: captchaForm.captcha_method,
      captcha_headers: captcha_headers,
      captcha_body: captcha_body,
      captcha_image_path: captchaForm.captcha_image_path,
      captcha_key_path: captchaForm.captcha_key_path,
      user_request_body: requestForm.body
    })

    if (response.code === 200) {
      const { code, codeKey, updatedRequestBody } = response.data

      // 如果有更新的请求体，则替换当前请求体
      if (updatedRequestBody) {
        requestForm.body = updatedRequestBody
        message.success(`验证码获取成功！已自动替换请求体中的占位符。验证码: ${code}`)
      } else {
        message.success(`验证码获取成功！验证码: ${code}, codeKey: ${codeKey}`)
      }

      showCaptchaModal.value = false
    } else {
      message.error(response.msg || '验证码获取失败')
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    message.error('获取验证码失败')
  } finally {
    captchaLoading.value = false
  }
}

// 页面加载时获取最近调用记录
onMounted(() => {
  fetchRecentRequests()
})
// 在setup() 中添加watch
watch(
  () => requestForm.body,
  (newVal) => {
    if (newVal && newVal.trim()) {
      try {
        const parsed = JSON.parse(newVal);
        requestForm.body = JSON.stringify(parsed, null, 2);
      } catch (e) {
        // 非JSON格式保持原样
      }
    }
  },
  { immediate: true }
);
</script>

<template>
  <NLayout has-sider wh-full>
    <!-- 左侧最近调用列表 -->
    <NLayoutSider
      bordered
      content-style="padding: 16px;"
      :collapsed-width="0"
      :width="290"
      show-trigger="arrow-circle"
    >
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium">最近调用</h3>
        <NButton size="small" @click="fetchRecentRequests" :loading="loadingRecent">
          <TheIcon icon="material-symbols:refresh" :size="16" />
        </NButton>
      </div>

      <NScrollbar style="max-height: calc(100vh - 200px); width: 250px;">
        <NList v-if="recentRequests.length > 0">
          <NListItem
            v-for="request in recentRequests"
            :key="request.id"
            class="cursor-pointer hover:bg-gray-50 rounded-lg p-2 mb-2"
            :class="{ 'bg-blue-50 border border-blue-200': selectedRequestId === request.id }"
            @click="loadRequestFromHistory(request)"
          >
            <div class="flex items-start space-x-2">
              <!-- 左侧方法图标 -->
              <div class="flex-shrink-0 mt-1">
                <NTag :type="getMethodColor(request.method)" size="small" class="method-tag">
                  {{ request.method }}
                </NTag>
              </div>

              <!-- 右侧内容 -->
              <div class="flex-1 min-w-0 overflow-hidden overflow-x-hidden">
                <div class="truncate w-[180px]" :title="request.url">
                  {{ request.url }}
                </div>
              </div>
            </div>
          </NListItem>
        </NList>
        <div v-else-if="!loadingRecent" class="text-center text-gray-500 py-8">
          <TheIcon icon="material-symbols:history" :size="48" class="mb-2" />
          <div>暂无调用记录</div>
        </div>
      </NScrollbar>
    </NLayoutSider>

    <!-- 右侧主要内容 -->
    <NLayoutContent>
      <CommonPage show-footer title="API请求">
        <template #action>
          <div>
            <NButton
              class="float-right mr-15"
              type="primary"
              @click="saveApi"
              :disabled="!responseData"
            >
              <TheIcon icon="material-symbols:save" :size="18" class="mr-5" />保存接口
            </NButton>
            <NButton
              class="float-right mr-15"
              @click="clearForm"
            >
              <TheIcon icon="material-symbols:refresh" :size="18" class="mr-5" />重置
            </NButton>
          </div>
        </template>
    
    <!-- 请求区域 -->
    <NCard title="请求" class="mb-4">
      <div class="flex items-center mb-4">
        <NSelect
          v-model:value="requestForm.method"
          :options="methodOptions"
          style="width: 120px"
        />
        <NInput
          v-model:value="requestForm.url"
          placeholder="请输入请求URL"
          class="ml-2 flex-1"
          @blur="parseUrlParams(requestForm.url)"
        />
        <NButton
          type="primary"
          class="ml-2"
          :loading="isLoading"
          @click="sendRequest"
        >
          发送
        </NButton>
      </div>
      
      <NTabs type="line" animated>
        <NTabPane name="params" tab="Params">
          <div class="flex justify-end mb-2">
            <NButton size="small" @click="addParam">
              <TheIcon icon="material-symbols:add" :size="16" class="mr-1" />添加参数
            </NButton>
          </div>
          
          <!-- 使用自定义表单替代数据表格 -->
          <div class="custom-table">
            <div class="table-header flex">
              <div style="width: 50px"></div>
              <div style="flex: 1">Key</div>
              <div style="flex: 1">Value</div>
              <div style="flex: 1">Description</div>
              <div style="width: 50px"></div>
            </div>
            
            <div v-for="(param, index) in requestForm.params" :key="index" class="table-row flex items-center">
              <div style="width: 50px">
                <NCheckbox v-model:checked="param.checked" />
              </div>
              <div style="flex: 1">
                <NInput v-model:value="param.key" placeholder="参数名" />
              </div>
              <div style="flex: 1">
                <NInput v-model:value="param.value" placeholder="参数值" />
              </div>
              <div style="flex: 1">
                <NInput v-model:value="param.description" placeholder="描述" />
              </div>
              <div style="width: 50px">
                <NButton quaternary circle @click="removeParam(index)">
                  <TheIcon icon="material-symbols:delete-outline" />
                </NButton>
              </div>
            </div>
          </div>
        </NTabPane>
        
        <NTabPane name="headers" tab="Headers">
          <div class="flex justify-end mb-2">
            <NButton size="small" @click="addHeader">
              <TheIcon icon="material-symbols:add" :size="16" class="mr-1" />添加请求头
            </NButton>
          </div>
          
          <!-- 使用自定义表单替代数据表格 -->
          <div class="custom-table">
            <div class="table-header flex">
              <div style="width: 50px"></div>
              <div style="flex: 1">Key</div>
              <div style="flex: 1">Value</div>
              <div style="flex: 1">Description</div>
              <div style="width: 50px"></div>
            </div>
            
            <div v-for="(header, index) in requestForm.headers" :key="index" class="table-row flex items-center">
              <div style="width: 50px">
                <NCheckbox v-model:checked="header.checked" />
              </div>
              <div style="flex: 1">
                <NInput v-model:value="header.key" placeholder="头部名" />
              </div>
              <div style="flex: 1">
                <NInput v-model:value="header.value" placeholder="头部值" />
              </div>
              <div style="flex: 1">
                <NInput v-model:value="header.description" placeholder="描述" />
              </div>
              <div style="width: 50px">
                <NButton quaternary circle @click="removeHeader(index)">
                  <TheIcon icon="material-symbols:delete-outline" />
                </NButton>
              </div>
            </div>
          </div>
        </NTabPane>
        
        <NTabPane name="body" tab="Body">
          <div style="display: flex; flex-direction: column; gap: 8px">
            <NInput
              v-model:value="requestForm.body"
              type="textarea"
              placeholder="请输入请求体 (JSON格式)，支持验证码占位符: ${code}, ${codeKey}"
              :autosize="{ minRows: 5, maxRows: 15 }"
            />
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="display: flex; gap: 8px;">
                <NButton
                  secondary
                  type="info"
                  @click="showCaptchaModal = true"
                  size="small"
                >
                  <TheIcon icon="material-symbols:security" :size="16" class="mr-1" />
                  获取验证码
                </NButton>
              </div>
              <NButton
                secondary
                type="primary"
                @click="formatRequestBody"
                style="margin-top: 0"
              >
                美化JSON
              </NButton>
            </div>
          </div>
        </NTabPane>
      </NTabs>
    </NCard>
    
    <!-- 响应区域 -->
    <NCard title="响应" v-if="responseData">
      <div class="flex items-center mb-4 text-sm">
        <div class="mr-4">
          <span class="font-bold">状态:</span>
          <span :class="responseStatus.startsWith('2') ? 'text-green-500' : 'text-red-500'">
            {{ responseStatus }}
          </span>
        </div>
        <div class="mr-4">
          <span class="font-bold">时间:</span> {{ responseTime }}
        </div>
        <div>
          <span class="font-bold">大小:</span> {{ responseSize }}
        </div>
      </div>
      
      <NTabs type="line" animated>
        <NTabPane name="body" tab="Body">
          <div class="response-code-wrapper">
            <NCode :code="JSON.stringify(responseData, null, 2)" language="json" />
          </div>
        </NTabPane>
        
        <NTabPane name="headers" tab="Headers">
          <div v-if="responseHeaders && Object.keys(responseHeaders).length > 0" class="response-code-wrapper">
            <div v-for="(value, key) in responseHeaders" :key="key" class="header-item">
              <span class="header-key">{{ key }}:</span>
              <span class="header-value">{{ value }}</span>
            </div>
          </div>
          <div v-else class="text-gray-500">暂无响应头信息</div>
        </NTabPane>
        
        <NTabPane name="cookies" tab="Cookies">
          <!-- 这里可以展示Cookie信息 -->
          <div class="text-gray-500">暂无Cookie信息</div>
        </NTabPane>
      </NTabs>
    </NCard>
    
    <!-- 保存接口对话框 -->
    <NModal
      v-model:show="showSaveModal"
      preset="card"
      title="保存接口"
      style="width: 500px"
      :bordered="false"
      size="huge"
      :segmented="{
        content: true,
        footer: 'soft'
      }"
    >
      <NForm ref="formRef" :model="saveForm" label-placement="left" label-width="80">
        <NFormItem label="接口名称" required>
          <NInput v-model:value="saveForm.api_name" placeholder="请输入接口名称" />
        </NFormItem>
        <NFormItem label="所属项目" required>
          <NSelect
            v-model:value="saveForm.project_id"
            :options="projects"
            placeholder="请选择所属项目"
            clearable
            @update:value="onProjectChange"
          />
        </NFormItem>
        <NFormItem label="所属模块">
          <NSelect
            v-model:value="saveForm.module_id"
            :options="modules"
            placeholder="请选择所属模块（可选）"
            clearable
            :disabled="!saveForm.project_id"
          />
        </NFormItem>
      </NForm>
      
      <template #footer>
        <div style="text-align: right">
          <NButton class="mr-2" @click="cancelSave">取消</NButton>
          <NButton type="primary" :loading="saveLoading" @click="confirmSave">确认</NButton>
        </div>
      </template>
    </NModal>

    <!-- 验证码配置对话框 -->
    <NModal
      v-model:show="showCaptchaModal"
      preset="card"
      title="验证码配置"
      style="width: 600px"
      :bordered="false"
      size="huge"
      :segmented="{
        content: true,
        footer: 'soft'
      }"
    >
      <NForm :model="captchaForm" label-placement="left" label-width="100">
        <NFormItem label="验证码地址" required>
          <NInput
            v-model:value="captchaForm.captcha_url"
            placeholder="请输入验证码获取URL"
            clearable
          />
        </NFormItem>

        <NFormItem label="请求方式">
          <NSelect
            v-model:value="captchaForm.captcha_method"
            :options="[
              { label: 'GET', value: 'GET' },
              { label: 'POST', value: 'POST' },
              { label: 'PUT', value: 'PUT' },
              { label: 'DELETE', value: 'DELETE' }
            ]"
            placeholder="请选择请求方式"
          />
        </NFormItem>

        <NFormItem label="请求头">
          <NInput
            v-model:value="captchaForm.captcha_headers"
            type="textarea"
            :rows="2"
            placeholder='JSON格式，例如: {"Content-Type": "application/json"}'
            clearable
          />
        </NFormItem>

        <NFormItem label="请求体">
          <NInput
            v-model:value="captchaForm.captcha_body"
            type="textarea"
            :rows="3"
            placeholder='JSON格式，例如: {"type": "captcha"}'
            clearable
          />
        </NFormItem>

        <NFormItem label="图片字段路径">
          <NInput
            v-model:value="captchaForm.captcha_image_path"
            placeholder="如: content.imageBase64 或 data.image"
            clearable
          />
        </NFormItem>

        <NFormItem label="Key字段路径">
          <NInput
            v-model:value="captchaForm.captcha_key_path"
            placeholder="如: content.codeKey 或 data.key"
            clearable
          />
        </NFormItem>

        <div style="font-size: 12px; color: #666; margin-bottom: 16px;">
          <strong>说明：</strong><br/>
          • 系统会自动获取验证码并识别<br/>
          • 识别成功后会自动替换请求体中的 ${code} 和 ${codeKey} 占位符<br/>
          • 支持的占位符格式：${code}, ${codekey}, ${codeKey}, {code}, {codekey}, {codeKey}
        </div>
      </NForm>

      <template #footer>
        <div style="text-align: right">
          <NButton class="mr-2" @click="showCaptchaModal = false">取消</NButton>
          <NButton type="primary" :loading="captchaLoading" @click="getCaptcha">获取验证码</NButton>
        </div>
      </template>
    </NModal>
      </CommonPage>
    </NLayoutContent>
  </NLayout>
</template>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
.mr-1 {
  margin-right: 4px;
}
.mr-2 {
  margin-right: 8px;
}
.mr-4 {
  margin-right: 16px;
}
.mr-5 {
  margin-right: 5px;
}
.ml-2 {
  margin-left: 8px;
}
.flex {
  display: flex;
}
.flex-1 {
  flex: 1;
}
.items-center {
  align-items: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.text-sm {
  font-size: 14px;
}
.text-xs {
  font-size: 12px;
}
.text-lg {
  font-size: 18px;
}
.font-bold {
  font-weight: bold;
}
.font-medium {
  font-weight: 500;
}
.cursor-pointer {
  cursor: pointer;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.rounded-lg {
  border-radius: 8px;
}
.border {
  border-width: 1px;
}
.border-blue-200 {
  border-color: #bfdbfe;
}
.bg-gray-50 {
  background-color: #f9fafb;
}
.bg-blue-50 {
  background-color: #eff6ff;
}
.text-center {
  text-align: center;
}
.py-8 {
  padding-top: 32px;
  padding-bottom: 32px;
}
.p-2 {
  padding: 8px;
}
.mb-2 {
  margin-bottom: 8px;
}
.mt-1 {
  margin-top: 4px;
}
.space-x-2 > * + * {
  margin-left: 8px;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.min-w-0 {
  min-width: 0;
}
.text-green-600 {
  color: #059669;
}
.text-red-600 {
  color: #dc2626;
}
.text-gray-400 {
  color: #9ca3af;
}
.text-gray-900 {
  color: #111827;
}
.method-tag {
  min-width: 50px;
  text-align: center;
}
.text-green-500 {
  color: #10b981;
}
.text-red-500 {
  color: #ef4444;
}
.text-gray-500 {
  color: #6b7280;
}
.custom-table {
  border: 1px solid #eee;
  border-radius: 3px;
}

.table-header {
  background-color: #f9f9f9;
  padding: 8px;
  font-weight: bold;
  border-bottom: 1px solid #eee;
}

.table-row {
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.table-row:last-child {
  border-bottom: none;
}

/* 添加响应头样式 */
.header-item {
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.header-item:last-child {
  border-bottom: none;
}

.header-key {
  font-weight: bold;
  margin-right: 8px;
}

.header-value {
  word-break: break-all;
}

/* 添加响应代码区域的样式 */
.response-code-wrapper {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 3px;
}
</style>
