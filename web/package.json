{"name": "vue-fastapi-admin-web", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue .", "lint:fix": "eslint --fix --ext .js,.vue .", "lint:staged": "lint-staged", "prettier": "npx prettier --write ."}, "dependencies": {"@iconify/json": "^2.2.351", "@iconify/vue": "^4.3.0", "@unocss/eslint-config": "^0.55.7", "@vicons/ionicons5": "^0.13.0", "@vueuse/core": "^10.11.1", "@zclzone/eslint-config": "^0.0.4", "axios": "^1.10.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "eslint": "^8.57.1", "highlight.js": "^11.11.1", "lodash-es": "^4.17.21", "naive-ui": "^2.42.0", "pinia": "^2.3.1", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.89.2", "typescript": "^5.8.3", "unocss": "^0.55.7", "unplugin-auto-import": "^0.16.7", "unplugin-icons": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.5.17", "vue-i18n": "^9.14.4", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "vite": "^4.5.14"}, "lint-staged": {"*.{js,vue}": ["eslint --ext .js,.vue ."]}, "eslintConfig": {"extends": ["@zclzone", "@unocss", ".eslint-global-variables.json"]}}