# 环境配置验证码占位符使用示例

## 完整配置步骤

### 1. 基本环境配置

在环境管理页面中，创建或编辑环境配置：

```
环境名称: 测试环境
环境类型: 测试环境
主机地址: 10.162.22.38
端口号: 32049
```

### 2. Token自动获取配置

启用"自动刷新Token"并配置：

```
Token获取URL: http://10.162.22.38:32049/auth/login
请求方式: POST
Token字段名: token
Token路径: content.token
刷新间隔: 3600秒
```

**请求头配置：**
```json
{
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

**请求体配置（使用占位符）：**
```json
{
  "code": "${code}",
  "codeKey": "${codekey}",
  "password": "2C6i4jZhS7U97Jj1uB4Biw==",
  "username": "tanxi",
  "grantType": "password"
}
```

### 3. 验证码配置

启用"启用验证码"并配置：

```
验证码地址: http://10.162.22.38:32049/auth/captcha
请求方式: GET
图片字段路径: content.imageBase64
Key字段路径: content.codeKey
```

## 工作流程

### 自动Token刷新流程

1. **触发条件**：
   - 定时刷新（根据刷新间隔）
   - 手动点击"立即刷新"按钮
   - Token过期时自动刷新

2. **执行步骤**：
   ```
   步骤1: 调用验证码API
   GET http://10.162.22.38:32049/auth/captcha
   
   步骤2: 解析响应获取验证码图片和Key
   响应: {
     "content": {
       "imageBase64": "data:image/png;base64,iVBORw0KGgo...",
       "codeKey": "f2225d3728304665be02ed874c7370ad"
     }
   }
   
   步骤3: 使用ddddocr识别验证码
   识别结果: "1234"
   
   步骤4: 替换请求体中的占位符
   原始: {
     "code": "${code}",
     "codeKey": "${codekey}",
     "password": "2C6i4jZhS7U97Jj1uB4Biw==",
     "username": "tanxi",
     "grantType": "password"
   }
   
   替换后: {
     "code": "1234",
     "codeKey": "f2225d3728304665be02ed874c7370ad",
     "password": "2C6i4jZhS7U97Jj1uB4Biw==",
     "username": "tanxi",
     "grantType": "password"
   }
   
   步骤5: 调用Token获取API
   POST http://10.162.22.38:32049/auth/login
   Body: 替换后的请求体
   
   步骤6: 解析响应获取Token并更新环境配置
   ```

## 支持的占位符格式

### 验证码内容占位符
- `${code}` - 标准格式
- `${CODE}` - 大写格式
- `{code}` - 不带$符号

### 验证码Key占位符
- `${codekey}` - 小写格式
- `${codeKey}` - 驼峰命名格式
- `${CODEKEY}` - 大写格式
- `{codekey}` - 不带$符号小写
- `{codeKey}` - 不带$符号驼峰命名

## 常见配置示例

### 示例1：标准登录接口
```json
{
  "username": "admin",
  "password": "123456",
  "captcha": "${code}",
  "captchaId": "${codekey}",
  "rememberMe": false
}
```

### 示例2：复杂嵌套结构
```json
{
  "auth": {
    "user": "admin",
    "pass": "123456",
    "verification": {
      "code": "${code}",
      "key": "${codeKey}"
    }
  },
  "options": {
    "timeout": 30000
  }
}
```

### 示例3：混合格式
```json
{
  "loginInfo": {
    "username": "user",
    "password": "pass"
  },
  "securityCode": "{code}",
  "sessionKey": "${codekey}",
  "timestamp": 1234567890
}
```

## 测试和调试

### 测试验证码地址
在环境配置页面中，点击"测试验证码地址"按钮可以：
- 验证验证码API是否可访问
- 检查响应数据格式是否正确
- 测试验证码识别功能
- 查看识别出的验证码内容

### 测试Token配置
点击"测试配置"按钮可以：
- 完整测试Token获取流程
- 验证占位符替换是否正确
- 检查Token提取是否成功

### 日志查看
系统会记录详细的日志信息：
- 验证码获取过程
- 识别结果
- 占位符替换详情
- Token获取结果

## 注意事项

1. **占位符格式**：确保占位符格式正确，大小写敏感
2. **JSON格式**：请求体必须是有效的JSON格式
3. **网络连通性**：确保验证码API和Token API都可以正常访问
4. **识别准确率**：复杂验证码可能识别失败，系统会自动重试
5. **安全性**：验证码和Token信息会记录在日志中，注意日志安全

## 故障排除

### 常见问题

1. **占位符未被替换**
   - 检查占位符格式是否正确
   - 确认验证码识别是否成功
   - 查看日志中的替换详情

2. **验证码识别失败**
   - 检查验证码图片是否正确获取
   - 验证图片字段路径配置
   - 尝试重新测试验证码地址

3. **Token获取失败**
   - 确认Token API地址正确
   - 检查请求体格式
   - 验证用户名密码等其他参数

### 调试步骤

1. 先测试验证码地址，确保能正确获取和识别验证码
2. 再测试Token配置，检查整个流程
3. 查看系统日志，定位具体问题
4. 根据错误信息调整配置
