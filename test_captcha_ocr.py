#!/usr/bin/env python3
"""
测试验证码OCR识别功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.captcha_service import captcha_service

async def test_captcha_recognition():
    """测试验证码识别功能"""
    print("=== 测试验证码识别功能 ===")
    
    # 测试用的Base64验证码图片（这是一个简单的示例）
    # 实际使用时会从验证码API获取
    test_image_base64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    try:
        # 测试验证码识别
        result = await captcha_service.recognize_captcha(test_image_base64, max_retries=2)
        
        if result:
            print(f"✅ 验证码识别成功: {result}")
        else:
            print("❌ 验证码识别失败")
            
    except Exception as e:
        print(f"❌ 测试验证码识别失败: {e}")

async def test_full_captcha_flow():
    """测试完整的验证码获取和识别流程"""
    print("\n=== 测试完整验证码流程 ===")
    
    # 使用您提供的验证码API
    captcha_url = "http://10.162.22.38:32049/auth/captcha"
    
    try:
        # 测试完整流程
        result = await captcha_service.get_captcha_with_recognition(
            captcha_url=captcha_url,
            captcha_method="GET",
            captcha_image_path="content.imageBase64",
            captcha_key_path="content.codeKey",
            max_retries=2
        )
        
        if result:
            recognized_code, code_key = result
            print(f"✅ 完整流程测试成功")
            print(f"   识别结果: {recognized_code}")
            print(f"   codeKey: {code_key}")
            print(f"   可用于登录请求体: {{'code': '{recognized_code}', 'codeKey': '{code_key}'}}")
        else:
            print("❌ 完整流程测试失败")
            
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")

async def test_image_processing():
    """测试ddddocr图像处理功能"""
    print("\n=== 测试ddddocr图像处理功能 ===")

    try:
        # 创建一个简单的测试图像
        import base64
        import io
        from PIL import Image, ImageDraw, ImageFont

        # 创建一个更清晰的验证码图片
        img = Image.new('RGB', (120, 50), color='white')
        draw = ImageDraw.Draw(img)

        # 绘制简单的文本（使用更大的字体）
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
        except:
            # 如果找不到字体，使用默认字体
            font = ImageFont.load_default()

        draw.text((20, 15), "1234", fill='black', font=font)

        # 转换为Base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.getvalue()).decode()
        img_data_url = f"data:image/png;base64,{img_base64}"

        print(f"创建测试图片，Base64长度: {len(img_base64)}")

        # 测试图像解码
        image_bytes = captcha_service._decode_base64_image(img_data_url)
        if image_bytes:
            print(f"✅ Base64解码成功，字节长度: {len(image_bytes)}")

            # 测试ddddocr识别
            recognized_text = captcha_service._ocr_with_ddddocr(image_bytes)
            if recognized_text:
                print(f"✅ ddddocr识别成功: {recognized_text}")
            else:
                print("❌ ddddocr识别失败")
        else:
            print("❌ Base64解码失败")

    except Exception as e:
        print(f"❌ 图像处理测试失败: {e}")

async def main():
    """主测试函数"""
    print("开始测试验证码OCR识别功能...\n")
    
    try:
        # 测试图像处理功能
        await test_image_processing()
        
        # 测试验证码识别
        await test_captcha_recognition()
        
        # 测试完整流程
        await test_full_captcha_flow()
        
        print("\n=== 测试完成 ===")
        print("\n📝 说明:")
        print("1. 当前使用ddddocr库进行验证码识别")
        print("2. ddddocr是一个简单易用的验证码识别库，支持：")
        print("   - 数字验证码识别")
        print("   - 字母验证码识别")
        print("   - 数字+字母混合验证码识别")
        print("   - 简单的中文验证码识别")
        print("3. 验证码识别功能已集成到Token自动刷新流程中")
        print("4. ddddocr无需额外配置，开箱即用")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    finally:
        # 关闭服务
        await captcha_service.close()

if __name__ == "__main__":
    asyncio.run(main())
