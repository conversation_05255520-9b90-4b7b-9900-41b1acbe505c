# 数据库查询模块功能说明

## 概述

数据库查询模块是一个功能完整的数据库管理和查询工具，支持多种数据库类型，提供了连接管理、SQL执行、查询历史等核心功能。

## 功能特性

### 1. 数据库连接管理
- **支持多种数据库类型**: MySQL、PostgreSQL、SQLite
- **连接配置管理**: 支持创建、编辑、删除数据库连接配置
- **连接测试**: 实时测试数据库连接状态
- **安全管理**: 密码加密存储，界面显示时隐藏敏感信息

### 2. SQL执行器
- **可视化SQL编辑器**: 支持语法高亮的SQL编辑界面
- **数据库结构浏览**: 左侧树形展示数据库表结构
- **智能SQL生成**: 点击表名自动生成SELECT语句
- **查询结果展示**: 表格形式展示查询结果，支持大数据量分页
- **执行统计**: 显示执行时间、影响行数等统计信息

### 3. 查询历史管理
- **历史记录**: 自动保存所有SQL查询历史
- **收藏功能**: 支持收藏常用查询语句
- **快速复用**: 一键复制历史查询到执行器
- **筛选搜索**: 按连接、状态、收藏状态筛选历史记录

### 4. 安全控制
- **权限管理**: 基于RBAC权限控制，用户只能访问自己的连接
- **SQL限制**: 支持设置查询结果行数限制，防止大数据量查询
- **连接隔离**: 每个用户的数据库连接相互隔离

## 技术架构

### 后端技术栈
- **FastAPI**: 高性能异步Web框架
- **Tortoise ORM**: 异步数据库ORM
- **aiomysql/asyncpg/aiosqlite**: 异步数据库驱动
- **Pydantic**: 数据验证和序列化

### 前端技术栈
- **Vue 3**: 现代化前端框架
- **Naive UI**: 企业级UI组件库
- **Highlight.js**: 代码语法高亮

### 数据库设计

#### 数据库连接表 (database_connections)
```sql
CREATE TABLE database_connections (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '连接名称',
    db_type VARCHAR(20) NOT NULL COMMENT '数据库类型',
    host VARCHAR(255) NOT NULL COMMENT '主机地址',
    port INT NOT NULL COMMENT '端口号',
    database VARCHAR(100) NOT NULL COMMENT '数据库名称',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    charset VARCHAR(20) DEFAULT 'utf8mb4' COMMENT '字符集',
    description TEXT COMMENT '连接描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    max_connections INT DEFAULT 10 COMMENT '最大连接数',
    connection_timeout INT DEFAULT 30 COMMENT '连接超时时间(秒)',
    user_id BIGINT NOT NULL COMMENT '创建用户ID',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

#### 查询历史表 (query_history)
```sql
CREATE TABLE query_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    query_name VARCHAR(200) COMMENT '查询名称',
    sql_content TEXT NOT NULL COMMENT 'SQL语句内容',
    database_connection_id BIGINT NOT NULL COMMENT '数据库连接ID',
    execution_time INT COMMENT '执行时间(毫秒)',
    affected_rows INT COMMENT '影响行数',
    result_count INT COMMENT '结果行数',
    status VARCHAR(20) NOT NULL COMMENT '执行状态',
    error_message TEXT COMMENT '错误信息',
    is_favorite BOOLEAN DEFAULT FALSE COMMENT '是否收藏',
    user_id BIGINT NOT NULL COMMENT '执行用户ID',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## API接口

### 数据库连接管理
- `GET /api/v1/database_query/connections` - 获取连接列表
- `POST /api/v1/database_query/connections` - 创建连接
- `PUT /api/v1/database_query/connections/{id}` - 更新连接
- `DELETE /api/v1/database_query/connections/{id}` - 删除连接
- `POST /api/v1/database_query/connections/{id}/test` - 测试连接

### SQL执行
- `POST /api/v1/database_query/execute` - 执行SQL查询
- `GET /api/v1/database_query/connections/{id}/schema` - 获取数据库表结构
- `GET /api/v1/database_query/connections/{id}/tables/{table}/schema` - 获取表详细结构

### 查询历史
- `GET /api/v1/database_query/history` - 获取查询历史
- `POST /api/v1/database_query/history/{id}/favorite` - 切换收藏状态
- `DELETE /api/v1/database_query/history/{id}` - 删除查询历史

## 页面结构

### 1. 连接管理页面 (`/database_query/connection_manage`)
- 数据库连接列表展示
- 连接的增删改查操作
- 连接测试功能
- 连接状态管理

### 2. SQL执行器页面 (`/database_query/sql_executor`)
- 左侧数据库结构树
- 右侧SQL编辑器和结果展示
- 支持多标签页展示结果
- 查询统计信息

### 3. 查询历史页面 (`/database_query/history`)
- 历史查询列表
- 筛选和搜索功能
- 收藏管理
- 快速复用到执行器

## 使用流程

1. **配置数据库连接**
   - 进入"连接管理"页面
   - 点击"添加"按钮创建新连接
   - 填写数据库连接信息
   - 点击"测试连接"验证配置
   - 保存连接配置

2. **执行SQL查询**
   - 进入"SQL执行器"页面
   - 选择数据库连接
   - 在左侧浏览数据库表结构
   - 在右侧编辑器中输入SQL语句
   - 点击"执行查询"按钮
   - 查看查询结果和统计信息

3. **管理查询历史**
   - 进入"查询历史"页面
   - 查看所有历史查询记录
   - 使用筛选功能查找特定查询
   - 收藏常用查询
   - 复用历史查询到执行器

## 安全注意事项

1. **数据库连接安全**
   - 数据库密码加密存储
   - 连接配置用户隔离
   - 支持连接超时设置

2. **查询安全**
   - 设置查询结果行数限制
   - 记录所有查询操作
   - 支持查询权限控制

3. **数据安全**
   - 查询历史用户隔离
   - 敏感信息脱敏显示
   - 支持查询审计

## 扩展功能

未来可以考虑添加的功能：
- SQL语句格式化和美化
- 查询性能分析
- 数据导出功能
- 批量SQL执行
- 数据库备份和恢复
- 更多数据库类型支持（Oracle、SQL Server等）

## 部署说明

1. **后端部署**
   - 确保安装了所需的数据库驱动包
   - 运行数据库迁移创建相关表
   - 在菜单表中添加相应的菜单配置

2. **前端部署**
   - 确保前端路由配置正确
   - 检查API接口地址配置
   - 验证组件依赖是否完整

3. **权限配置**
   - 为相关角色分配数据库查询模块权限
   - 配置菜单访问权限
   - 设置API接口权限
