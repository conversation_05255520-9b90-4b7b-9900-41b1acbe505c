# Token刷新日志优化说明

## 优化目标

当在"Token自动获取配置"页面点击"立即刷新"时，在后台日志中显示详细的获取Token的请求链接和参数（替换占位符后的）。

## 优化内容

### 1. 增强日志记录格式

使用更清晰的日志格式，包含emoji图标和分隔线，便于在日志中快速定位和阅读：

- 🚀 开始获取Token
- 📍 请求方式
- 🔗 请求链接  
- 📋 请求头
- 📦 请求体
- 🔐 验证码相关信息
- 📥 响应信息
- ✅ 成功信息
- ❌ 错误信息

### 2. 详细的请求信息记录

现在会记录以下详细信息：

#### 基本请求信息
- 环境名称和ID
- HTTP请求方式（GET/POST/PUT等）
- 完整的请求URL（包含替换后的占位符）
- 请求头信息（JSON格式）
- 请求体内容（JSON格式）

#### 验证码处理信息（如果启用）
- 验证码获取地址
- 验证码请求方式
- 识别出的验证码和codeKey
- 替换前后的URL对比
- 替换前后的请求体对比

#### 响应信息
- HTTP响应状态码
- 响应头信息
- 响应内容（JSON格式）
- Token提取结果

## 日志示例

### 无验证码的Token获取日志

```
================================================================================
🚀 开始获取Token - 环境: 测试环境 (ID: 1)
================================================================================
ℹ️ 环境未配置验证码，直接使用原始请求参数
🔗 原始URL: http://api.example.com/auth/login
📦 原始请求体: {
  "username": "admin",
  "password": "123456",
  "grantType": "password"
}
📍 请求方式: POST
🔗 请求链接: http://api.example.com/auth/login
📋 请求头: {
  "Content-Type": "application/json"
}
📦 请求体: {
  "username": "admin",
  "password": "123456",
  "grantType": "password"
}
--------------------------------------------------------------------------------
📥 响应状态码: 200
📥 响应头: {'content-type': 'application/json', 'content-length': '156'}
✅ 响应内容: {"code": 200, "data": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}
📊 解析后的响应数据: {
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
🎉 成功获取并更新环境 测试环境 的Token
🔑 获取到的Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
================================================================================
```

### 带验证码的Token获取日志

```
================================================================================
🚀 开始获取Token - 环境: 生产环境 (ID: 2)
================================================================================
🔐 环境配置了验证码，开始获取验证码...
🔐 验证码地址: http://api.example.com/auth/captcha
🔐 验证码请求方式: GET
✅ 验证码获取成功: code=1234, codeKey=abc123def456
🔄 替换前的URL: http://api.example.com/auth/login?key=${codekey}&captcha=${code}
🔄 替换前的请求体: {
  "code": "${code}",
  "codeKey": "${codekey}",
  "username": "admin",
  "password": "123456",
  "grantType": "password"
}
✅ 占位符替换完成
🔄 替换后的URL: http://api.example.com/auth/login?key=abc123def456&captcha=1234
🔄 替换后的请求体: {
  "code": "1234",
  "codeKey": "abc123def456",
  "username": "admin",
  "password": "123456",
  "grantType": "password"
}
📍 请求方式: POST
🔗 请求链接: http://api.example.com/auth/login?key=abc123def456&captcha=1234
📋 请求头: {
  "Content-Type": "application/json"
}
📦 请求体: {
  "code": "1234",
  "codeKey": "abc123def456",
  "username": "admin",
  "password": "123456",
  "grantType": "password"
}
--------------------------------------------------------------------------------
📥 响应状态码: 200
📥 响应头: {'content-type': 'application/json', 'content-length': '156'}
✅ 响应内容: {"code": 200, "data": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}
📊 解析后的响应数据: {
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
🎉 成功获取并更新环境 生产环境 的Token
🔑 获取到的Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
================================================================================
```

### 错误情况的日志

```
================================================================================
🚀 开始获取Token - 环境: 测试环境 (ID: 3)
================================================================================
🔐 环境配置了验证码，开始获取验证码...
🔐 验证码地址: http://api.example.com/auth/captcha
🔐 验证码请求方式: GET
❌ 验证码获取失败，无法继续获取Token
================================================================================
```

或者：

```
📥 响应状态码: 401
📥 响应头: {'content-type': 'application/json', 'content-length': '45'}
❌ 获取Token失败，状态码: 401
❌ 响应内容: {"code": 401, "message": "用户名或密码错误"}
================================================================================
```

## 修改的文件

- `app/services/token_service.py` - 增强了`fetch_token`方法的日志记录

## 优化效果

1. **可视化增强**：使用emoji图标和分隔线，日志更易读
2. **信息完整**：记录完整的请求和响应信息
3. **占位符透明**：清楚显示占位符替换前后的对比
4. **错误诊断**：详细的错误信息便于问题排查
5. **调试友好**：结构化的日志格式便于开发调试

## 使用方法

1. 在环境配置页面点击"立即刷新"按钮
2. 查看后台日志（通常在应用的日志文件中）
3. 搜索包含"🚀 开始获取Token"的日志条目
4. 查看完整的请求和响应信息

这样的日志记录可以帮助开发者和运维人员：
- 快速定位Token获取问题
- 验证占位符替换是否正确
- 检查请求参数是否符合预期
- 分析API响应是否正常
