from enum import Enum, StrEnum


class EnumBase(Enum):
    @classmethod
    def get_member_values(cls):
        return [item.value for item in cls._member_map_.values()]

    @classmethod
    def get_member_names(cls):
        return [name for name in cls._member_names_]


class MethodType(StrEnum):
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    OPTIONS = "OPTIONS"
    HEAD = "HEAD"


class EnvironmentType(StrEnum):
    DEVELOPMENT = "development"  # 开发环境
    TESTING = "testing"  # 测试环境
    STAGING = "staging"  # 预发布环境
    PRODUCTION = "production"  # 生产环境


class AIModelType(StrEnum):
    """AI模型类型枚举"""
    OPENAI = "openai"
    OPENROUTER = "openrouter"
    VOLCENGINE = "volcengine"
    CLAUDE = "claude"
    GEMINI = "gemini"
    QWEN = "qwen"
    BAIDU = "baidu"
    ZHIPU = "zhipu"
    DEEPSEEK = "deepseek"
    MOONSHOT = "moonshot"
    CUSTOM = "custom"


class AIModelStatus(StrEnum):
    """AI模型状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"


class MenuType(StrEnum):
    DIRECTORY = "directory"
    MENU = "menu"
