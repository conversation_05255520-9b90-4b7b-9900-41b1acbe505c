import logging
import asyncio
from datetime import datetime, timezone
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor

from app.controllers.scheduled_task import scheduled_task_controller
from app.controllers.api_test_plan import api_test_plan_controller

logger = logging.getLogger(__name__)


class TaskScheduler:
    def __init__(self):
        # 配置调度器
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 3
        }
        
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        self._started = False

    async def start(self):
        """启动调度器"""
        if not self._started:
            self.scheduler.start()
            self._started = True
            logger.info("任务调度器已启动")

            # 加载现有的定时任务
            await self.load_existing_tasks()

            # 设置Token刷新任务
            await self.setup_token_refresh_task()

    async def shutdown(self):
        """关闭调度器"""
        if self._started:
            self.scheduler.shutdown()
            self._started = False
            logger.info("任务调度器已关闭")

    async def load_existing_tasks(self):
        """加载现有的定时任务"""
        try:
            tasks = await scheduled_task_controller.get_active_tasks()
            for task in tasks:
                await self.add_job(task.id, task.cron_expression, task.plan_id)
            logger.info(f"已加载 {len(tasks)} 个定时任务")
        except Exception as e:
            logger.error(f"加载定时任务失败: {str(e)}")

    async def add_job(self, task_id: int, cron_expression: str, plan_id: int):
        """添加定时任务"""
        try:
            job_id = f"scheduled_task_{task_id}"
            
            # 如果任务已存在，先删除
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
            
            # 添加新任务
            self.scheduler.add_job(
                func=self.execute_test_plan,
                trigger=CronTrigger.from_crontab(cron_expression),
                args=[task_id, plan_id],
                id=job_id,
                name=f"定时执行测试计划_{plan_id}",
                replace_existing=True
            )
            logger.info(f"已添加定时任务: {job_id}, cron: {cron_expression}")
        except Exception as e:
            logger.error(f"添加定时任务失败: {str(e)}")

    async def remove_job(self, task_id: int):
        """删除定时任务"""
        try:
            job_id = f"scheduled_task_{task_id}"
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                logger.info(f"已删除定时任务: {job_id}")
        except Exception as e:
            logger.error(f"删除定时任务失败: {str(e)}")

    async def update_job(self, task_id: int, cron_expression: str, plan_id: int):
        """更新定时任务"""
        await self.remove_job(task_id)
        await self.add_job(task_id, cron_expression, plan_id)

    async def pause_job(self, task_id: int):
        """暂停定时任务"""
        try:
            job_id = f"scheduled_task_{task_id}"
            if self.scheduler.get_job(job_id):
                self.scheduler.pause_job(job_id)
                logger.info(f"已暂停定时任务: {job_id}")
        except Exception as e:
            logger.error(f"暂停定时任务失败: {str(e)}")

    async def resume_job(self, task_id: int):
        """恢复定时任务"""
        try:
            job_id = f"scheduled_task_{task_id}"
            if self.scheduler.get_job(job_id):
                self.scheduler.resume_job(job_id)
                logger.info(f"已恢复定时任务: {job_id}")
        except Exception as e:
            logger.error(f"恢复定时任务失败: {str(e)}")

    async def execute_test_plan(self, task_id: int, plan_id: int):
        """执行测试计划"""
        try:
            logger.info(f"开始执行定时任务 {task_id}，测试计划 {plan_id}")
            
            # 获取测试计划信息
            plan = await api_test_plan_controller.get(id=plan_id)
            if not plan.environment_id:
                logger.error(f"测试计划 {plan_id} 未配置运行环境")
                return
            
            # 执行测试计划
            result = await api_test_plan_controller.batch_execute_test_plan(
                plan_id=plan_id,
                environment_id=plan.environment_id
            )
            
            # 更新任务执行信息
            await scheduled_task_controller.update_run_info(task_id, success=True)
            
            logger.info(f"定时任务 {task_id} 执行完成，通过率: {result.get('pass_rate', 0)}%")
            
        except Exception as e:
            logger.error(f"执行定时任务 {task_id} 失败: {str(e)}")
            # 更新任务执行信息（失败）
            try:
                await scheduled_task_controller.update_run_info(task_id, success=False)
            except:
                pass

    def get_job_info(self, task_id: int):
        """获取任务信息"""
        job_id = f"scheduled_task_{task_id}"
        job = self.scheduler.get_job(job_id)
        if job:
            return {
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time,
                'trigger': str(job.trigger)
            }
        return None

    def list_jobs(self):
        """列出所有任务"""
        jobs = self.scheduler.get_jobs()
        return [
            {
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time,
                'trigger': str(job.trigger)
            }
            for job in jobs
        ]

    async def setup_token_refresh_task(self):
        """设置Token刷新任务"""
        try:
            # 添加Token刷新任务，每5分钟检查一次（更频繁的检查）
            self.scheduler.add_job(
                func=self.refresh_tokens_task,
                trigger='interval',
                minutes=5,
                id='token_refresh_task',
                name='Token自动刷新任务',
                replace_existing=True
            )
            logger.info("Token自动刷新定时任务已设置，每1分钟执行一次")

        except Exception as e:
            logger.error(f"设置Token刷新定时任务失败: {str(e)}")

    async def refresh_tokens_task(self):
        """Token自动刷新定时任务"""
        try:
            from app.services.token_service import token_service

            logger.info("开始执行Token自动刷新任务")

            # 获取需要刷新Token的环境列表
            environment_ids = await token_service.get_environments_need_refresh()

            if not environment_ids:
                logger.debug("没有需要刷新Token的环境")
                return

            logger.info(f"发现 {len(environment_ids)} 个环境需要刷新Token: {environment_ids}")

            # 批量刷新Token
            results = await token_service.refresh_tokens_for_environments(environment_ids)

            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            failed_count = len(results) - success_count

            logger.info(f"Token刷新任务完成，成功: {success_count}, 失败: {failed_count}")

            # 记录失败的环境
            for env_id, success in results.items():
                if not success:
                    logger.warning(f"环境 {env_id} Token刷新失败")

        except Exception as e:
            logger.error(f"Token自动刷新任务执行失败: {str(e)}")


# 创建全局调度器实例
task_scheduler = TaskScheduler()
