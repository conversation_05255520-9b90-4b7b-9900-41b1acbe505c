from fastapi import APIRouter

from app.core.dependency import Depend<PERSON><PERSON><PERSON>son

from .apis import apis_router
from .auditlog import auditlog_router
from .base import base_router
from .depts import depts_router
from .menus import menus_router
from .roles import roles_router
from .users import users_router
from .project import project_router, project_public_router
from .api_requests import api_request_router
from .api_import import api_import_router  # 新增导入
from .environment import environment_router
from .api_execution_history import api_execution_history_router
from .api_test_case import router as api_test_case_router
from .test_case import test_case_router, test_case_public_router
from .api_test_plan import router as api_test_plan_router, public_router as api_test_plan_public_router
from .functional_test_plan import router as functional_test_plan_router, public_router as functional_test_plan_public_router
from .scheduled_task import router as scheduled_task_router, public_router as scheduled_task_public_router
from .faker_data import faker_data_router
from .json_tools import json_tools_router
from .ai_model_config import ai_model_config_router
from .prompt_template import prompt_template_router, prompt_template_public_router
from .ai_test_case_generation import ai_test_case_generation_router
from .database_query import database_query_router

v1_router = APIRouter()

v1_router.include_router(base_router, prefix="/base")
v1_router.include_router(users_router, prefix="/user", dependencies=[DependPermisson])
v1_router.include_router(roles_router, prefix="/role", dependencies=[DependPermisson])
v1_router.include_router(menus_router, prefix="/menu", dependencies=[DependPermisson])
v1_router.include_router(apis_router, prefix="/api", dependencies=[DependPermisson])
v1_router.include_router(depts_router, prefix="/dept", dependencies=[DependPermisson])
v1_router.include_router(auditlog_router, prefix="/auditlog", dependencies=[DependPermisson])
v1_router.include_router(project_router, prefix="/project", tags=["项目管理"], dependencies=[DependPermisson])
v1_router.include_router(project_public_router, prefix="/project", tags=["项目管理-公开"])
v1_router.include_router(api_request_router, prefix="/api_requests", tags=["接口请求"], dependencies=[DependPermisson])
v1_router.include_router(api_import_router, prefix="/api_import", tags=["接口导入"], dependencies=[DependPermisson])
v1_router.include_router(environment_router, prefix="/environment", tags=["环境配置"], dependencies=[DependPermisson])
v1_router.include_router(api_execution_history_router, prefix="/api_execution_history", tags=["API执行历史"], dependencies=[DependPermisson])
v1_router.include_router(api_test_case_router, prefix="/api_test_case", tags=["接口测试用例"], dependencies=[DependPermisson])
v1_router.include_router(test_case_router, prefix="/test_case", tags=["功能测试用例"], dependencies=[DependPermisson])
v1_router.include_router(api_test_plan_router, prefix="/api_test_plan", tags=["接口测试计划"], dependencies=[DependPermisson])
v1_router.include_router(functional_test_plan_router, prefix="/functional_test_plan", tags=["功能测试计划"], dependencies=[DependPermisson])
v1_router.include_router(scheduled_task_router, prefix="/scheduled_task", tags=["定时任务"], dependencies=[DependPermisson])
v1_router.include_router(faker_data_router, prefix="/faker_data", tags=["Faker数据生成"], dependencies=[DependPermisson])
v1_router.include_router(json_tools_router, prefix="/json_tools", tags=["JSON工具"], dependencies=[DependPermisson])
v1_router.include_router(ai_model_config_router, prefix="/ai_model_config", tags=["AI模型配置"], dependencies=[DependPermisson])
v1_router.include_router(prompt_template_router, prefix="/prompt_template", tags=["提示词模板"], dependencies=[DependPermisson])
v1_router.include_router(ai_test_case_generation_router, prefix="/ai_test_case_generation", tags=["AI测试用例生成"], dependencies=[DependPermisson])
v1_router.include_router(database_query_router, prefix="/database_query", tags=["数据库查询"], dependencies=[DependPermisson])
v1_router.include_router(prompt_template_public_router, prefix="/prompt_template", tags=["提示词模板-公开"])
v1_router.include_router(test_case_public_router, prefix="/test_case", tags=["功能测试用例-公开"])
v1_router.include_router(api_test_plan_public_router, prefix="/api_test_plan", tags=["接口测试计划-公开"])
v1_router.include_router(functional_test_plan_public_router, prefix="/functional_test_plan", tags=["功能测试计划-公开"])
v1_router.include_router(scheduled_task_public_router, prefix="/scheduled_task", tags=["定时任务-公开"])