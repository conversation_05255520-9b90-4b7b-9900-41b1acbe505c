import logging
from fastapi import APIRouter, Query
from typing import Optional, Dict, Any
from app.schemas.base import Success, Fail
from app.utils.faker_manager import faker_manager

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/data_types", summary="获取Faker支持的数据类型")
async def get_faker_data_types():
    """获取Faker支持的所有数据类型"""
    try:
        data_types = faker_manager.get_data_types()
        return Success(data=data_types, msg="获取数据类型成功")
    except Exception as e:
        logger.error(f"获取Faker数据类型失败: {str(e)}")
        return Fail(msg=f"获取数据类型失败: {str(e)}")


@router.get("/sample_data", summary="获取Faker示例数据")
async def get_faker_sample_data():
    """获取所有类型的Faker示例数据"""
    try:
        sample_data = faker_manager.generate_sample_data()
        return Success(data=sample_data, msg="获取示例数据成功")
    except Exception as e:
        logger.error(f"获取Faker示例数据失败: {str(e)}")
        return Fail(msg=f"获取示例数据失败: {str(e)}")


@router.post("/generate", summary="生成指定类型的随机数据")
async def generate_faker_data(
    data_type: str = Query(..., description="数据类型"),
    category: Optional[str] = Query(None, description="数据分类"),
    count: int = Query(1, description="生成数量", ge=1, le=100)
):
    """生成指定类型的随机数据"""
    try:
        results = []
        for _ in range(count):
            data = faker_manager.generate_data(data_type, category)
            if data is not None:
                results.append(str(data))
            else:
                results.append("N/A")
        
        return Success(data={
            "type": data_type,
            "category": category,
            "count": count,
            "results": results
        }, msg="生成数据成功")
    except Exception as e:
        logger.error(f"生成Faker数据失败: {str(e)}")
        return Fail(msg=f"生成数据失败: {str(e)}")


@router.post("/replace_variables", summary="替换文本中的Faker变量")
async def replace_faker_variables(
    text: str = Query(..., description="包含Faker变量的文本")
):
    """替换文本中的Faker变量"""
    try:
        result = faker_manager.replace_faker_variables(text)
        return Success(data={
            "original": text,
            "replaced": result
        }, msg="替换变量成功")
    except Exception as e:
        logger.error(f"替换Faker变量失败: {str(e)}")
        return Fail(msg=f"替换变量失败: {str(e)}")
