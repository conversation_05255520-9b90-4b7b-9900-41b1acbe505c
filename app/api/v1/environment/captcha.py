import logging
import httpx
from fastapi import APIRouter, Query, HTTPException
from jsonpath_ng import parse
from app.schemas.base import Success, Fail
from app.core.dependency import DependAuth
from app.services.captcha_service import captcha_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/test", summary="测试验证码地址")
async def test_captcha_url(
    captcha_url: str = Query(..., description="验证码获取URL"),
    captcha_method: str = Query(default="GET", description="验证码获取请求方式"),
    captcha_headers: str = Query(default="{}", description="验证码获取请求头(JSON字符串)"),
    captcha_body: str = Query(default="{}", description="验证码获取请求体(JSON字符串)"),
    captcha_image_path: str = Query(default="content.imageBase64", description="验证码图片字段路径(JSONPath)"),
    captcha_key_path: str = Query(default="content.codeKey", description="验证码Key字段路径(JSONPath)"),
    current_user=DependAuth,
):
    """
    测试验证码地址是否可用，支持自定义JSON路径和HTTP方法
    """
    try:
        # 解析请求头和请求体
        try:
            headers = eval(captcha_headers) if captcha_headers and captcha_headers != "{}" else {}
            body = eval(captcha_body) if captcha_body and captcha_body != "{}" else {}
        except Exception as e:
            return Fail(msg=f"解析请求头或请求体失败: {str(e)}")

        async with httpx.AsyncClient(timeout=10.0) as client:
            # 根据请求方法发送请求
            if captcha_method.upper() == "GET":
                response = await client.get(captcha_url, headers=headers)
            elif captcha_method.upper() == "POST":
                if headers.get("Content-Type") == "application/json" or not headers.get("Content-Type"):
                    response = await client.post(captcha_url, json=body, headers=headers)
                else:
                    response = await client.post(captcha_url, data=body, headers=headers)
            elif captcha_method.upper() == "PUT":
                response = await client.put(captcha_url, json=body, headers=headers)
            elif captcha_method.upper() == "DELETE":
                response = await client.delete(captcha_url, headers=headers)
            else:
                return Fail(msg=f"不支持的请求方法: {captcha_method}")

            logger.info(f"验证码请求: {captcha_method} {captcha_url}, 状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    logger.info(f"验证码API返回数据: {data}")

                    # 使用JSONPath解析数据
                    image_base64 = None
                    code_key = None

                    # 解析验证码图片
                    try:
                        image_parser = parse(captcha_image_path)
                        image_matches = image_parser.find(data)
                        if image_matches:
                            image_base64 = image_matches[0].value
                    except Exception as e:
                        logger.error(f"解析验证码图片路径失败: {e}")
                        return Fail(msg=f"解析验证码图片路径失败: {captcha_image_path}")

                    # 解析验证码Key
                    try:
                        key_parser = parse(captcha_key_path)
                        key_matches = key_parser.find(data)
                        if key_matches:
                            code_key = key_matches[0].value
                    except Exception as e:
                        logger.error(f"解析验证码Key路径失败: {e}")
                        return Fail(msg=f"解析验证码Key路径失败: {captcha_key_path}")

                    # 检查是否成功获取到数据
                    if image_base64 and code_key:
                        # 尝试识别验证码
                        recognized_code = None
                        recognition_message = ""

                        try:
                            recognized_code = await captcha_service.recognize_captcha(image_base64, max_retries=3)
                            if recognized_code:
                                recognition_message = f"验证码识别成功: {recognized_code}"
                                logger.info(recognition_message)
                            else:
                                recognition_message = "验证码识别失败"
                                logger.warning(recognition_message)
                        except Exception as e:
                            recognition_message = f"验证码识别出错: {str(e)}"
                            logger.error(recognition_message)

                        return Success(
                            data={
                                "imageBase64": image_base64,
                                "codeKey": code_key,
                                "recognizedCode": recognized_code,
                                "recognitionMessage": recognition_message,
                                "status": "success",
                                "message": "验证码获取成功",
                                "raw_data": data  # 返回原始数据用于调试
                            },
                            msg="验证码地址测试成功"
                        )
                    else:
                        missing_fields = []
                        if not image_base64:
                            missing_fields.append(f"验证码图片(路径: {captcha_image_path})")
                        if not code_key:
                            missing_fields.append(f"验证码Key(路径: {captcha_key_path})")
                        return Fail(msg=f"无法获取到: {', '.join(missing_fields)}")

                except Exception as e:
                    logger.error(f"解析返回数据失败: {e}")
                    return Fail(msg=f"解析返回数据失败: {str(e)}")
            else:
                return Fail(msg=f"请求失败，状态码: {response.status_code}")

    except httpx.TimeoutException:
        return Fail(msg="请求超时，请检查验证码地址是否正确")
    except httpx.RequestError as e:
        return Fail(msg=f"请求错误: {str(e)}")
    except Exception as e:
        logger.error(f"测试验证码地址失败: {str(e)}")
        return Fail(msg=f"测试失败: {str(e)}")
