import logging
from fastapi import APIRouter, Body, Query
from tortoise.expressions import Q
from app.controllers.environment import environment_controller
from app.schemas.base import Fail, Success, SuccessExtra
from app.schemas.environment import EnvironmentCreate, EnvironmentUpdate, EnvironmentCopy

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/list", summary="获取环境配置列表")
async def list_environments(
        page: int = Query(1, description="页码"),
        page_size: int = Query(10, description="每页数量"),
        project_id: int = Query(None, description="项目ID"),
        name: str = Query("", description="环境名称，用于搜索"),
        env_type: str = Query("", description="环境类型"),
):
    if project_id:
        total, env_objs = await environment_controller.get_environments_by_project(
            project_id=project_id, page=page, page_size=page_size, name=name, env_type=env_type
        )
    else:
        q = Q()
        if name:
            q &= Q(name__contains=name)
        if env_type:
            q &= Q(env_type=env_type)
        total, env_objs = await environment_controller.list(page=page, page_size=page_size, search=q)
    
    data = [await obj.to_dict() for obj in env_objs]
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="获取环境配置详情")
async def get_environment(
        environment_id: int = Query(..., description="环境配置ID"),
):
    env_obj = await environment_controller.get(id=environment_id)
    env_dict = await env_obj.to_dict()
    return Success(data=env_dict)


@router.post("/create", summary="创建环境配置")
async def create_environment(
        env_in: EnvironmentCreate,
):
    # 检查环境名称是否已存在
    if await environment_controller.check_name_exists(env_in.name, env_in.project_id):
        return Fail(msg="该项目中已存在同名环境配置")
    
    await environment_controller.create(obj_in=env_in)
    return Success(msg="环境配置创建成功")


@router.post("/update", summary="更新环境配置")
async def update_environment(
        env_in: EnvironmentUpdate = Body(...),
):
    # 检查环境名称是否已存在（排除当前记录）
    if await environment_controller.check_name_exists(env_in.name, env_in.project_id, env_in.id):
        return Fail(msg="该项目中已存在同名环境配置")
    
    await environment_controller.update(id=env_in.id, obj_in=env_in)
    return Success(msg="环境配置更新成功")


@router.delete("/delete", summary="删除环境配置")
async def delete_environment(
        id: int = Query(..., description="环境配置ID"),
):
    await environment_controller.remove(id=id)
    return Success(msg="环境配置删除成功")


@router.post("/copy", summary="复制环境配置")
async def copy_environment(
        copy_data: EnvironmentCopy = Body(...),
):
    # 检查新环境名称是否已存在
    source_env = await environment_controller.get(id=copy_data.id)
    target_project_id = copy_data.project_id or source_env.project_id
    if await environment_controller.check_name_exists(copy_data.name, target_project_id):
        return Fail(msg="目标项目中已存在同名环境配置")
    
    await environment_controller.copy_environment(copy_data)
    return Success(msg="环境配置复制成功")
