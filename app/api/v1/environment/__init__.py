from fastapi import APIRouter

from .environment import router
from .token import router as token_router
from .debug_token import router as debug_router
from .captcha import router as captcha_router

environment_router = APIRouter()
environment_router.include_router(router, tags=["环境配置"])
environment_router.include_router(token_router, prefix="/token", tags=["Token管理"])
environment_router.include_router(debug_router, prefix="/debug", tags=["Token调试"])
environment_router.include_router(captcha_router, prefix="/captcha", tags=["验证码管理"])

__all__ = ["environment_router"]
