import logging
from fastapi import APIRouter, Query
from app.schemas.base import Fail, Success
from app.services.token_service import token_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/refresh", summary="手动刷新环境Token")
async def refresh_environment_token(
    environment_id: int = Query(..., description="环境ID"),
):
    """手动刷新指定环境的Token"""
    try:
        token = await token_service.fetch_token(environment_id)
        
        if token:
            return Success(msg="Token刷新成功", data={"token": token})
        else:
            return Fail(msg="Token刷新失败，请检查配置")
            
    except Exception as e:
        logger.error(f"手动刷新Token失败: {str(e)}")
        return Fail(msg="Token刷新失败")


@router.post("/test", summary="测试Token获取配置")
async def test_token_config(
    environment_id: int = Query(..., description="环境ID"),
):
    """测试Token获取配置是否正确"""
    try:
        token = await token_service.fetch_token(environment_id)
        
        if token:
            return Success(msg="Token获取测试成功", data={"token": token[:20] + "..." if len(token) > 20 else token})
        else:
            return Fail(msg="Token获取测试失败，请检查配置")
            
    except Exception as e:
        logger.error(f"测试Token配置失败: {str(e)}")
        return Fail(msg="Token配置测试失败")


@router.get("/refresh_status", summary="获取Token刷新状态")
async def get_token_refresh_status():
    """获取所有环境的Token刷新状态"""
    try:
        need_refresh = await token_service.get_environments_need_refresh()
        
        return Success(data={
            "need_refresh_count": len(need_refresh),
            "need_refresh_environments": need_refresh
        })
        
    except Exception as e:
        logger.error(f"获取Token刷新状态失败: {str(e)}")
        return Fail(msg="获取Token刷新状态失败")
