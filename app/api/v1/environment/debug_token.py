import logging
from fastapi import APIRouter, Body, Query
from app.schemas.base import Success, Fail
from app.services.token_service import token_service
from app.controllers.environment import environment_controller

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/debug_extract", summary="调试Token提取功能")
async def debug_token_extract(
    data: dict = Body(..., description="测试数据")
):
    """调试Token提取功能"""
    try:
        response_data = data.get("response_data", {})
        field_name = data.get("field_name", "token")
        field_path = data.get("field_path", "")
        
        logger.info(f"调试Token提取 - response_data: {response_data}")
        logger.info(f"调试Token提取 - field_name: {field_name}")
        logger.info(f"调试Token提取 - field_path: {field_path}")
        
        # 调用Token提取方法
        token = token_service._extract_token(response_data, field_name, field_path)
        
        return Success(data={
            "extracted_token": token,
            "success": token is not None,
            "input_data": {
                "response_data": response_data,
                "field_name": field_name,
                "field_path": field_path
            }
        })
        
    except Exception as e:
        logger.error(f"调试Token提取失败: {str(e)}")
        return Fail(msg=f"调试失败: {str(e)}")


@router.post("/test_nfoc", summary="测试NFOC系统Token提取")
async def test_nfoc_token():
    """测试NFOC系统的Token提取"""
    try:
        # NFOC系统的模拟响应数据
        nfoc_response = {
            'g5ReqId': None, 
            'status': 'OK', 
            'code': '200', 
            'msg': '执行成功', 
            'content': {
                'userId': '1', 
                'username': 'Admin', 
                'employeeNo': None, 
                'nickname': '管理员', 
                'token': '4977c934-775a-4af3-8d06-b050d3f2355c', 
                'tempCode': None, 
                'setRole': False, 
                'roles': None, 
                'hadDuty': None
            }
        }
        
        # 测试不同的提取方式
        results = {}
        
        # 1. 使用JSONPath
        token1 = token_service._extract_token(nfoc_response, "token", "$.content.token")
        results["jsonpath_extract"] = token1
        
        # 2. 使用字段名
        token2 = token_service._extract_token(nfoc_response, "token", "")
        results["field_name_extract"] = token2
        
        # 3. 直接访问验证
        expected_token = nfoc_response['content']['token']
        results["expected_token"] = expected_token
        
        return Success(data={
            "results": results,
            "all_match": all(token == expected_token for token in results.values() if token),
            "nfoc_response": nfoc_response
        })
        
    except Exception as e:
        logger.error(f"测试NFOC Token提取失败: {str(e)}")
        return Fail(msg=f"测试失败: {str(e)}")


@router.get("/check_env_config", summary="检查环境配置")
async def check_env_config(
    environment_id: int = Query(..., description="环境ID")
):
    """检查指定环境的Token配置"""
    try:
        # 获取环境配置
        env = await environment_controller.get(id=environment_id)
        env_dict = await env.to_dict()

        return Success(data={
            "environment_config": {
                "id": env.id,
                "name": env.name,
                "token_url": env.token_url,
                "token_method": env.token_method,
                "token_headers": env.token_headers,
                "token_body": env.token_body,
                "token_field_name": env.token_field_name,
                "token_field_path": env.token_field_path,
                "auto_refresh_token": env.auto_refresh_token,
                "token_refresh_interval": env.token_refresh_interval,
                "last_token_refresh": env.last_token_refresh
            },
            "field_types": {
                "token_field_name": type(env.token_field_name).__name__,
                "token_field_path": type(env.token_field_path).__name__,
                "token_headers": type(env.token_headers).__name__,
                "token_body": type(env.token_body).__name__
            }
        })

    except Exception as e:
        logger.error(f"检查环境配置失败: {str(e)}")
        return Fail(msg=f"检查失败: {str(e)}")
