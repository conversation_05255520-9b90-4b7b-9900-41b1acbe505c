import logging
from fastapi import APIRouter, Body
from app.controllers.api_execution_history import api_execution_history_controller
from app.schemas.base import Success, Fail
from app.schemas.api_execution_history import ApiExecutionHistoryCreate
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/recent", summary="获取最近执行历史", dependencies=[DependAuth])
async def get_recent_execution_history():
    """获取当前用户最近20条执行历史记录"""
    try:
        user_id = CTX_USER_ID.get()
        
        # 获取最近的执行历史
        recent_history = await api_execution_history_controller.get_user_recent_history(user_id, limit=20)
        
        # 转换为字典格式
        data = [await record.to_dict() for record in recent_history]
        
        return Success(data=data, msg="获取执行历史成功")
    except Exception as e:
        logger.error(f"获取执行历史失败: {str(e)}")
        return Fail(msg=f"获取失败: {str(e)}")


@router.post("/create", summary="创建执行记录", dependencies=[DependAuth])
async def create_execution_record(
        record_data: dict = Body(..., description="执行记录数据")
):
    """创建新的执行记录"""
    try:
        user_id = CTX_USER_ID.get()
        
        # 添加用户ID到记录数据中
        record_data['user_id'] = user_id
        
        # 创建执行记录
        create_data = ApiExecutionHistoryCreate(**record_data)
        record = await api_execution_history_controller.create_execution_record(create_data)
        
        return Success(data=await record.to_dict(), msg="创建执行记录成功")
    except Exception as e:
        logger.error(f"创建执行记录失败: {str(e)}")
        return Fail(msg=f"创建失败: {str(e)}")
