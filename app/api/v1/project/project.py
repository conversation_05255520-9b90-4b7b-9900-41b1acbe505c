import logging
from fastapi import APIRouter, Body, Query
from tortoise.expressions import Q
from app.controllers.project import project_controller
from app.schemas.base import Fail, Success, SuccessExtra
from app.schemas.project import ProjectCreate, ProjectUpdate, ModuleCreate, ModuleUpdate

logger = logging.getLogger(__name__)
router = APIRouter()
public_router = APIRouter()  # 公开路由，不需要认证


@router.get("/list", summary="获取项目列表")
async def list_projects(
        page: int = Query(1, description="页码"),
        page_size: int = Query(10, description="每页数量"),
        name: str = Query("", description="项目名称，用于搜索"),
        status: str = Query("", description="项目状态"),
        manager: str = Query("", description="项目经理"),
):
    q = Q()
    if name:
        q &= Q(name__contains=name)
        # print(name)
    if status:
        q &= Q(status=status)
    if manager:
        q &= Q(manager__contains=manager)
    total, project_objs = await project_controller.list(page=page, page_size=page_size, search=q)
    data = [await obj.to_dict(m2m=True) for obj in project_objs]
    # for x in data:
    #     print("=============")
    #     print(x)
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="获取项目详情")
async def get_project(
        project_id: int = Query(..., description="项目ID"),
):
    project_obj = await project_controller.get(id=project_id)
    project_dict = await project_obj.to_dict()
    return Success(data=project_dict)


@router.post("/create", summary="创建项目")
async def create_project(
        project_in: ProjectCreate,
):
    await project_controller.create(obj_in=project_in)
    return Success(msg="项目创建成功")


@router.post("/update", summary="更新项目")
async def update_project(
        project_in: ProjectUpdate = Body(...),
):
    await project_controller.update(id=project_in.id, obj_in=project_in)
    return Success(msg="项目更新成功")


@router.delete("/delete", summary="删除项目")
async def delete_project(
        id: int = Query(..., description="项目ID"),
):
    await project_controller.remove(id=id)
    return Success(msg="项目删除成功")


@public_router.get("/project_list", summary="获取项目列表（不分页）")
async def get_project_list(
        name: str = Query("", description="项目名称，用于搜索"),
):
    projects = await project_controller.get_project_list(name=name)
    return Success(data=projects)


@public_router.get("/module_tree", summary="获取项目模块树")
async def get_project_module_tree(
        project_id: int = Query(..., description="项目ID"),
        name: str = Query("", description="模块名称，用于搜索"),
):
    modules = await project_controller.get_module_tree(project_id=project_id, name=name)
    return Success(data=modules)


@router.post("/module/create", summary="创建模块")
async def create_project_module(
        module_in: ModuleCreate,
):
    await project_controller.create_module(obj_in=module_in)
    return Success(msg="模块创建成功")


@router.post("/module/update", summary="更新模块")
async def update_project_module(
        module_in: ModuleUpdate = Body(...),
):
    await project_controller.update_module(module_id=module_in.id, obj_in=module_in)
    return Success(msg="模块更新成功")


@router.delete("/module/delete", summary="删除模块")
async def delete_project_module(
        module_id: int = Query(..., description="模块ID"),
):
    await project_controller.delete_module(module_id=module_id)
    return Success(msg="模块删除成功")


@router.get("/module/check_delete", summary="检查模块是否可以删除")
async def check_can_delete_module(
        module_id: int = Query(..., description="模块ID"),
):
    result = await project_controller.check_can_delete_module(module_id=module_id)
    return Success(data=result)
