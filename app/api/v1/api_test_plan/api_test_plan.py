import logging
from typing import Optional
from fastapi import APIRouter, Body, Query
from tortoise.expressions import Q
from app.controllers.api_test_plan import api_test_plan_controller
from app.schemas.base import Fail, Success, SuccessExtra
from app.schemas.api_test_plan import (
    ApiTestPlanCreate,
    ApiTestPlanUpdate,
    ApiTestPlanCopy,
    AddTestCasesToPlan,
    RemoveTestCasesFromPlan,
    ExecuteTestPlan,
    UpdateCaseOrder
)
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth

logger = logging.getLogger(__name__)
router = APIRouter()
public_router = APIRouter()  # 公开路由，不需要认证


@router.get("/list", summary="获取接口测试计划列表")
async def get_api_test_plan_list(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    plan_name: str = Query("", description="计划名称"),
    status: str = Query("", description="状态"),
    level: str = Query("", description="等级"),
    project_id: Optional[str] = Query(None, description="项目ID"),
):
    # 构建查询条件
    q = Q()
    if plan_name:
        q &= Q(plan_name__icontains=plan_name)
    if status:
        q &= Q(status=status)
    if level:
        q &= Q(level=level)

    # 处理project_id参数，将空字符串转换为None
    actual_project_id = None
    if project_id and project_id.strip() and project_id != "":
        try:
            actual_project_id = int(project_id)
        except ValueError:
            actual_project_id = None

    if actual_project_id:
        total, data = await api_test_plan_controller.list_by_project_with_user_info(
            project_id=actual_project_id, page=page, page_size=page_size, search=q
        )
    else:
        total, data = await api_test_plan_controller.list_with_user_info(
            page=page, page_size=page_size, search=q
        )

    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="获取接口测试计划详情")
async def get_api_test_plan(
        plan_id: int = Query(..., description="测试计划ID"),
):
    plan_obj = await api_test_plan_controller.get(id=plan_id)
    plan_dict = await plan_obj.to_dict()
    
    # 获取创建人信息
    try:
        from app.models.admin import User
        user = await User.get(id=plan_obj.user_id)
        plan_dict['creator_name'] = user.alias or user.username
    except:
        plan_dict['creator_name'] = '未知用户'
    
    # 获取环境信息
    if plan_obj.environment_id:
        try:
            from app.models.admin import Environment
            environment = await Environment.get(id=plan_obj.environment_id)
            plan_dict['environment_name'] = environment.name  # 使用环境名称
        except:
            plan_dict['environment_name'] = '环境已删除'
    else:
        plan_dict['environment_name'] = None
    
    return Success(data=plan_dict)


@router.post("/create", summary="创建接口测试计划", dependencies=[DependAuth])
async def create_api_test_plan(
        plan_in: ApiTestPlanCreate,
):
    user_id = CTX_USER_ID.get()
    await api_test_plan_controller.create_with_user(obj_in=plan_in, user_id=user_id)
    return Success(msg="接口测试计划创建成功")


@router.post("/update", summary="更新接口测试计划", dependencies=[DependAuth])
async def update_api_test_plan(
        plan_in: ApiTestPlanUpdate = Body(...),
):
    await api_test_plan_controller.update(id=plan_in.id, obj_in=plan_in)
    return Success(msg="接口测试计划更新成功")


@router.delete("/delete", summary="删除接口测试计划", dependencies=[DependAuth])
async def delete_api_test_plan(
        id: int = Query(..., description="测试计划ID"),
):
    await api_test_plan_controller.remove(id=id)
    return Success(msg="接口测试计划删除成功")


@router.post("/copy", summary="复制接口测试计划", dependencies=[DependAuth])
async def copy_api_test_plan(
        copy_data: ApiTestPlanCopy = Body(...),
):
    user_id = CTX_USER_ID.get()
    await api_test_plan_controller.copy_plan(plan_id=copy_data.id, user_id=user_id)
    return Success(msg="接口测试计划复制成功")


@router.get("/cases", summary="获取测试计划关联的测试用例")
async def get_test_plan_cases(
        plan_id: int = Query(..., description="测试计划ID"),
):
    cases = await api_test_plan_controller.get_plan_cases(plan_id=plan_id)
    return Success(data=cases)


@router.post("/add_cases", summary="添加测试用例到测试计划", dependencies=[DependAuth])
async def add_test_cases_to_plan(
        add_data: AddTestCasesToPlan = Body(...),
):
    await api_test_plan_controller.add_cases_to_plan(
        plan_id=add_data.plan_id, 
        case_ids=add_data.case_ids
    )
    return Success(msg="测试用例添加成功")


@router.post("/remove_cases", summary="从测试计划中移除测试用例", dependencies=[DependAuth])
async def remove_test_cases_from_plan(
        remove_data: RemoveTestCasesFromPlan = Body(...),
):
    await api_test_plan_controller.remove_cases_from_plan(
        plan_id=remove_data.plan_id, 
        case_ids=remove_data.case_ids
    )
    return Success(msg="测试用例移除成功")


@router.get("/approved_cases", summary="获取项目下已审核的接口测试用例")
async def get_approved_test_cases(
        project_id: int = Query(..., description="项目ID"),
):
    cases = await api_test_plan_controller.get_approved_cases_by_project(project_id=project_id)
    return Success(data=cases)


@router.post("/update_case_order", summary="更新测试用例执行顺序", dependencies=[DependAuth])
async def update_case_order(
        order_data: UpdateCaseOrder = Body(...),
):
    await api_test_plan_controller.update_case_order(
        plan_id=order_data.plan_id,
        case_orders=order_data.case_orders
    )
    return Success(msg="用例顺序更新成功")


@router.post("/execute", summary="执行接口测试计划", dependencies=[DependAuth])
async def execute_api_test_plan(
        execute_data: ExecuteTestPlan = Body(...),
):
    try:
        result = await api_test_plan_controller.execute_test_plan(
            plan_id=execute_data.plan_id,
            environment_id=execute_data.environment_id
        )
        return Success(data=result, msg="测试计划执行成功")
    except Exception as e:
        logger.error(f"执行测试计划失败: {str(e)}")
        return Fail(msg=f"执行失败: {str(e)}")


@public_router.get("/statistics", summary="获取接口测试计划统计数据")
async def get_api_test_plan_statistics():
    """获取接口测试计划统计数据，按项目分组"""
    try:
        statistics = await api_test_plan_controller.get_statistics()
        return Success(data=statistics)
    except Exception as e:
        logger.error(f"获取统计数据失败: {str(e)}")
        return Fail(msg=f"获取统计数据失败: {str(e)}")


@router.post("/batch_execute", summary="批量执行接口测试计划（pytest）", dependencies=[DependAuth])
async def batch_execute_api_test_plan(
        execute_data: ExecuteTestPlan = Body(...),
):
    try:
        result = await api_test_plan_controller.batch_execute_test_plan(
            plan_id=execute_data.plan_id,
            environment_id=execute_data.environment_id
        )
        return Success(data=result, msg="测试计划批量执行完成")
    except Exception as e:
        logger.error(f"批量执行测试计划失败: {str(e)}")
        return Fail(msg=f"批量执行失败: {str(e)}")


@router.post("/update_case_status", summary="更新测试用例执行状态", dependencies=[DependAuth])
async def update_case_execution_status(
        plan_id: int = Body(..., description="测试计划ID"),
        case_id: int = Body(..., description="测试用例ID"),
        execution_status: str = Body(..., description="执行状态")
):
    try:
        success = await api_test_plan_controller.update_case_execution_status(
            plan_id=plan_id,
            case_id=case_id,
            execution_status=execution_status
        )
        if success:
            return Success(msg="执行状态更新成功")
        else:
            return Fail(msg="执行状态更新失败")
    except Exception as e:
        logger.error(f"更新执行状态失败: {str(e)}")
        return Fail(msg=f"更新失败: {str(e)}")
