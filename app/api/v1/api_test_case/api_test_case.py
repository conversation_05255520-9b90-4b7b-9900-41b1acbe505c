import logging
from fastapi import APIR<PERSON>er, Body, Query
from tortoise.expressions import Q
from app.controllers.api_test_case import api_test_case_controller
from app.schemas.base import Fail, Success, SuccessExtra
from app.schemas.api_test_case import ApiTestCaseCreate, ApiTestCaseUpdate, ApiTestCaseCopy
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/list", summary="测试用例列表")
async def list_api_test_case(
        page: int = Query(1, description="页码"),
        page_size: int = Query(10, description="每页数量"),
        case_name: str = Query(None, description="用例名称，用于搜索"),
        method: str = Query(None, description="请求方法"),
        project_id: int = Query(None, description="项目ID"),
        module_id: int = Query(None, description="模块ID"),
        status: str = Query(None, description="状态"),
        is_smoke: bool = Query(None, description="是否冒烟用例"),
        source: str = Query(None, description="来源")
):
    q = Q()
    if case_name:
        q &= Q(case_name__contains=case_name)
    if method:
        q &= Q(method=method)
    if project_id:
        q &= Q(project_id=project_id)
    if module_id:
        q &= Q(module_id=module_id)
    if status:
        q &= Q(status=status)
    if is_smoke is not None:
        q &= Q(is_smoke=is_smoke)
    if source:
        q &= Q(source=source)

    if project_id:
        total, data = await api_test_case_controller.list_by_project_with_user_info(
            project_id=project_id, page=page, page_size=page_size, search=q
        )
    else:
        total, data = await api_test_case_controller.list_with_user_info(
            page=page, page_size=page_size, search=q
        )

    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="获取测试用例详情")
async def get_api_test_case(
        test_case_id: int = Query(..., description="测试用例ID"),
):
    test_case_obj = await api_test_case_controller.get(id=test_case_id)
    test_case_dict = await test_case_obj.to_dict()
    return Success(data=test_case_dict)


@router.post("/create", summary="创建测试用例", dependencies=[DependAuth])
async def create_api_test_case(
        test_case_in: ApiTestCaseCreate,
):
    user_id = CTX_USER_ID.get()
    await api_test_case_controller.create_with_case_number(obj_in=test_case_in, user_id=user_id)
    return Success(msg="测试用例创建成功")


@router.post("/update", summary="更新测试用例", dependencies=[DependAuth])
async def update_api_test_case(
        test_case_in: ApiTestCaseUpdate = Body(...),
):
    await api_test_case_controller.update(id=test_case_in.id, obj_in=test_case_in)
    return Success(msg="测试用例更新成功")


@router.delete("/delete", summary="删除测试用例", dependencies=[DependAuth])
async def delete_api_test_case(
        id: int = Query(..., description="测试用例ID"),
):
    await api_test_case_controller.remove(id=id)
    return Success(msg="测试用例删除成功")


@router.post("/copy", summary="复制测试用例", dependencies=[DependAuth])
async def copy_api_test_case(
        copy_data: ApiTestCaseCopy = Body(...),
):
    user_id = CTX_USER_ID.get()
    await api_test_case_controller.copy_test_case(copy_data=copy_data, user_id=user_id)
    return Success(msg="测试用例复制成功")


@router.post("/execute", summary="执行测试用例", dependencies=[DependAuth])
async def execute_api_test_case(
        request_data: dict = Body(..., description="执行请求数据")
):
    try:
        test_case_id = request_data.get("test_case_id")
        environment_id = request_data.get("environment_id")

        if not test_case_id:
            return Fail(msg="缺少test_case_id参数")
        if not environment_id:
            return Fail(msg="缺少environment_id参数")

        # 执行测试用例
        result = await api_test_case_controller.execute_test_case(test_case_id, environment_id)

        return Success(data=result, msg="测试用例执行成功")
    except Exception as e:
        logger.error(f"执行测试用例失败: {str(e)}")
        return Fail(msg=f"执行失败: {str(e)}")
