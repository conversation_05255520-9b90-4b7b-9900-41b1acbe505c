from fastapi import APIRouter, HTTPException
from app.schemas.json_tools import (
    JsonFormatRequest, JsonFormatResponse,
    JsonValidateRequest, JsonValidateResponse,
    JsonConvertRequest, JsonConvertResponse,
    JsonPathRequest, JsonPathResponse,
    JsonSchemaValidateRequest, JsonSchemaValidateResponse,
    JsonCompareRequest, JsonCompareResponse,
    JsonStatsResponse
)
from app.utils.json_tools_manager import json_tools_manager
from app.schemas.base import Success, Fail
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/format", summary="格式化JSON", response_model=JsonFormatResponse)
async def format_json(request: JsonFormatRequest):
    """格式化JSON数据"""
    try:
        result = json_tools_manager.format_json(
            request.json_data,
            request.indent,
            request.sort_keys
        )
        return Success(data=result, msg="JSON格式化成功")
    except ValueError as e:
        logger.error(f"JSON格式化失败: {str(e)}")
        return Fail(msg=str(e))
    except Exception as e:
        logger.error(f"JSON格式化异常: {str(e)}")
        return Fail(msg="JSON格式化失败")


@router.post("/compress", summary="压缩JSON", response_model=JsonFormatResponse)
async def compress_json(request: JsonFormatRequest):
    """压缩JSON数据"""
    try:
        result = json_tools_manager.compress_json(request.json_data)
        return Success(data=result, msg="JSON压缩成功")
    except ValueError as e:
        logger.error(f"JSON压缩失败: {str(e)}")
        return Fail(msg=str(e))
    except Exception as e:
        logger.error(f"JSON压缩异常: {str(e)}")
        return Fail(msg="JSON压缩失败")


@router.post("/validate", summary="验证JSON", response_model=JsonValidateResponse)
async def validate_json(request: JsonValidateRequest):
    """验证JSON格式"""
    try:
        result = json_tools_manager.validate_json(request.json_data)
        return Success(data=result, msg="JSON验证完成")
    except Exception as e:
        logger.error(f"JSON验证异常: {str(e)}")
        return Fail(msg="JSON验证失败")


@router.post("/convert", summary="格式转换", response_model=JsonConvertResponse)
async def convert_format(request: JsonConvertRequest):
    """格式转换"""
    try:
        converted_data = json_tools_manager.convert_format(
            request.data,
            request.source_format,
            request.target_format,
            request.csv_delimiter
        )
        
        result = {
            "converted_data": converted_data,
            "source_format": request.source_format,
            "target_format": request.target_format
        }
        
        return Success(data=result, msg="格式转换成功")
    except ValueError as e:
        logger.error(f"格式转换失败: {str(e)}")
        return Fail(msg=str(e))
    except Exception as e:
        logger.error(f"格式转换异常: {str(e)}")
        return Fail(msg="格式转换失败")


@router.post("/jsonpath", summary="JSONPath查询", response_model=JsonPathResponse)
async def query_jsonpath(request: JsonPathRequest):
    """JSONPath查询"""
    try:
        result = json_tools_manager.query_jsonpath(
            request.json_data,
            request.json_path
        )
        return Success(data=result, msg="JSONPath查询成功")
    except ValueError as e:
        logger.error(f"JSONPath查询失败: {str(e)}")
        return Fail(msg=str(e))
    except Exception as e:
        logger.error(f"JSONPath查询异常: {str(e)}")
        return Fail(msg="JSONPath查询失败")


@router.post("/schema-validate", summary="JSON Schema验证", response_model=JsonSchemaValidateResponse)
async def validate_schema(request: JsonSchemaValidateRequest):
    """JSON Schema验证"""
    try:
        result = json_tools_manager.validate_schema(
            request.json_data,
            request.json_schema
        )
        return Success(data=result, msg="Schema验证完成")
    except ValueError as e:
        logger.error(f"Schema验证失败: {str(e)}")
        return Fail(msg=str(e))
    except Exception as e:
        logger.error(f"Schema验证异常: {str(e)}")
        return Fail(msg="Schema验证失败")


@router.post("/compare", summary="JSON对比", response_model=JsonCompareResponse)
async def compare_json(request: JsonCompareRequest):
    """JSON对比"""
    try:
        result = json_tools_manager.compare_json(
            request.json1,
            request.json2
        )
        return Success(data=result, msg="JSON对比完成")
    except ValueError as e:
        logger.error(f"JSON对比失败: {str(e)}")
        return Fail(msg=str(e))
    except Exception as e:
        logger.error(f"JSON对比异常: {str(e)}")
        return Fail(msg="JSON对比失败")


@router.post("/stats", summary="JSON统计", response_model=JsonStatsResponse)
async def get_json_stats(request: JsonValidateRequest):
    """获取JSON统计信息"""
    try:
        result = json_tools_manager.get_json_stats(request.json_data)
        return Success(data=result, msg="JSON统计完成")
    except ValueError as e:
        logger.error(f"JSON统计失败: {str(e)}")
        return Fail(msg=str(e))
    except Exception as e:
        logger.error(f"JSON统计异常: {str(e)}")
        return Fail(msg="JSON统计失败")


@router.get("/examples", summary="获取示例数据")
async def get_examples():
    """获取各种格式的示例数据"""
    examples = {
        "json": {
            "name": "JSON示例",
            "data": """{
  "name": "张三",
  "age": 30,
  "city": "北京",
  "hobbies": ["读书", "游泳", "编程"],
  "address": {
    "street": "中关村大街",
    "number": 123
  }
}"""
        },
        "xml": {
            "name": "XML示例",
            "data": """<?xml version="1.0" encoding="UTF-8"?>
<person>
    <name>张三</name>
    <age>30</age>
    <city>北京</city>
    <hobbies>
        <item_0>读书</item_0>
        <item_1>游泳</item_1>
        <item_2>编程</item_2>
    </hobbies>
    <address>
        <street>中关村大街</street>
        <number>123</number>
    </address>
</person>"""
        },
        "yaml": {
            "name": "YAML示例",
            "data": """name: 张三
age: 30
city: 北京
hobbies:
- 读书
- 游泳
- 编程
address:
  street: 中关村大街
  number: 123"""
        },
        "csv": {
            "name": "CSV示例",
            "data": """name,age,city
张三,30,北京
李四,25,上海
王五,35,广州"""
        },
        "jsonpath": {
            "name": "JSONPath示例",
            "expressions": [
                "$.name",
                "$.hobbies[*]",
                "$.address.street",
                "$..number"
            ]
        },
        "schema": {
            "name": "JSON Schema示例",
            "data": """{
  "type": "object",
  "properties": {
    "name": {
      "type": "string"
    },
    "age": {
      "type": "integer",
      "minimum": 0
    },
    "city": {
      "type": "string"
    },
    "hobbies": {
      "type": "array",
      "items": {
        "type": "string"
      }
    }
  },
  "required": ["name", "age"]
}"""
        }
    }
    
    return Success(data=examples, msg="获取示例数据成功")
