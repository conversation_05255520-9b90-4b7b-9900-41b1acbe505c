from fastapi import APIRouter, Query, Depends
from tortoise.expressions import Q
from app.controllers.ai_test_case_generation import ai_test_case_generation_controller
from app.schemas import Success, SuccessExtra
from app.schemas.ai_test_case_generation import (
    AITestCaseGenerationRequest,
    AITestCaseGenerationResult,
    SaveGeneratedTestCases,
    TestCasePreview
)
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth

router = APIRouter()


@router.post("/generate", summary="生成功能测试用例", dependencies=[DependAuth])
async def generate_test_cases(
    request: AITestCaseGenerationRequest,
):
    """使用AI生成功能测试用例"""
    user_id = CTX_USER_ID.get()
    result = await ai_test_case_generation_controller.generate_test_cases(request, user_id)
    return Success(data=result.model_dump())


@router.post("/save", summary="保存生成的测试用例", dependencies=[DependAuth])
async def save_generated_test_cases(
    save_request: SaveGeneratedTestCases,
):
    """保存生成的测试用例到数据库"""
    user_id = CTX_USER_ID.get()
    saved_count = await ai_test_case_generation_controller.save_generated_test_cases(save_request, user_id)
    return Success(data={"saved_count": saved_count}, msg=f"成功保存 {saved_count} 个测试用例")


@router.get("/history", summary="查看生成历史", dependencies=[DependAuth])
async def get_generation_history(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    project_id: int = Query(None, description="项目ID"),
    user_id: int = Query(None, description="用户ID"),
):
    """获取AI测试用例生成历史"""
    current_user_id = CTX_USER_ID.get()
    
    # 如果不是管理员，只能查看自己的记录
    if user_id and user_id != current_user_id:
        # 这里可以添加权限检查逻辑
        user_id = current_user_id
    
    total, data = await ai_test_case_generation_controller.get_generation_history(
        user_id=user_id or current_user_id,
        project_id=project_id,
        page=page,
        page_size=page_size
    )

    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/task/{task_id}", summary="查看生成任务详情", dependencies=[DependAuth])
async def get_generation_task(
    task_id: int,
):
    """获取生成任务详情"""
    data = await ai_test_case_generation_controller.get_task_detail(task_id)
    return Success(data=data)


@router.delete("/task/{task_id}", summary="删除生成任务", dependencies=[DependAuth])
async def delete_generation_task(
    task_id: int,
):
    """删除生成任务"""
    await ai_test_case_generation_controller.remove(id=task_id)
    return Success(msg="删除成功")
