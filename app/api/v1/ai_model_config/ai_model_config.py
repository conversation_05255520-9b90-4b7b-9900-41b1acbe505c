from fastapi import APIRouter, Query, Depends
from tortoise.expressions import Q
from app.controllers.ai_model_config import ai_model_config_controller
from app.schemas import Success, SuccessExtra
from app.schemas.ai_model_config import (
    AIModelConfigCreate, 
    AIModelConfigUpdate, 
    AIModelConfigResponse,
    AIModelTestRequest,
    AIModelTestResponse
)
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth
from app.models.enums import AIModelType, AIModelStatus

router = APIRouter()


@router.get("/list", summary="查看AI模型配置列表")
async def list_ai_model_config(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    name: str = Query(None, description="模型名称"),
    model_type: AIModelType = Query(None, description="模型类型"),
    status: AIModelStatus = Query(None, description="状态"),
):
    q = Q()
    if name:
        q &= Q(name__contains=name)
    if model_type:
        q &= Q(model_type=model_type)
    if status:
        q &= Q(status=status)
    
    total, config_objs = await ai_model_config_controller.list(
        page=page, 
        page_size=page_size, 
        search=q, 
        order=["-created_at"]
    )
    
    data = []
    for obj in config_objs:
        obj_dict = await obj.to_dict()
        # 隐藏API密钥的敏感信息
        if obj_dict.get("api_key"):
            obj_dict["api_key"] = obj_dict["api_key"][:8] + "****"
        data.append(obj_dict)
    
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="查看AI模型配置详情")
async def get_ai_model_config(
    id: int = Query(..., description="配置ID"),
):
    config_obj = await ai_model_config_controller.get(id=id)
    data = await config_obj.to_dict()
    # 隐藏API密钥的敏感信息
    if data.get("api_key"):
        data["api_key"] = data["api_key"][:8] + "****"
    return Success(data=data)


@router.post("/create", summary="创建AI模型配置", dependencies=[DependAuth])
async def create_ai_model_config(
    config_in: AIModelConfigCreate,
):
    user_id = CTX_USER_ID.get()
    await ai_model_config_controller.create_ai_model_config(obj_in=config_in, user_id=user_id)
    return Success(msg="创建成功")


@router.post("/update", summary="更新AI模型配置", dependencies=[DependAuth])
async def update_ai_model_config(
    config_in: AIModelConfigUpdate,
):
    await ai_model_config_controller.update_ai_model_config(id=config_in.id, obj_in=config_in)
    return Success(msg="更新成功")


@router.delete("/delete", summary="删除AI模型配置", dependencies=[DependAuth])
async def delete_ai_model_config(
    config_id: int = Query(..., description="配置ID"),
):
    await ai_model_config_controller.remove(id=config_id)
    return Success(msg="删除成功")


@router.get("/types", summary="获取AI模型类型列表")
async def get_ai_model_types():
    """获取所有可用的AI模型类型"""
    types = [
        {"value": "openai", "label": "OpenAI"},
        {"value": "openrouter", "label": "OpenRouter"},
        {"value": "volcengine", "label": "火山引擎"},
        {"value": "claude", "label": "Claude"},
        {"value": "gemini", "label": "Gemini"},
        {"value": "qwen", "label": "通义千问"},
        {"value": "baidu", "label": "百度文心"},
        {"value": "zhipu", "label": "智谱AI"},
        {"value": "deepseek", "label": "DeepSeek"},
        {"value": "moonshot", "label": "月之暗面"},
        {"value": "custom", "label": "自定义"},
    ]
    return Success(data=types)


@router.get("/status_options", summary="获取状态选项")
async def get_status_options():
    """获取所有可用的状态选项"""
    options = [
        {"value": "active", "label": "启用"},
        {"value": "inactive", "label": "禁用"},
        {"value": "testing", "label": "测试中"},
    ]
    return Success(data=options)


@router.post("/test", summary="测试AI模型连接", dependencies=[DependAuth])
async def test_ai_model_config(
    test_request: AIModelTestRequest,
):
    """测试AI模型配置是否可用"""
    result = await ai_model_config_controller.test_model_connection(
        config_id=test_request.config_id,
        test_message=test_request.test_message
    )
    return Success(data=result)


@router.get("/default", summary="获取默认AI模型配置")
async def get_default_ai_model_config():
    """获取默认的AI模型配置"""
    config = await ai_model_config_controller.get_default_config()
    if not config:
        return Success(data=None, msg="未设置默认模型")
    
    data = await config.to_dict()
    # 隐藏API密钥的敏感信息
    if data.get("api_key"):
        data["api_key"] = data["api_key"][:8] + "****"
    
    return Success(data=data)


@router.post("/set_default", summary="设置默认AI模型", dependencies=[DependAuth])
async def set_default_ai_model_config(
    config_id: int = Query(..., description="配置ID"),
):
    """设置指定配置为默认模型"""
    # 先取消所有默认设置
    await ai_model_config_controller.model.filter(is_default=True).update(is_default=False)
    
    # 设置新的默认模型
    config = await ai_model_config_controller.get(id=config_id)
    config.is_default = True
    await config.save()
    
    return Success(msg="设置默认模型成功")


@router.get("/by_type", summary="根据类型获取AI模型配置")
async def get_ai_model_configs_by_type(
    model_type: AIModelType = Query(..., description="模型类型"),
):
    """根据模型类型获取配置列表"""
    configs = await ai_model_config_controller.get_configs_by_type(model_type)
    data = []
    for config in configs:
        config_dict = await config.to_dict()
        # 隐藏API密钥的敏感信息
        if config_dict.get("api_key"):
            config_dict["api_key"] = config_dict["api_key"][:8] + "****"
        data.append(config_dict)
    
    return Success(data=data)
