import json
import logging
import httpx
import re
from datetime import datetime
from fastapi import APIR<PERSON>er, Body, Query
from tortoise.expressions import Q
from app.controllers.api_request import api_request_controller
from app.controllers.api_test_case import api_test_case_controller
from app.schemas.base import Fail, Success, SuccessExtra
from app.schemas.api_request import ApiRequestCreate, ApiRequestUpdate
from app.schemas.api_test_case import ApiTestCaseCreate
from app.models.admin import ApiRequest
from app.api.v1.project.project import get_project
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/list", summary="接口列表")
async def list_api_request(
        page: int = Query(1, description="页码"),
        page_size: int = Query(10, description="每页数量"),
        api_name: str = Query(None, description="接口名称，用于搜索"),
        method: str = Query(None, description="请求方法"),
        project_id: str = Query(None, description="项目ID"),
        module_id: str = Query(None, description="模块ID"),
        category: str = Query(None, description="分类")
):
    q = Q()
    print(q)
    if api_name:
        q &= Q(api_name__contains=api_name)  # 修改这里，使用双下划线
    if method:
        q &= Q(method=method)
    if project_id:
        q &= Q(project_id=project_id)
    if module_id:
        q &= Q(module_id=module_id)
    if category:
        q &= Q(category__contains=category)  # 修改这里，使用双下划线
    total, api_request_objs = await api_request_controller.list(page=page, page_size=page_size, search=q)
    data = [await obj.to_dict(m2m=True) for obj in api_request_objs]

    # 处理返回的数据，确保params和headers是字符串格式
    for x in data:
        print("=" * 70)
        # 处理headers字段
        if x.get("headers") is not None:
            if isinstance(x["headers"], dict):
                x["headers"] = json.dumps(x["headers"])
            elif not isinstance(x["headers"], str):
                x["headers"] = str(x["headers"])

        # 处理params字段
        if x.get("params") is not None:
            if isinstance(x["params"], dict):
                x["params"] = json.dumps(x["params"])
            elif not isinstance(x["params"], str):
                x["params"] = str(x["params"])

    # 这里展示了直接调用admin里面的对应的方法就可以获取到数据
    # print("="*70)
    # if project_id:
    #     a = await get_project(project_id)
    #     response_data = json.loads(a.body)  # 解析字节类型的 body 为 JSON
    #     print(response_data)
    #     project_name = response_data.get("data").get("name")  # 获取 name 值
    #     print(f"项目名称: {project_name}")
    #     print("="*70)
    #     print(vars(a))  # 使用 vars() 查看对象的所有属性
    #     # 或者直接打印整个对象的字典表示
    # print("="*70)

    # print(data)
    # for x in data:
    #     print("="*70)
    #     print(x.get("api_name"))
    # print("="*70)
    # 直接调用 admin 里面的对应的方法就可以获取到数据
    # aa = await ApiRequest.filter(q).all()
    # for x in aa:
    #     print(await x.to_dict())

    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


# @router.get("/get", summary="获取项目详情")
# async def get_project(
#     project_id: int = Query(..., description="项目ID"),
# ):
#     project_obj = await project_controller.get(id=project_id)
#     project_dict = await project_obj.to_dict()
#     return Success(data=project_dict)

def clean_url(url: str) -> str:
    """清理URL，去掉协议、域名和端口，只保留路径部分"""
    if not url:
        return url

    # 使用正则表达式匹配并提取路径部分
    # 匹配 http://domain:port/path 或 https://domain:port/path 格式
    pattern = r'^https?://[^/]+(.*)$'
    match = re.match(pattern, url)

    if match:
        path = match.group(1)
        return path if path else '/'

    # 如果不是完整URL格式，直接返回原URL
    return url


def convert_params_for_test_case(params_str: str) -> str:
    """将接口管理的参数格式转换为接口测试用例的格式"""
    if not params_str or params_str == '[]':
        return None

    try:
        # 如果已经是字符串格式的JSON对象，直接返回
        if isinstance(params_str, str):
            # 尝试解析为JSON
            params_obj = json.loads(params_str)

            # 如果是字典格式，直接返回
            if isinstance(params_obj, dict):
                return params_str

            # 如果是列表格式，转换为字典
            if isinstance(params_obj, list):
                params_dict = {}
                for item in params_obj:
                    if isinstance(item, dict) and item.get('checked', True) and item.get('key'):
                        params_dict[item['key']] = item['value']
                return json.dumps(params_dict) if params_dict else None

        return None
    except Exception as e:
        logger.error(f"转换参数格式失败: {str(e)}")
        return None


@router.post("/create", summary="保存接口请求", dependencies=[DependAuth])
async def create_api_request(
        api_request_in: ApiRequestCreate,
):
    user_id = CTX_USER_ID.get()

    # 处理params参数，将列表格式转换为键值对格式
    if api_request_in.params and api_request_in.params != '[]':
        try:
            params_list = json.loads(api_request_in.params)
            params_dict = {}
            for item in params_list:
                if item.get('checked', True) and item.get('key'):
                    params_dict[item['key']] = item['value']
            api_request_in.params = json.dumps(params_dict)
        except Exception as e:
            logger.error(f"处理params参数失败: {str(e)}")
    else:
        api_request_in.params = None

    # 处理headers参数，将列表格式转换为键值对格式
    if api_request_in.headers and api_request_in.headers != '[]':
        try:
            headers_list = json.loads(api_request_in.headers)
            headers_dict = {}
            for item in headers_list:
                if item.get('checked', True) and item.get('key'):
                    headers_dict[item['key']] = item['value']
            api_request_in.headers = json.dumps(headers_dict)
        except Exception as e:
            logger.error(f"处理headers参数失败: {str(e)}")
    else:
        api_request_in.headers = None

    # 处理body参数
    if not api_request_in.body or api_request_in.body == '[]':
        api_request_in.body = None

    # 设置当前用户ID
    if not api_request_in.user_id:
        api_request_in.user_id = CTX_USER_ID.get()

    # print(api_request_in.body)
    logger.info(f"处理后的API请求数据: {api_request_in}")

    # 保存接口请求
    api_request = await api_request_controller.create(obj_in=api_request_in)

    # 自动创建接口测试用例
    try:
        # 清理URL，去掉协议、域名和端口
        cleaned_url = clean_url(api_request_in.url)

        # 转换参数格式
        test_case_params = convert_params_for_test_case(api_request_in.params)

        # 构建测试用例数据
        test_case_data = ApiTestCaseCreate(
            case_name=api_request_in.api_name,
            method=api_request_in.method,
            url=cleaned_url,
            params=test_case_params,
            body=api_request_in.body,
            project_id=api_request_in.project_id,
            module_id=api_request_in.module_id,
            status="pending",
            source="manual"  # 标记为人工创建
        )

        # 创建测试用例
        await api_test_case_controller.create_with_case_number(
            obj_in=test_case_data,
            user_id=user_id
        )

        logger.info(f"自动创建接口测试用例成功: {api_request_in.api_name}")

    except Exception as e:
        logger.error(f"自动创建接口测试用例失败: {str(e)}")
        # 不影响接口保存的成功，只记录错误

    return Success(msg="接口保存成功，已自动创建接口测试用例")


@router.post("/update", summary="更新接口", dependencies=[DependAuth])
async def update_api_request(
        api_request_in: ApiRequestUpdate = Body(...),
):
    # 处理params参数，将列表格式转换为键值对格式
    if api_request_in.params and api_request_in.params != '[]':
        try:
            params_list = json.loads(api_request_in.params)
            params_dict = {}
            for item in params_list:
                if item.get('checked', True) and item.get('key'):
                    params_dict[item['key']] = item['value']
            api_request_in.params = json.dumps(params_dict)
        except Exception as e:
            logger.error(f"处理params参数失败: {str(e)}")
    else:
        api_request_in.params = None

    # 处理headers参数，将列表格式转换为键值对格式
    if api_request_in.headers and api_request_in.headers != '[]':
        try:
            headers_list = json.loads(api_request_in.headers)
            headers_dict = {}
            for item in headers_list:
                if item.get('checked', True) and item.get('key'):
                    headers_dict[item['key']] = item['value']
            api_request_in.headers = json.dumps(headers_dict)
        except Exception as e:
            logger.error(f"处理headers参数失败: {str(e)}")
    else:
        api_request_in.headers = None

    # 处理body参数
    if not api_request_in.body or api_request_in.body == '[]':
        api_request_in.body = None

    logger.info(f"处理后的API请求数据: {api_request_in}")

    await api_request_controller.update(id=api_request_in.id, obj_in=api_request_in)

    # 返回更新成功的消息，同时包含项目ID，前端可以用这个ID来重新筛选
    return Success(msg="接口更新成功", data={"project_id": api_request_in.project_id})


@router.delete("/delete", summary="删除接口")
async def delete_api_request(
        id: int = Query(..., description="接口ID"),
):
    await api_request_controller.remove(id=id)
    return Success(msg="项目删除成功")


@router.post("/execute", summary="执行API请求")
async def execute_api_request(
        request_data: dict = Body(..., description="执行请求数据")
):
    try:
        api_request_id = request_data.get("api_request_id")
        if not api_request_id:
            return Fail(msg="缺少api_request_id参数")

        # 执行API请求
        result = await api_request_controller.execute_api_request(api_request_id)

        return Success(data=result, msg="API执行成功")
    except Exception as e:
        logger.error(f"执行API请求失败: {str(e)}")
        return Fail(msg=f"执行失败: {str(e)}")


@router.get("/recent", summary="获取最近调用的接口记录", dependencies=[DependAuth])
async def get_recent_api_requests():
    """获取当前用户最近20条调用记录，按最后执行时间倒序排列"""
    try:
        user_id = CTX_USER_ID.get()

        # 查询当前用户最近执行的接口记录
        recent_requests = await ApiRequest.filter(
            user_id=user_id,
            last_executed__not_isnull=True  # 只获取已执行过的记录
        ).order_by('-last_executed').limit(20)

        # 转换为字典格式
        data = []
        for request in recent_requests:
            request_dict = await request.to_dict()
            # 确保params和headers是正确的格式
            if request_dict.get("params") and isinstance(request_dict["params"], dict):
                request_dict["params"] = json.dumps(request_dict["params"])
            if request_dict.get("headers") and isinstance(request_dict["headers"], dict):
                request_dict["headers"] = json.dumps(request_dict["headers"])
            data.append(request_dict)

        return Success(data=data, msg="获取最近调用记录成功")
    except Exception as e:
        logger.error(f"获取最近调用记录失败: {str(e)}")
        return Fail(msg=f"获取失败: {str(e)}")


@router.post("/proxy", summary="代理外部API请求", dependencies=[DependAuth])
async def proxy_external_api(
        request_data: dict = Body(..., description="代理请求数据")
):
    """代理外部API请求，解决前端跨域问题"""
    try:
        # 提取请求参数
        method = request_data.get("method", "GET").upper()
        url = request_data.get("url")
        params = request_data.get("params", {})
        headers = request_data.get("headers", {})
        body = request_data.get("body")

        if not url:
            return Fail(msg="缺少URL参数")

        # 设置默认请求头
        default_headers = {
            'User-Agent': 'Mozilla/5.0 (compatible; API-Request-Tool/1.0)',
            'Accept': 'application/json, text/plain, */*'
        }

        # 合并用户自定义请求头
        final_headers = {**default_headers, **headers}

        # 处理请求体
        data = None
        if body and method in ['POST', 'PUT', 'PATCH']:
            try:
                data = json.loads(body) if isinstance(body, str) else body
            except json.JSONDecodeError:
                data = body

        # 记录开始时间
        start_time = datetime.now()

        # 发送HTTP请求
        async with httpx.AsyncClient(timeout=30.0, follow_redirects=True) as client:
            response = await client.request(
                method=method,
                url=url,
                params=params,
                headers=final_headers,
                json=data if isinstance(data, dict) else None,
                content=data if isinstance(data, str) else None
            )

        # 计算响应时间
        end_time = datetime.now()
        response_time = int((end_time - start_time).total_seconds() * 1000)

        # 处理响应数据
        try:
            response_data = response.json()
        except:
            response_data = response.text

        # 构建响应结果
        result = {
            "status": response.status_code,
            "statusText": response.reason_phrase or "",
            "headers": dict(response.headers),
            "data": response_data,
            "responseTime": response_time
        }

        logger.info(f"代理请求成功: {method} {url}, 状态码: {response.status_code}")
        return Success(data=result, msg="代理请求成功")

    except httpx.RequestError as e:
        logger.error(f"代理请求错误: {str(e)}")
        return Fail(msg=f"网络请求失败: {str(e)}")
    except Exception as e:
        logger.error(f"代理请求失败: {str(e)}")
        return Fail(msg=f"代理请求失败: {str(e)}")


@router.post("/get_captcha", summary="获取并识别验证码", dependencies=[DependAuth])
async def get_captcha_for_request(
        request_data: dict = Body(..., description="验证码获取配置")
):
    """获取并识别验证码，用于API请求"""
    try:
        # 提取验证码配置参数
        captcha_url = request_data.get("captcha_url")
        captcha_method = request_data.get("captcha_method", "GET")
        captcha_headers = request_data.get("captcha_headers", {})
        captcha_body = request_data.get("captcha_body", {})
        captcha_image_path = request_data.get("captcha_image_path", "content.imageBase64")
        captcha_key_path = request_data.get("captcha_key_path", "content.codeKey")
        # 新增：用户的请求体模板
        user_request_body = request_data.get("user_request_body", "")

        if not captcha_url:
            return Fail(msg="缺少验证码URL参数")

        # 获取并识别验证码
        captcha_result = await captcha_service.get_captcha_with_recognition(
            captcha_url=captcha_url,
            captcha_method=captcha_method,
            captcha_headers=captcha_headers,
            captcha_body=captcha_body,
            captcha_image_path=captcha_image_path,
            captcha_key_path=captcha_key_path,
            max_retries=3
        )

        if captcha_result:
            recognized_code, code_key = captcha_result

            # 如果提供了用户请求体模板，则进行占位符替换
            updated_request_body = ""
            if user_request_body:
                updated_request_body = _replace_captcha_placeholders_in_string(
                    user_request_body, recognized_code, code_key
                )

            return Success(
                data={
                    "code": recognized_code,
                    "codeKey": code_key,
                    "originalRequestBody": user_request_body,
                    "updatedRequestBody": updated_request_body,
                    "message": "验证码获取并识别成功"
                },
                msg="验证码获取成功"
            )
        else:
            return Fail(msg="验证码获取或识别失败，请检查配置")

    except Exception as e:
        logger.error(f"获取验证码失败: {str(e)}")
        return Fail(msg=f"获取验证码失败: {str(e)}")


def _replace_captcha_placeholders_in_string(request_body_str: str, recognized_code: str, code_key: str) -> str:
    """
    替换字符串中的验证码占位符

    Args:
        request_body_str: 原始请求体字符串
        recognized_code: 识别出的验证码
        code_key: 验证码Key

    Returns:
        替换后的请求体字符串
    """
    try:
        logger.info(f"原始请求体字符串: {request_body_str}")

        # 支持多种占位符格式
        placeholders = {
            '${code}': recognized_code,
            '${codekey}': code_key,
            '${codeKey}': code_key,  # 支持驼峰命名
            '${CODE}': recognized_code,  # 支持大写
            '${CODEKEY}': code_key,
            # 也支持不带$的格式
            '{code}': recognized_code,
            '{codekey}': code_key,
            '{codeKey}': code_key,
        }

        # 执行替换
        updated_str = request_body_str
        for placeholder, value in placeholders.items():
            if placeholder in updated_str:
                updated_str = updated_str.replace(placeholder, str(value))
                logger.info(f"替换占位符 {placeholder} -> {value}")

        logger.info(f"替换后的请求体字符串: {updated_str}")
        return updated_str

    except Exception as e:
        logger.error(f"替换验证码占位符时发生错误: {str(e)}")
        return request_body_str
