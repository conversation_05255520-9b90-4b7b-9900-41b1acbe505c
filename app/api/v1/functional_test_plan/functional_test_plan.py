import logging
from fastapi import APIRouter, Body, Query
from tortoise.expressions import Q
from app.controllers.functional_test_plan import functional_test_plan_controller
from app.schemas.base import Fail, Success, SuccessExtra
from app.schemas.functional_test_plan import (
    FunctionalTestPlanCreate,
    FunctionalTestPlanUpdate,
    FunctionalTestPlanCopy,
    AddTestCasesToPlan,
    RemoveTestCasesFromPlan,
    UpdateCaseOrder,
    UpdateCaseStatus
)
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth

logger = logging.getLogger(__name__)
router = APIRouter()
public_router = APIRouter()  # 公开路由，不需要认证


@router.get("/list", summary="获取功能测试计划列表")
async def get_functional_test_plan_list(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    plan_name: str = Query("", description="计划名称"),
    status: str = Query("", description="状态"),
    level: str = Query("", description="等级"),
    project_id: int = Query(None, description="项目ID"),
):
    # 构建查询条件
    q = Q()
    if plan_name:
        q &= Q(plan_name__icontains=plan_name)
    if status:
        q &= Q(status=status)
    if level:
        q &= Q(level=level)

    if project_id:
        total, data = await functional_test_plan_controller.list_by_project_with_user_info(
            project_id=project_id, page=page, page_size=page_size, search=q
        )
    else:
        total, data = await functional_test_plan_controller.list_with_user_info(
            page=page, page_size=page_size, search=q
        )

    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="获取功能测试计划详情")
async def get_functional_test_plan(
        plan_id: int = Query(..., description="测试计划ID"),
):
    plan_obj = await functional_test_plan_controller.get(id=plan_id)
    plan_dict = await plan_obj.to_dict()
    
    # 获取创建人信息
    try:
        from app.models.admin import User
        user = await User.get(id=plan_obj.user_id)
        plan_dict['creator_name'] = user.alias or user.username
    except:
        plan_dict['creator_name'] = '未知用户'
    
    return Success(data=plan_dict)


@router.post("/create", summary="创建功能测试计划", dependencies=[DependAuth])
async def create_functional_test_plan(
        plan_in: FunctionalTestPlanCreate,
):
    user_id = CTX_USER_ID.get()
    await functional_test_plan_controller.create_with_user(obj_in=plan_in, user_id=user_id)
    return Success(msg="功能测试计划创建成功")


@router.post("/update", summary="更新功能测试计划", dependencies=[DependAuth])
async def update_functional_test_plan(
        plan_in: FunctionalTestPlanUpdate = Body(...),
):
    await functional_test_plan_controller.update(id=plan_in.id, obj_in=plan_in)
    return Success(msg="功能测试计划更新成功")


@router.delete("/delete", summary="删除功能测试计划", dependencies=[DependAuth])
async def delete_functional_test_plan(
        id: int = Query(..., description="测试计划ID"),
):
    await functional_test_plan_controller.remove(id=id)
    return Success(msg="功能测试计划删除成功")


@router.post("/copy", summary="复制功能测试计划", dependencies=[DependAuth])
async def copy_functional_test_plan(
        copy_data: FunctionalTestPlanCopy = Body(...),
):
    user_id = CTX_USER_ID.get()
    await functional_test_plan_controller.copy_plan(plan_id=copy_data.id, user_id=user_id)
    return Success(msg="功能测试计划复制成功")


@router.get("/cases", summary="获取测试计划关联的测试用例")
async def get_test_plan_cases(
        plan_id: int = Query(..., description="测试计划ID"),
):
    cases = await functional_test_plan_controller.get_plan_cases(plan_id=plan_id)
    return Success(data=cases)


@router.post("/add_cases", summary="添加测试用例到测试计划", dependencies=[DependAuth])
async def add_test_cases_to_plan(
        add_data: AddTestCasesToPlan = Body(...),
):
    await functional_test_plan_controller.add_cases_to_plan(
        plan_id=add_data.plan_id,
        case_ids=add_data.case_ids
    )
    return Success(msg="测试用例添加成功")


@router.post("/remove_cases", summary="从测试计划中移除测试用例", dependencies=[DependAuth])
async def remove_test_cases_from_plan(
        remove_data: RemoveTestCasesFromPlan = Body(...),
):
    await functional_test_plan_controller.remove_cases_from_plan(
        plan_id=remove_data.plan_id,
        case_ids=remove_data.case_ids
    )
    return Success(msg="测试用例移除成功")


@router.get("/approved_cases", summary="获取项目下已审核的功能测试用例")
async def get_approved_test_cases(
        project_id: int = Query(..., description="项目ID"),
        plan_id: int = Query(None, description="测试计划ID，用于过滤已添加的用例"),
        case_name: str = Query(None, description="用例名称，用于搜索"),
):
    if case_name:
        cases = await functional_test_plan_controller.search_approved_test_cases(
            project_id=project_id,
            plan_id=plan_id,
            case_name=case_name
        )
    else:
        cases = await functional_test_plan_controller.get_approved_test_cases(
            project_id=project_id,
            plan_id=plan_id
        )
    return Success(data=cases)


@router.post("/update_case_order", summary="更新测试用例执行顺序", dependencies=[DependAuth])
async def update_case_order(
        order_data: UpdateCaseOrder = Body(...),
):
    await functional_test_plan_controller.update_case_order(
        plan_id=order_data.plan_id,
        case_orders=[order.dict() for order in order_data.case_orders]
    )
    return Success(msg="执行顺序更新成功")


@router.post("/update_case_status", summary="更新测试用例执行状态", dependencies=[DependAuth])
async def update_case_status(
        status_data: UpdateCaseStatus = Body(...),
):
    await functional_test_plan_controller.update_case_status(
        plan_id=status_data.plan_id,
        case_id=status_data.case_id,
        execution_status=status_data.execution_status
    )
    return Success(msg="执行状态更新成功")


@public_router.get("/statistics", summary="获取功能测试计划统计数据")
async def get_functional_test_plan_statistics():
    """获取功能测试计划统计数据，按项目分组"""
    try:
        statistics = await functional_test_plan_controller.get_statistics()
        return Success(data=statistics)
    except Exception as e:
        logger.error(f"获取统计数据失败: {str(e)}")
        return Fail(msg=f"获取统计数据失败: {str(e)}")
