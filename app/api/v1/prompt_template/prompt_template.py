from fastapi import APIRouter, Query, Depends
from tortoise.expressions import Q
from app.controllers.prompt_template import prompt_template_controller
from app.schemas import Success, SuccessExtra
from app.schemas.prompt_template import (
    PromptTemplateCreate,
    PromptTemplateUpdate,
    PromptTemplateResponse,
    PromptTemplateCopy,
    PromptTemplateTest,
    PromptTemplateTestResponse
)
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth

router = APIRouter()
public_router = APIRouter()  # 公开路由，不需要认证


@router.get("/list", summary="查看提示词模板列表")
async def list_prompt_template(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    name: str = Query(None, description="模板名称"),
    category: str = Query(None, description="模板分类"),
    is_active: bool = Query(None, description="是否启用"),
):
    q = Q()
    if name:
        q &= Q(name__contains=name)
    if category:
        q &= Q(category=category)
    if is_active is not None:
        q &= Q(is_active=is_active)
    
    total, template_objs = await prompt_template_controller.list(
        page=page,
        page_size=page_size,
        search=q,
        order=["-created_at"]
    )
    
    data = []
    for obj in template_objs:
        obj_dict = await obj.to_dict()
        data.append(obj_dict)
    
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="查看提示词模板详情")
async def get_prompt_template(
    id: int = Query(..., description="模板ID"),
):
    template_obj = await prompt_template_controller.get(id=id)
    data = await template_obj.to_dict()
    return Success(data=data)


@router.post("/create", summary="创建提示词模板", dependencies=[DependAuth])
async def create_prompt_template(
    template_in: PromptTemplateCreate,
):
    user_id = CTX_USER_ID.get()
    await prompt_template_controller.create_prompt_template(obj_in=template_in, user_id=user_id)
    return Success(msg="创建成功")


@router.post("/update", summary="更新提示词模板", dependencies=[DependAuth])
async def update_prompt_template(
    template_in: PromptTemplateUpdate,
):
    await prompt_template_controller.update_prompt_template(id=template_in.id, obj_in=template_in)
    return Success(msg="更新成功")


@router.delete("/delete", summary="删除提示词模板", dependencies=[DependAuth])
async def delete_prompt_template(
    id: int = Query(..., description="模板ID"),
):
    await prompt_template_controller.remove(id=id)
    return Success(msg="删除成功")


@router.post("/copy", summary="复制提示词模板", dependencies=[DependAuth])
async def copy_prompt_template(
    copy_request: PromptTemplateCopy,
):
    user_id = CTX_USER_ID.get()
    await prompt_template_controller.copy_template(
        template_id=copy_request.id,
        new_name=copy_request.name,
        user_id=user_id
    )
    return Success(msg="复制成功")


@router.post("/test", summary="测试提示词模板", dependencies=[DependAuth])
async def test_prompt_template(
    test_request: PromptTemplateTest,
):
    result = await prompt_template_controller.test_template(test_request)
    return Success(data=result)


@public_router.get("/categories", summary="获取提示词模板分类列表")
async def get_prompt_template_categories():
    categories = await prompt_template_controller.get_categories()
    return Success(data=categories)


@public_router.get("/by_category", summary="根据分类获取提示词模板")
async def get_templates_by_category(
    category: str = Query(..., description="模板分类"),
):
    templates = await prompt_template_controller.get_templates_by_category(category)
    data = []
    for template in templates:
        template_dict = await template.to_dict()
        data.append(template_dict)
    return Success(data=data)


@public_router.get("/default", summary="获取默认提示词模板")
async def get_default_template(
    category: str = Query(..., description="模板分类"),
):
    template = await prompt_template_controller.get_default_template(category)
    if template:
        data = await template.to_dict()
        return Success(data=data)
    else:
        return Success(data=None, msg="未找到默认模板")
