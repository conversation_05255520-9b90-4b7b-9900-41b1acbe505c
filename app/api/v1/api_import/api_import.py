import json
import logging
from fastapi import APIRouter, Body, Query, UploadFile, File, Form, Depends  # 新增Form导入
from fastapi.responses import JSONResponse
from tortoise.expressions import Q
from app.controllers.api_import import api_import_controller
from app.schemas.base import Fail, Success, SuccessExtra
from app.schemas.api_import import (
    ApiImportUpdate,
    ApiImportCreate,
    ApiImportAIGenerateRequest,
    SaveApiTestCasesRequest
)
from app.utils.swagger_converter import convert_swagger_to_interface_list
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth
from io import StringIO

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/list", summary="接口列表")
async def list_api_import(
        page: int = Query(1, description="页码"),
        page_size: int = Query(10, description="每页数量"),
        api_name: str = Query(None, description="接口名称，用于搜索"),
        url_path: str = Query(None, description="URL路径，用于搜索"),
        method: str = Query(None, description="请求方法"),
        project_id: str = Query(None, description="项目ID"), ):
    q = Q()
    print(q)
    if api_name:
        q &= Q(api_name__contains=api_name)  # 修改这里，使用双下划线
    if url_path:
        q &= Q(url_path__contains=url_path)  # 添加URL路径搜索
    if method:
        q &= Q(method=method)
    if project_id:
        q &= Q(project_id=project_id)
    total, api_request_objs = await api_import_controller.list(page=page, page_size=page_size, search=q)
    data = [await obj.to_dict(m2m=True) for obj in api_request_objs]

    # 处理返回的数据，确保params和headers是字符串格式
    for x in data:
        print("=" * 70)
        # 处理headers字段
        if x.get("headers") is not None:
            if isinstance(x["headers"], dict):
                x["headers"] = json.dumps(x["headers"])
            elif not isinstance(x["headers"], str):
                x["headers"] = str(x["headers"])

        # 处理params字段
        if x.get("params") is not None:
            if isinstance(x["params"], dict):
                x["params"] = json.dumps(x["params"])
            elif not isinstance(x["params"], str):
                x["params"] = str(x["params"])

    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.post("/update", summary="更新接口")
async def update_api_import(
        api_import_in: ApiImportUpdate = Body(...),
):
    await api_import_controller.update(id=api_import_in.id, obj_in=api_import_in)

    # 返回更新成功的消息，同时包含项目ID，前端可以用这个ID来重新筛选
    return Success(msg="接口更新成功", data={"project_id": api_import_in.project_id})


@router.delete("/delete", summary="删除接口")
async def delete_api_request(
        id: int = Query(..., description="接口ID"),
):
    await api_import_controller.remove(id=id)
    return Success(msg="接口删除成功")


@router.post("/batch_delete", summary="批量删除接口")
async def batch_delete_api_import(
        request_data: dict = Body(..., description="批量删除请求数据")
):
    try:
        ids = request_data.get("ids", [])
        if not ids:
            return Fail(msg="请选择要删除的接口")

        # 批量删除
        deleted_count = 0
        for api_id in ids:
            try:
                await api_import_controller.remove(id=api_id)
                deleted_count += 1
            except Exception as e:
                logger.error(f"删除接口 {api_id} 失败: {str(e)}")
                continue

        return Success(msg=f"成功删除 {deleted_count} 个接口")
    except Exception as e:
        logger.error(f"批量删除失败: {str(e)}")
        return Fail(msg=f"批量删除失败: {str(e)}")


@router.post("/upload", summary="解析接口")
async def upload_api_import(
        file_content: str = Body(..., embed=True, alias="fileContent"),
        project_id: int = Body(..., embed=True, alias="projectId"),
):
    try:
        logger.info(f"开始导入接口，项目ID: {project_id}")
        swagger_data = json.loads(file_content)
        swagger_data_list = convert_swagger_to_interface_list(swagger_data)
        logger.info(f"解析到 {len(swagger_data_list)} 个接口")

        # 使用/create路由的创建逻辑
        success_count = 0
        for i, api_data in enumerate(swagger_data_list):
            try:
                logger.info(f"正在处理第 {i+1} 个接口: {api_data.get('接口说明', 'Unknown')}")
                api_import_in = ApiImportCreate(
                    api_name=api_data["接口说明"],
                    url_path=api_data["接口路径"],
                    method=api_data["请求方式"],
                    params_list=json.dumps(api_data["请求参数"], ensure_ascii=False),
                    status="新增",
                    project_id=project_id,
                )
                await api_import_controller.create(obj_in=api_import_in)
                success_count += 1
            except Exception as create_error:
                logger.error(f"创建第 {i+1} 个接口失败: {str(create_error)}", exc_info=True)
                continue

        logger.info(f"导入完成，成功导入 {success_count} 个接口")
        return Success(data={"count": success_count}, msg=f"导入成功，共导入 {success_count} 个接口")
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {str(e)}")
        return Fail(msg="无效的JSON格式")
    except Exception as e:
        logger.error(f"导入失败: {str(e)}", exc_info=True)
        return Fail(msg=f"服务器错误: {str(e)}")


@router.post("/ai_generate", summary="AI生成接口测试用例", dependencies=[DependAuth])
async def ai_generate_test_cases(
    request: ApiImportAIGenerateRequest,
):
    """使用AI生成接口测试用例"""
    user_id = CTX_USER_ID.get()
    result = await api_import_controller.ai_generate_test_cases(request, user_id)
    return Success(data=result.model_dump())


@router.post("/save_test_cases", summary="保存生成的接口测试用例", dependencies=[DependAuth])
async def save_generated_test_cases(
    request: SaveApiTestCasesRequest,
):
    """保存生成的接口测试用例到数据库"""
    user_id = CTX_USER_ID.get()
    saved_count = await api_import_controller.save_generated_test_cases(request, user_id)
    return Success(data={"saved_count": saved_count}, msg=f"成功保存 {saved_count} 个测试用例")
