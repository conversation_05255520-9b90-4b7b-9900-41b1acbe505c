from fastapi import APIRouter, Query, Depends, HTTPException
from tortoise.queryset import Q

from app.controllers.database_query import database_query_controller
from app.schemas.database_query import (
    DatabaseConnectionCreate,
    DatabaseConnectionUpdate,
    DatabaseConnectionOut,
    QueryExecuteRequest,
    QueryResult,
    QueryHistoryOut,
    DatabaseSchema,
    TableSchema,
    ConnectionTestResult
)
from app.schemas.base import Success, SuccessExtra, Fail
from app.core.dependency import DependAuth
from app.core.ctx import CTX_USER_ID
from app.models.admin import DatabaseConnection, QueryHistory
import logging

logger = logging.getLogger(__name__)

router = APIRouter()



@router.get("/connections", summary="获取数据库连接列表", dependencies=[DependAuth])
async def list_database_connections(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    name: str = Query(None, description="连接名称"),
    db_type: str = Query(None, description="数据库类型"),
):
    """获取当前用户的数据库连接列表"""
    try:
        user_id = CTX_USER_ID.get()

        q = Q(user_id=user_id)
        if name:
            q &= Q(name__contains=name)
        if db_type:
            q &= Q(db_type=db_type)

        total, connections = await database_query_controller.list(
            page=page,
            page_size=page_size,
            search=q,
            order=["-created_at"]
        )
        
        data = []
        for conn in connections:
            conn_dict = await conn.to_dict()
            # 隐藏密码
            conn_dict["password"] = "****"
            data.append(conn_dict)
        
        return SuccessExtra(data=data, total=total, page=page, page_size=page_size)
    except Exception as e:
        logger.error(f"获取数据库连接列表失败: {str(e)}")
        return Fail(msg=f"获取失败: {str(e)}")


@router.post("/connections", summary="创建数据库连接", dependencies=[DependAuth])
async def create_database_connection(connection_in: DatabaseConnectionCreate):
    """创建数据库连接配置"""
    try:
        user_id = CTX_USER_ID.get()
        await database_query_controller.create_connection(connection_in, user_id)
        return Success(msg="创建成功")
    except Exception as e:
        logger.error(f"创建数据库连接失败: {str(e)}")
        return Fail(msg=f"创建失败: {str(e)}")


@router.put("/connections/{connection_id}", summary="更新数据库连接", dependencies=[DependAuth])
async def update_database_connection(
    connection_id: int,
    connection_in: DatabaseConnectionUpdate
):
    """更新数据库连接配置"""
    try:
        user_id = CTX_USER_ID.get()
        await database_query_controller.update_connection(connection_id, connection_in, user_id)
        return Success(msg="更新成功")
    except Exception as e:
        logger.error(f"更新数据库连接失败: {str(e)}")
        return Fail(msg=f"更新失败: {str(e)}")


@router.delete("/connections/{connection_id}", summary="删除数据库连接", dependencies=[DependAuth])
async def delete_database_connection(connection_id: int):
    """删除数据库连接配置"""
    try:
        user_id = CTX_USER_ID.get()
        await database_query_controller.delete_connection(connection_id, user_id)
        return Success(msg="删除成功")
    except Exception as e:
        logger.error(f"删除数据库连接失败: {str(e)}")
        return Fail(msg=f"删除失败: {str(e)}")


@router.get("/connections/{connection_id}", summary="获取数据库连接详情", dependencies=[DependAuth])
async def get_database_connection(connection_id: int):
    """获取数据库连接详情"""
    try:
        user_id = CTX_USER_ID.get()
        connection = await DatabaseConnection.get(id=connection_id, user_id=user_id)
        conn_dict = await connection.to_dict()
        # 隐藏密码
        conn_dict["password"] = "****"
        return Success(data=conn_dict)
    except Exception as e:
        logger.error(f"获取数据库连接详情失败: {str(e)}")
        return Fail(msg=f"获取失败: {str(e)}")


@router.post("/connections/{connection_id}/test", summary="测试数据库连接", dependencies=[DependAuth])
async def test_database_connection(connection_id: int):
    """测试数据库连接"""
    try:
        user_id = CTX_USER_ID.get()
        result = await database_query_controller.test_connection(connection_id, user_id)
        return Success(data=result.model_dump())
    except Exception as e:
        logger.error(f"测试数据库连接失败: {str(e)}")
        return Fail(msg=f"测试失败: {str(e)}")


@router.post("/execute", summary="执行SQL查询", dependencies=[DependAuth])
async def execute_query(request: QueryExecuteRequest):
    """执行SQL查询"""
    try:
        user_id = CTX_USER_ID.get()
        result = await database_query_controller.execute_query(request, user_id)
        return Success(data=result.model_dump())
    except Exception as e:
        logger.error(f"执行SQL查询失败: {str(e)}")
        return Fail(msg=f"执行失败: {str(e)}")


@router.get("/connections/{connection_id}/schema", summary="获取数据库表结构", dependencies=[DependAuth])
async def get_database_schema(connection_id: int):
    """获取数据库表结构"""
    try:
        user_id = CTX_USER_ID.get()
        schema = await database_query_controller.get_database_schema(connection_id, user_id)
        return Success(data=schema.model_dump())
    except Exception as e:
        logger.error(f"获取数据库表结构失败: {str(e)}")
        return Fail(msg=f"获取失败: {str(e)}")


@router.get("/connections/{connection_id}/tables/{table_name}/schema", summary="获取表详细结构", dependencies=[DependAuth])
async def get_table_schema(connection_id: int, table_name: str):
    """获取表的详细结构"""
    try:
        user_id = CTX_USER_ID.get()
        schema = await database_query_controller.get_table_schema(connection_id, table_name, user_id)
        return Success(data=schema.model_dump())
    except Exception as e:
        logger.error(f"获取表结构失败: {str(e)}")
        return Fail(msg=f"获取失败: {str(e)}")


@router.get("/history", summary="获取查询历史", dependencies=[DependAuth])
async def get_query_history(
    page: int = Query(1, description="页码"),
    page_size: int = Query(20, description="每页数量"),
    connection_id: int = Query(None, description="数据库连接ID"),
    status: str = Query(None, description="执行状态"),
    is_favorite: bool = Query(None, description="是否收藏"),
):
    """获取查询历史"""
    try:
        user_id = CTX_USER_ID.get()
        
        q = Q(user_id=user_id)
        if connection_id:
            q &= Q(database_connection_id=connection_id)
        if status:
            q &= Q(status=status)
        if is_favorite is not None:
            q &= Q(is_favorite=is_favorite)
        
        total = await QueryHistory.filter(q).count()
        history_list = await QueryHistory.filter(q).order_by("-created_at").limit(page_size).offset((page - 1) * page_size)
        
        data = []
        for history in history_list:
            history_dict = await history.to_dict()
            # 获取数据库连接名称
            try:
                connection = await DatabaseConnection.get(id=history.database_connection_id)
                history_dict["database_connection_name"] = connection.name
            except:
                history_dict["database_connection_name"] = "未知连接"
            data.append(history_dict)
        
        return SuccessExtra(data=data, total=total, page=page, page_size=page_size)
    except Exception as e:
        logger.error(f"获取查询历史失败: {str(e)}")
        return Fail(msg=f"获取失败: {str(e)}")


@router.post("/history/{history_id}/favorite", summary="切换收藏状态", dependencies=[DependAuth])
async def toggle_favorite(history_id: int):
    """切换查询历史的收藏状态"""
    try:
        user_id = CTX_USER_ID.get()
        is_favorite = await database_query_controller.toggle_favorite(history_id, user_id)
        return Success(data={"is_favorite": is_favorite}, msg="操作成功")
    except Exception as e:
        logger.error(f"切换收藏状态失败: {str(e)}")
        return Fail(msg=f"操作失败: {str(e)}")


@router.delete("/history/{history_id}", summary="删除查询历史", dependencies=[DependAuth])
async def delete_query_history(history_id: int):
    """删除查询历史"""
    try:
        user_id = CTX_USER_ID.get()
        history = await QueryHistory.get(id=history_id, user_id=user_id)
        await history.delete()
        return Success(msg="删除成功")
    except Exception as e:
        logger.error(f"删除查询历史失败: {str(e)}")
        return Fail(msg=f"删除失败: {str(e)}")
