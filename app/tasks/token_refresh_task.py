import logging
import asyncio
from datetime import datetime

from app.services.token_service import token_service

logger = logging.getLogger(__name__)


async def refresh_tokens_task():
    """
    Token自动刷新定时任务
    """
    try:
        logger.info("开始执行Token自动刷新任务")
        
        # 获取需要刷新Token的环境列表
        environment_ids = await token_service.get_environments_need_refresh()
        
        if not environment_ids:
            logger.info("没有需要刷新Token的环境")
            return
        
        logger.info(f"发现 {len(environment_ids)} 个环境需要刷新Token: {environment_ids}")
        
        # 批量刷新Token
        results = await token_service.refresh_tokens_for_environments(environment_ids)
        
        # 统计结果
        success_count = sum(1 for success in results.values() if success)
        failed_count = len(results) - success_count
        
        logger.info(f"Token刷新任务完成，成功: {success_count}, 失败: {failed_count}")
        
        # 记录失败的环境
        for env_id, success in results.items():
            if not success:
                logger.warning(f"环境 {env_id} Token刷新失败")
        
    except Exception as e:
        logger.error(f"Token自动刷新任务执行失败: {str(e)}")


def setup_token_refresh_scheduler(scheduler):
    """
    设置Token刷新定时任务
    
    Args:
        scheduler: APScheduler实例
    """
    try:
        # 添加Token刷新任务，每5分钟检查一次
        scheduler.add_job(
            func=refresh_tokens_task,
            trigger='interval',
            minutes=5,
            id='token_refresh_task',
            name='Token自动刷新任务',
            replace_existing=True
        )
        logger.info("Token自动刷新定时任务已设置，每5分钟执行一次")
        
    except Exception as e:
        logger.error(f"设置Token刷新定时任务失败: {str(e)}")
