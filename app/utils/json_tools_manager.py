import json
import re
import csv
import io
from typing import Dict, Any, List, Optional, Union
from xml.etree import ElementTree as ET
import yaml
import jsonpath_ng
from jsonschema import validate, ValidationError, Draft7Validator
from deepdiff import DeepDiff
import logging

logger = logging.getLogger(__name__)


class JsonToolsManager:
    """JSON工具管理器"""
    
    def __init__(self):
        self.logger = logger
    
    def format_json(self, json_str: str, indent: int = 2, sort_keys: bool = False) -> Dict[str, Any]:
        """
        格式化JSON
        
        Args:
            json_str: JSON字符串
            indent: 缩进空格数
            sort_keys: 是否排序键名
            
        Returns:
            格式化结果
        """
        try:
            # 解析JSON
            data = json.loads(json_str)
            
            # 格式化
            formatted = json.dumps(data, indent=indent, sort_keys=sort_keys, ensure_ascii=False)
            
            # 计算大小
            original_size = len(json_str)
            formatted_size = len(formatted)
            compression_ratio = round((1 - formatted_size / original_size) * 100, 2) if original_size > 0 else 0
            
            return {
                "formatted_json": formatted,
                "original_size": original_size,
                "formatted_size": formatted_size,
                "compression_ratio": compression_ratio
            }
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON格式错误: {str(e)}")
    
    def compress_json(self, json_str: str) -> Dict[str, Any]:
        """
        压缩JSON（移除空格和换行）
        
        Args:
            json_str: JSON字符串
            
        Returns:
            压缩结果
        """
        try:
            data = json.loads(json_str)
            compressed = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
            
            original_size = len(json_str)
            compressed_size = len(compressed)
            compression_ratio = round((1 - compressed_size / original_size) * 100, 2) if original_size > 0 else 0
            
            return {
                "formatted_json": compressed,
                "original_size": original_size,
                "formatted_size": compressed_size,
                "compression_ratio": compression_ratio
            }
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON格式错误: {str(e)}")
    
    def validate_json(self, json_str: str) -> Dict[str, Any]:
        """
        验证JSON格式
        
        Args:
            json_str: JSON字符串
            
        Returns:
            验证结果
        """
        try:
            json.loads(json_str)
            return {
                "is_valid": True,
                "error_message": None,
                "error_line": None,
                "error_column": None
            }
        except json.JSONDecodeError as e:
            # 尝试解析错误位置
            error_line = getattr(e, 'lineno', None)
            error_column = getattr(e, 'colno', None)
            
            return {
                "is_valid": False,
                "error_message": str(e),
                "error_line": error_line,
                "error_column": error_column
            }
    
    def json_to_xml(self, json_str: str) -> str:
        """JSON转XML"""
        try:
            data = json.loads(json_str)
            
            def dict_to_xml(tag, d):
                elem = ET.Element(tag)
                if isinstance(d, dict):
                    for key, val in d.items():
                        child = dict_to_xml(key, val)
                        elem.append(child)
                elif isinstance(d, list):
                    for i, item in enumerate(d):
                        child = dict_to_xml(f"item_{i}", item)
                        elem.append(child)
                else:
                    elem.text = str(d)
                return elem
            
            root = dict_to_xml("root", data)
            return ET.tostring(root, encoding='unicode')
        except Exception as e:
            raise ValueError(f"JSON转XML失败: {str(e)}")
    
    def xml_to_json(self, xml_str: str) -> str:
        """XML转JSON"""
        try:
            root = ET.fromstring(xml_str)
            
            def xml_to_dict(element):
                result = {}
                for child in element:
                    if len(child) == 0:
                        result[child.tag] = child.text
                    else:
                        result[child.tag] = xml_to_dict(child)
                return result
            
            data = xml_to_dict(root)
            return json.dumps(data, indent=2, ensure_ascii=False)
        except Exception as e:
            raise ValueError(f"XML转JSON失败: {str(e)}")
    
    def json_to_yaml(self, json_str: str) -> str:
        """JSON转YAML"""
        try:
            data = json.loads(json_str)
            return yaml.dump(data, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            raise ValueError(f"JSON转YAML失败: {str(e)}")
    
    def yaml_to_json(self, yaml_str: str) -> str:
        """YAML转JSON"""
        try:
            data = yaml.safe_load(yaml_str)
            return json.dumps(data, indent=2, ensure_ascii=False)
        except Exception as e:
            raise ValueError(f"YAML转JSON失败: {str(e)}")
    
    def json_to_csv(self, json_str: str, delimiter: str = ",") -> str:
        """JSON转CSV（仅支持数组格式）"""
        try:
            data = json.loads(json_str)
            if not isinstance(data, list) or not data:
                raise ValueError("JSON必须是非空数组格式")
            
            output = io.StringIO()
            if isinstance(data[0], dict):
                fieldnames = data[0].keys()
                writer = csv.DictWriter(output, fieldnames=fieldnames, delimiter=delimiter)
                writer.writeheader()
                writer.writerows(data)
            else:
                writer = csv.writer(output, delimiter=delimiter)
                for row in data:
                    writer.writerow([row] if not isinstance(row, list) else row)
            
            return output.getvalue()
        except Exception as e:
            raise ValueError(f"JSON转CSV失败: {str(e)}")
    
    def csv_to_json(self, csv_str: str, delimiter: str = ",") -> str:
        """CSV转JSON"""
        try:
            input_stream = io.StringIO(csv_str)
            reader = csv.DictReader(input_stream, delimiter=delimiter)
            data = list(reader)
            return json.dumps(data, indent=2, ensure_ascii=False)
        except Exception as e:
            raise ValueError(f"CSV转JSON失败: {str(e)}")
    
    def convert_format(self, data: str, source_format: str, target_format: str, csv_delimiter: str = ",") -> str:
        """
        格式转换
        
        Args:
            data: 源数据
            source_format: 源格式
            target_format: 目标格式
            csv_delimiter: CSV分隔符
            
        Returns:
            转换后的数据
        """
        if source_format == target_format:
            return data
        
        # 转换映射
        converters = {
            ("json", "xml"): self.json_to_xml,
            ("xml", "json"): self.xml_to_json,
            ("json", "yaml"): self.json_to_yaml,
            ("yaml", "json"): self.yaml_to_json,
            ("json", "csv"): lambda x: self.json_to_csv(x, csv_delimiter),
            ("csv", "json"): lambda x: self.csv_to_json(x, csv_delimiter),
        }
        
        converter = converters.get((source_format, target_format))
        if not converter:
            raise ValueError(f"不支持从{source_format}转换到{target_format}")
        
        return converter(data)
    
    def query_jsonpath(self, json_str: str, json_path: str) -> Dict[str, Any]:
        """
        JSONPath查询
        
        Args:
            json_str: JSON数据
            json_path: JSONPath表达式
            
        Returns:
            查询结果
        """
        try:
            data = json.loads(json_str)
            jsonpath_expr = jsonpath_ng.parse(json_path)
            matches = jsonpath_expr.find(data)
            
            results = [match.value for match in matches]
            
            return {
                "results": results,
                "count": len(results)
            }
        except Exception as e:
            raise ValueError(f"JSONPath查询失败: {str(e)}")
    
    def validate_schema(self, json_str: str, schema_str: str) -> Dict[str, Any]:
        """
        JSON Schema验证
        
        Args:
            json_str: JSON数据
            schema_str: JSON Schema
            
        Returns:
            验证结果
        """
        try:
            data = json.loads(json_str)
            schema = json.loads(schema_str)
            
            validator = Draft7Validator(schema)
            errors = list(validator.iter_errors(data))
            
            return {
                "is_valid": len(errors) == 0,
                "errors": [error.message for error in errors]
            }
        except Exception as e:
            raise ValueError(f"Schema验证失败: {str(e)}")
    
    def compare_json(self, json1_str: str, json2_str: str) -> Dict[str, Any]:
        """
        JSON对比
        
        Args:
            json1_str: 第一个JSON
            json2_str: 第二个JSON
            
        Returns:
            对比结果
        """
        try:
            data1 = json.loads(json1_str)
            data2 = json.loads(json2_str)
            
            diff = DeepDiff(data1, data2, ignore_order=True)
            
            differences = []
            if diff:
                for change_type, changes in diff.items():
                    if isinstance(changes, dict):
                        for path, change in changes.items():
                            differences.append({
                                "type": change_type,
                                "path": path,
                                "change": str(change)
                            })
                    else:
                        differences.append({
                            "type": change_type,
                            "change": str(changes)
                        })
            
            return {
                "is_equal": len(differences) == 0,
                "differences": differences
            }
        except Exception as e:
            raise ValueError(f"JSON对比失败: {str(e)}")
    
    def get_json_stats(self, json_str: str) -> Dict[str, Any]:
        """
        获取JSON统计信息
        
        Args:
            json_str: JSON字符串
            
        Returns:
            统计信息
        """
        try:
            data = json.loads(json_str)
            
            def analyze_data(obj, depth=0):
                stats = {
                    "keys": 0,
                    "values": 0,
                    "max_depth": depth,
                    "types": {}
                }
                
                if isinstance(obj, dict):
                    stats["keys"] += len(obj)
                    for key, value in obj.items():
                        sub_stats = analyze_data(value, depth + 1)
                        stats["values"] += sub_stats["values"]
                        stats["keys"] += sub_stats["keys"]
                        stats["max_depth"] = max(stats["max_depth"], sub_stats["max_depth"])
                        for t, count in sub_stats["types"].items():
                            stats["types"][t] = stats["types"].get(t, 0) + count
                    stats["values"] += len(obj)
                elif isinstance(obj, list):
                    for item in obj:
                        sub_stats = analyze_data(item, depth + 1)
                        stats["values"] += sub_stats["values"]
                        stats["keys"] += sub_stats["keys"]
                        stats["max_depth"] = max(stats["max_depth"], sub_stats["max_depth"])
                        for t, count in sub_stats["types"].items():
                            stats["types"][t] = stats["types"].get(t, 0) + count
                    stats["values"] += len(obj)
                else:
                    stats["values"] += 1
                    obj_type = type(obj).__name__
                    stats["types"][obj_type] = stats["types"].get(obj_type, 0) + 1
                
                return stats
            
            stats = analyze_data(data)
            
            return {
                "total_keys": stats["keys"],
                "total_values": stats["values"],
                "max_depth": stats["max_depth"],
                "data_types": stats["types"],
                "size_info": {
                    "characters": len(json_str),
                    "bytes": len(json_str.encode('utf-8')),
                    "lines": json_str.count('\n') + 1
                }
            }
        except Exception as e:
            raise ValueError(f"JSON统计失败: {str(e)}")


# 全局JSON工具管理器实例
json_tools_manager = JsonToolsManager()
