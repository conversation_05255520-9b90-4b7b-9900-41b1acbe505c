import json
import logging

logger = logging.getLogger(__name__)


def get_schema_properties(swagger_data, schema_ref, prefix="", max_depth=3, current_depth=0):
    """解析schema引用，获取属性列表，支持递归展开引用"""
    try:
        # 防止无限递归
        if current_depth >= max_depth:
            return []

        # 从#/components/schemas/xxx提取实际的schema名称
        schema_name = schema_ref.split('/')[-1]
        schema = swagger_data.get('components', {}).get('schemas', {}).get(schema_name, {})

        properties = []
        if 'properties' in schema:
            required_fields = schema.get('required', [])
            for field_name, field_info in schema['properties'].items():
                display_name = f"{prefix}.{field_name}" if prefix else field_name

                # 处理引用类型的字段（递归展开）
                if '$ref' in field_info:
                    # 递归解析引用的schema
                    nested_properties = get_schema_properties(
                        swagger_data,
                        field_info['$ref'],
                        display_name,
                        max_depth,
                        current_depth + 1
                    )
                    properties.extend(nested_properties)
                    continue

                field = {
                    "字段名": display_name,
                    "类型": field_info.get('type', 'string'),
                    "描述": field_info.get('description', ''),
                    "是否必填": field_name in required_fields,
                    "校验规则": []
                }

                # 添加校验规则
                rules = []
                if 'maxLength' in field_info:
                    rules.append(f"最大长度{field_info['maxLength']}")
                if 'minLength' in field_info:
                    rules.append(f"最小长度{field_info['minLength']}")
                if 'enum' in field_info:
                    rules.append(f"枚举值: {', '.join(map(str, field_info['enum']))}")
                if 'format' in field_info:
                    rules.append(f"格式: {field_info['format']}")
                if 'minimum' in field_info:
                    rules.append(f"最小值: {field_info['minimum']}")
                if 'maximum' in field_info:
                    rules.append(f"最大值: {field_info['maximum']}")

                field['校验规则'] = '；'.join(rules) if rules else '无'

                # 处理数组类型的字段
                if field_info.get('type') == 'array' and 'items' in field_info:
                    items_info = field_info['items']
                    if '$ref' in items_info:
                        field['类型'] = f"array[object]"
                        # 递归解析数组元素的schema
                        nested_properties = get_schema_properties(
                            swagger_data,
                            items_info['$ref'],
                            f"{display_name}[]",
                            max_depth,
                            current_depth + 1
                        )
                        properties.extend(nested_properties)
                    else:
                        field['类型'] = f"array[{items_info.get('type', 'object')}]"
                    if 'description' in items_info:
                        field['校验规则'] = f"{field['校验规则']}；数组元素：{items_info['description']}"

                properties.append(field)

        return properties
    except Exception as e:
        logger.error(f"解析schema属性失败: {e}")
        return []


def parse_parameters(parameters, swagger_data):
    """解析参数列表"""
    param_list = []
    if not parameters:
        return param_list

    for param in parameters:
        try:
            param_info = {
                "字段名": param.get('name', ''),
                "类型": param.get('schema', {}).get('type', 'string') if 'schema' in param else param.get('type', 'string'),
                "描述": param.get('description', ''),
                "是否必填": param.get('required', False),
                "位置": param.get('in', 'query'),  # query, path, header, cookie
                "校验规则": []
            }

            # 处理schema中的校验规则
            schema = param.get('schema', {})
            rules = []
            if 'enum' in schema:
                rules.append(f"枚举值: {', '.join(map(str, schema['enum']))}")
            if 'format' in schema:
                rules.append(f"格式: {schema['format']}")
            if 'minimum' in schema:
                rules.append(f"最小值: {schema['minimum']}")
            if 'maximum' in schema:
                rules.append(f"最大值: {schema['maximum']}")
            if 'minLength' in schema:
                rules.append(f"最小长度: {schema['minLength']}")
            if 'maxLength' in schema:
                rules.append(f"最大长度: {schema['maxLength']}")

            param_info['校验规则'] = '；'.join(rules) if rules else '无'
            param_list.append(param_info)
        except Exception as e:
            logger.error(f"解析参数失败: {e}")
            continue

    return param_list


def convert_swagger_to_interface_list(swagger_data):
    """将Swagger/OpenAPI文档转换为接口列表"""
    interface_list = []

    if 'paths' not in swagger_data:
        logger.error("Swagger文档中没有找到paths字段")
        return interface_list

    # 遍历所有路径
    for path, methods in swagger_data['paths'].items():
        for method, details in methods.items():
            try:
                # 获取接口基本信息
                summary = details.get('summary', '') or details.get('description', '') or f"{method.upper()} {path}"
                # 确保接口说明不为空且长度合理
                if not summary or len(summary.strip()) == 0:
                    summary = f"{method.upper()} {path}"
                elif len(summary) > 100:  # 限制长度，避免数据库字段溢出
                    summary = summary[:97] + "..."

                interface = {
                    "接口路径": path,
                    "请求方式": method.upper(),
                    "接口说明": summary.strip(),
                    "请求参数": []
                }

                # 收集所有参数
                all_params = []

                # 1. 处理路径参数和查询参数
                if 'parameters' in details:
                    path_query_params = parse_parameters(details['parameters'], swagger_data)
                    all_params.extend(path_query_params)

                # 2. 处理请求体参数
                if 'requestBody' in details:
                    try:
                        request_body = details['requestBody']
                        content = request_body.get('content', {})

                        # 处理JSON请求体
                        if 'application/json' in content:
                            schema = content['application/json']['schema']
                            if '$ref' in schema:
                                body_params = get_schema_properties(swagger_data, schema['$ref'])
                                # 标记为请求体参数
                                for param in body_params:
                                    param['位置'] = 'body'
                                all_params.extend(body_params)
                            elif schema.get('type') == 'array' and 'items' in schema:
                                # 处理数组类型的请求体
                                items = schema['items']
                                if '$ref' in items:
                                    # 数组元素是引用类型
                                    array_params = get_schema_properties(swagger_data, items['$ref'])
                                    for param in array_params:
                                        param['位置'] = 'body'
                                        param['类型'] = f"array[{param['类型']}]"
                                        param['描述'] = f"数组元素：{param['描述']}"
                                    all_params.extend(array_params)
                                elif 'properties' in items:
                                    # 数组元素是直接定义的对象
                                    required_fields = items.get('required', [])
                                    for field_name, field_info in items['properties'].items():
                                        param = {
                                            "字段名": field_name,
                                            "类型": f"array[{field_info.get('type', 'string')}]",
                                            "描述": f"数组元素：{field_info.get('description', '')}",
                                            "是否必填": field_name in required_fields,
                                            "位置": 'body',
                                            "校验规则": '无'
                                        }
                                        all_params.append(param)
                            elif 'properties' in schema:
                                # 直接定义的schema
                                required_fields = schema.get('required', [])
                                for field_name, field_info in schema['properties'].items():
                                    param = {
                                        "字段名": field_name,
                                        "类型": field_info.get('type', 'string'),
                                        "描述": field_info.get('description', ''),
                                        "是否必填": field_name in required_fields,
                                        "位置": 'body',
                                        "校验规则": '无'
                                    }
                                    all_params.append(param)

                        # 处理表单数据
                        elif 'application/x-www-form-urlencoded' in content or 'multipart/form-data' in content:
                            content_type = 'application/x-www-form-urlencoded' if 'application/x-www-form-urlencoded' in content else 'multipart/form-data'
                            schema = content[content_type]['schema']
                            if '$ref' in schema:
                                form_params = get_schema_properties(swagger_data, schema['$ref'])
                                for param in form_params:
                                    param['位置'] = 'form'
                                all_params.extend(form_params)
                    except Exception as e:
                        logger.error(f"处理请求体参数失败: {e}")

                # 3. 如果没有请求参数，尝试从响应体中提取参数信息（用于文档展示）
                if not all_params and 'responses' in details:
                    try:
                        # 查找200响应
                        response_200 = details['responses'].get('200', {})
                        if 'content' in response_200:
                            content = response_200['content']
                            # 支持多种content-type
                            schema = None
                            for content_type in ['application/json', '*/*', 'application/*']:
                                if content_type in content:
                                    schema = content[content_type]['schema']
                                    break

                            if schema and '$ref' in schema:
                                # 解析响应体schema作为参数参考
                                response_params = get_schema_properties(swagger_data, schema['$ref'])
                                for param in response_params:
                                    param['位置'] = 'response'  # 标记为响应参数
                                    param['描述'] = f"响应字段：{param['描述']}"
                                all_params.extend(response_params)
                    except Exception as e:
                        logger.error(f"处理响应体参数失败: {e}")

                interface['请求参数'] = all_params
                interface_list.append(interface)

            except Exception as e:
                logger.error(f"处理接口 {method.upper()} {path} 失败: {e}")
                continue

    logger.info(f"成功解析 {len(interface_list)} 个接口")
    return interface_list


# 使用示例
def main():
    """测试函数"""
    try:
        swagger_file_path = 'default_OpenAPI.json'
        with open(swagger_file_path, 'r', encoding='utf-8') as f:
            swagger_data = json.load(f)

        interfaces = convert_swagger_to_interface_list(swagger_data)
        interface_list = json.dumps(interfaces, ensure_ascii=False, indent=2)
        return interface_list
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return "[]"


if __name__ == '__main__':
    interface = main()
    print(f"解析结果: {len(json.loads(interface))} 个接口")
    # 打印前3个接口作为示例
    interfaces = json.loads(interface)
    for i, interface_info in enumerate(interfaces[:3]):
        print(f"\n接口 {i+1}:")
        print(f"  路径: {interface_info['接口路径']}")
        print(f"  方法: {interface_info['请求方式']}")
        print(f"  说明: {interface_info['接口说明']}")
        print(f"  参数数量: {len(interface_info['请求参数'])}")
        if interface_info['请求参数']:
            print("  参数详情:")
            for param in interface_info['请求参数'][:3]:  # 只显示前3个参数
                print(f"    - {param['字段名']} ({param['类型']}) - {param['描述']}")

    # create_test_case(interface)
