import json
import re
import logging
from typing import Dict, List, Any, Union
import jsonpath_ng
from jsonschema import validate, ValidationError

logger = logging.getLogger(__name__)


class AssertionExecutor:
    """断言执行器"""
    
    def __init__(self):
        self.assertion_methods = {
            'status_code': self._assert_status_code,
            'response_time': self._assert_response_time,
            'json_path': self._assert_json_path,
            'response_body': self._assert_response_body,
            'response_header': self._assert_response_header
        }
        
        self.operators = {
            'equals': self._op_equals,
            'not_equals': self._op_not_equals,
            'greater_than': self._op_greater_than,
            'less_than': self._op_less_than,
            'in_range': self._op_in_range,
            'contains': self._op_contains,
            'not_contains': self._op_not_contains,
            'regex': self._op_regex,
            'exists': self._op_exists,
            'not_exists': self._op_not_exists,
            'exact_match': self._op_exact_match,
            'partial_match': self._op_partial_match,
            'json_schema': self._op_json_schema
        }
    
    def execute_assertions(self, assertions: List[Dict], execution_result: Dict) -> Dict:
        """执行所有断言"""
        if not assertions:
            return {
                'all_passed': True,
                'total_count': 0,
                'passed_count': 0,
                'failed_count': 0,
                'results': []
            }
        
        results = []
        passed_count = 0
        
        for assertion in assertions:
            try:
                result = self._execute_single_assertion(assertion, execution_result)
                results.append(result)
                if result['passed']:
                    passed_count += 1
            except Exception as e:
                logger.error(f"断言执行失败: {str(e)}")
                results.append({
                    'passed': False,
                    'error_message': f"断言执行异常: {str(e)}",
                    **assertion
                })
        
        total_count = len(results)
        failed_count = total_count - passed_count
        
        return {
            'all_passed': passed_count == total_count,
            'total_count': total_count,
            'passed_count': passed_count,
            'failed_count': failed_count,
            'results': results
        }
    
    def _execute_single_assertion(self, assertion: Dict, execution_result: Dict) -> Dict:
        """执行单个断言"""
        assertion_type = assertion.get('type')
        
        if assertion_type not in self.assertion_methods:
            return {
                'passed': False,
                'error_message': f"不支持的断言类型: {assertion_type}",
                **assertion
            }
        
        try:
            return self.assertion_methods[assertion_type](assertion, execution_result)
        except Exception as e:
            return {
                'passed': False,
                'error_message': str(e),
                **assertion
            }
    
    def _assert_status_code(self, assertion: Dict, execution_result: Dict) -> Dict:
        """状态码断言"""
        actual_status = execution_result.get('status_code')
        expected_value = assertion.get('expected_value')
        operator = assertion.get('operator')
        
        if actual_status is None:
            return {
                'passed': False,
                'error_message': "响应中没有状态码",
                'actual_value': None,
                **assertion
            }
        
        try:
            expected_value = int(expected_value) if expected_value else 200
        except (ValueError, TypeError):
            return {
                'passed': False,
                'error_message': f"期望值不是有效的数字: {expected_value}",
                'actual_value': actual_status,
                **assertion
            }
        
        passed = self._apply_operator(actual_status, expected_value, operator)
        
        return {
            'passed': passed,
            'actual_value': actual_status,
            'expected_value': expected_value,
            **assertion
        }
    
    def _assert_response_time(self, assertion: Dict, execution_result: Dict) -> Dict:
        """响应时间断言"""
        actual_time = execution_result.get('execution_time')
        expected_value = assertion.get('expected_value')
        operator = assertion.get('operator')
        
        if actual_time is None:
            return {
                'passed': False,
                'error_message': "响应中没有执行时间",
                'actual_value': None,
                **assertion
            }
        
        try:
            expected_value = float(expected_value) if expected_value else 1000
        except (ValueError, TypeError):
            return {
                'passed': False,
                'error_message': f"期望值不是有效的数字: {expected_value}",
                'actual_value': actual_time,
                **assertion
            }
        
        passed = self._apply_operator(actual_time, expected_value, operator)
        
        return {
            'passed': passed,
            'actual_value': actual_time,
            'expected_value': expected_value,
            **assertion
        }
    
    def _assert_json_path(self, assertion: Dict, execution_result: Dict) -> Dict:
        """JSON路径断言"""
        response_body = execution_result.get('response_body')
        json_path = assertion.get('json_path')
        expected_value = assertion.get('expected_value')
        operator = assertion.get('operator')
        
        if not json_path:
            return {
                'passed': False,
                'error_message': "JSON路径不能为空",
                **assertion
            }
        
        try:
            # 解析JSON路径
            jsonpath_expr = jsonpath_ng.parse(json_path)
            matches = jsonpath_expr.find(response_body)
            
            if operator in ['exists', 'not_exists']:
                actual_value = len(matches) > 0
                passed = self._apply_operator(actual_value, True, operator)
                return {
                    'passed': passed,
                    'actual_value': f"路径存在: {actual_value}",
                    **assertion
                }
            
            if not matches:
                return {
                    'passed': False,
                    'error_message': f"JSON路径 {json_path} 未找到匹配项",
                    'actual_value': None,
                    **assertion
                }
            
            # 取第一个匹配值
            actual_value = matches[0].value
            passed = self._apply_operator(actual_value, expected_value, operator)
            
            return {
                'passed': passed,
                'actual_value': actual_value,
                'expected_value': expected_value,
                **assertion
            }
            
        except Exception as e:
            return {
                'passed': False,
                'error_message': f"JSON路径解析失败: {str(e)}",
                **assertion
            }
    
    def _assert_response_body(self, assertion: Dict, execution_result: Dict) -> Dict:
        """响应体断言"""
        response_body = execution_result.get('response_body')
        expected_value = assertion.get('expected_value')
        operator = assertion.get('operator')
        
        # 将响应体转换为字符串
        if isinstance(response_body, dict) or isinstance(response_body, list):
            actual_value = json.dumps(response_body, ensure_ascii=False)
        else:
            actual_value = str(response_body) if response_body is not None else ""
        
        passed = self._apply_operator(actual_value, expected_value, operator)
        
        return {
            'passed': passed,
            'actual_value': actual_value[:200] + "..." if len(actual_value) > 200 else actual_value,
            'expected_value': expected_value,
            **assertion
        }
    
    def _assert_response_header(self, assertion: Dict, execution_result: Dict) -> Dict:
        """响应头断言"""
        response_headers = execution_result.get('response_headers', {})
        header_name = assertion.get('header_name')
        expected_value = assertion.get('expected_value')
        operator = assertion.get('operator')
        
        if not header_name:
            return {
                'passed': False,
                'error_message': "响应头名称不能为空",
                **assertion
            }
        
        # 查找响应头（不区分大小写）
        actual_value = None
        for key, value in response_headers.items():
            if key.lower() == header_name.lower():
                actual_value = value
                break
        
        if operator in ['exists', 'not_exists']:
            exists = actual_value is not None
            passed = self._apply_operator(exists, True, operator)
            return {
                'passed': passed,
                'actual_value': f"响应头存在: {exists}",
                **assertion
            }
        
        if actual_value is None:
            return {
                'passed': False,
                'error_message': f"响应头 {header_name} 不存在",
                'actual_value': None,
                **assertion
            }
        
        passed = self._apply_operator(str(actual_value), expected_value, operator)
        
        return {
            'passed': passed,
            'actual_value': actual_value,
            'expected_value': expected_value,
            **assertion
        }
    
    def _apply_operator(self, actual: Any, expected: Any, operator: str) -> bool:
        """应用操作符"""
        if operator not in self.operators:
            raise ValueError(f"不支持的操作符: {operator}")
        
        return self.operators[operator](actual, expected)
    
    # 操作符实现
    def _op_equals(self, actual: Any, expected: Any) -> bool:
        return actual == expected
    
    def _op_not_equals(self, actual: Any, expected: Any) -> bool:
        return actual != expected
    
    def _op_greater_than(self, actual: Any, expected: Any) -> bool:
        try:
            return float(actual) > float(expected)
        except (ValueError, TypeError):
            return False
    
    def _op_less_than(self, actual: Any, expected: Any) -> bool:
        try:
            return float(actual) < float(expected)
        except (ValueError, TypeError):
            return False
    
    def _op_in_range(self, actual: Any, expected: Any) -> bool:
        try:
            # 期望值格式: "min,max"
            min_val, max_val = map(float, str(expected).split(','))
            actual_val = float(actual)
            return min_val <= actual_val <= max_val
        except (ValueError, TypeError):
            return False
    
    def _op_contains(self, actual: Any, expected: Any) -> bool:
        return str(expected) in str(actual)
    
    def _op_not_contains(self, actual: Any, expected: Any) -> bool:
        return str(expected) not in str(actual)
    
    def _op_regex(self, actual: Any, expected: Any) -> bool:
        try:
            return bool(re.search(str(expected), str(actual)))
        except re.error:
            return False
    
    def _op_exists(self, actual: Any, expected: Any) -> bool:
        return actual is True
    
    def _op_not_exists(self, actual: Any, expected: Any) -> bool:
        return actual is False
    
    def _op_exact_match(self, actual: Any, expected: Any) -> bool:
        return str(actual) == str(expected)
    
    def _op_partial_match(self, actual: Any, expected: Any) -> bool:
        return str(expected) in str(actual)
    
    def _op_json_schema(self, actual: Any, expected: Any) -> bool:
        try:
            schema = json.loads(str(expected))
            if isinstance(actual, str):
                data = json.loads(actual)
            else:
                data = actual
            validate(instance=data, schema=schema)
            return True
        except (json.JSONDecodeError, ValidationError, Exception):
            return False


# 创建全局实例
assertion_executor = AssertionExecutor()
