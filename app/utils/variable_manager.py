"""
变量管理器 - 处理测试用例间的参数传递
"""
import json
import re
from typing import Dict, Any, List, Optional
from jsonpath_ng import parse
from loguru import logger
from .faker_manager import faker_manager


class VariableManager:
    """变量管理器，用于处理测试用例间的参数传递"""
    
    def __init__(self):
        self.variables: Dict[str, Any] = {}
    
    def extract_variables(self, extract_configs: List[Dict], response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从响应数据中提取变量
        
        Args:
            extract_configs: 变量提取配置列表
            response_data: 响应数据，包含status_code, response_body, response_headers等
            
        Returns:
            提取的变量字典
        """
        extracted_vars = {}
        
        if not extract_configs:
            return extracted_vars
            
        for config in extract_configs:
            try:
                var_name = config.get('variable_name', '')
                extract_type = config.get('extract_type', 'json_path')  # json_path, header, status_code
                extract_path = config.get('extract_path', '')
                default_value = config.get('default_value', '')
                
                if not var_name:
                    continue
                    
                extracted_value = None
                
                if extract_type == 'json_path':
                    # 从响应体中提取
                    response_body = response_data.get('response_body', {})
                    if isinstance(response_body, (dict, list)) and extract_path:
                        try:
                            jsonpath_expr = parse(extract_path)
                            matches = jsonpath_expr.find(response_body)
                            if matches:
                                extracted_value = matches[0].value
                        except Exception as e:
                            logger.warning(f"JSONPath提取失败: {extract_path}, 错误: {str(e)}")
                            
                elif extract_type == 'header':
                    # 从响应头中提取
                    response_headers = response_data.get('response_headers', {})
                    if extract_path and extract_path in response_headers:
                        extracted_value = response_headers[extract_path]
                        
                elif extract_type == 'status_code':
                    # 提取状态码
                    extracted_value = response_data.get('status_code')
                    
                elif extract_type == 'regex':
                    # 正则表达式提取
                    response_body = response_data.get('response_body', '')
                    if isinstance(response_body, str) and extract_path:
                        try:
                            match = re.search(extract_path, response_body)
                            if match:
                                extracted_value = match.group(1) if match.groups() else match.group(0)
                        except Exception as e:
                            logger.warning(f"正则表达式提取失败: {extract_path}, 错误: {str(e)}")
                
                # 使用提取的值或默认值
                final_value = extracted_value if extracted_value is not None else default_value
                
                if final_value is not None:
                    extracted_vars[var_name] = final_value
                    self.variables[var_name] = final_value
                    logger.info(f"提取变量: {var_name} = {final_value}")
                    
            except Exception as e:
                logger.error(f"变量提取失败: {config}, 错误: {str(e)}")
                
        return extracted_vars
    
    def replace_variables(self, text: str) -> str:
        """
        替换文本中的变量引用
        
        Args:
            text: 包含变量引用的文本，格式如 ${variable_name}
            
        Returns:
            替换后的文本
        """
        if not text or not isinstance(text, str):
            return text
            
        # 查找所有变量引用 ${variable_name}
        pattern = r'\$\{([^}]+)\}'
        
        def replace_func(match):
            var_name = match.group(1)

            # 首先检查是否是用户定义的变量
            if var_name in self.variables:
                replacement = str(self.variables[var_name])
                logger.info(f"替换用户变量: ${{{var_name}}} -> {replacement}")
                return replacement

            # 然后尝试生成Faker数据
            faker_data = faker_manager.generate_data(var_name)
            if faker_data is not None:
                replacement = str(faker_data)
                logger.info(f"替换Faker变量: ${{{var_name}}} -> {replacement}")
                return replacement

            # 如果都没有找到，保持原样
            logger.warning(f"未找到变量: {var_name}")
            return match.group(0)  # 保持原样
                
        return re.sub(pattern, replace_func, text)
    
    def replace_variables_in_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        递归替换字典中的变量引用
        
        Args:
            data: 包含变量引用的字典
            
        Returns:
            替换后的字典
        """
        if not isinstance(data, dict):
            return data
            
        result = {}
        for key, value in data.items():
            if isinstance(value, str):
                result[key] = self.replace_variables(value)
            elif isinstance(value, dict):
                result[key] = self.replace_variables_in_dict(value)
            elif isinstance(value, list):
                result[key] = self.replace_variables_in_list(value)
            else:
                result[key] = value
                
        return result
    
    def replace_variables_in_list(self, data: List[Any]) -> List[Any]:
        """
        递归替换列表中的变量引用
        
        Args:
            data: 包含变量引用的列表
            
        Returns:
            替换后的列表
        """
        if not isinstance(data, list):
            return data
            
        result = []
        for item in data:
            if isinstance(item, str):
                result.append(self.replace_variables(item))
            elif isinstance(item, dict):
                result.append(self.replace_variables_in_dict(item))
            elif isinstance(item, list):
                result.append(self.replace_variables_in_list(item))
            else:
                result.append(item)
                
        return result
    
    def process_test_case_data(self, test_case_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理测试用例数据，替换其中的变量引用
        
        Args:
            test_case_data: 测试用例数据，包含url, params, body等
            
        Returns:
            处理后的测试用例数据
        """
        processed_data = test_case_data.copy()
        
        # 替换URL中的变量
        if 'url' in processed_data:
            processed_data['url'] = self.replace_variables(processed_data['url'])
            
        # 替换参数中的变量
        if 'params' in processed_data and processed_data['params']:
            if isinstance(processed_data['params'], str):
                try:
                    params_dict = json.loads(processed_data['params'])
                    processed_params = self.replace_variables_in_dict(params_dict)
                    processed_data['params'] = processed_params
                except json.JSONDecodeError:
                    processed_data['params'] = self.replace_variables(processed_data['params'])
            elif isinstance(processed_data['params'], dict):
                processed_data['params'] = self.replace_variables_in_dict(processed_data['params'])
                
        # 替换请求体中的变量
        if 'body' in processed_data and processed_data['body']:
            if isinstance(processed_data['body'], str):
                try:
                    body_dict = json.loads(processed_data['body'])
                    processed_body = self.replace_variables_in_dict(body_dict)
                    processed_data['body'] = json.dumps(processed_body, ensure_ascii=False)
                except json.JSONDecodeError:
                    processed_data['body'] = self.replace_variables(processed_data['body'])
            elif isinstance(processed_data['body'], dict):
                processed_data['body'] = self.replace_variables_in_dict(processed_data['body'])
                
        return processed_data
    
    def get_variables(self) -> Dict[str, Any]:
        """获取当前所有变量"""
        return self.variables.copy()
    
    def set_variable(self, name: str, value: Any):
        """设置变量"""
        self.variables[name] = value
        logger.info(f"设置变量: {name} = {value}")
    
    def clear_variables(self):
        """清空所有变量"""
        self.variables.clear()
        logger.info("清空所有变量")


# 创建全局变量管理器实例
variable_manager = VariableManager()
