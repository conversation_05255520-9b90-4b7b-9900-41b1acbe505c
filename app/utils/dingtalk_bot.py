"""
钉钉机器人工具类
"""
import json
import time
import hmac
import hashlib
import base64
import urllib.parse
from typing import Dict, Any, Optional
import aiohttp
import asyncio
from app.log import logger


class DingTalkBot:
    """钉钉机器人类"""
    
    def __init__(self, webhook_url: str, secret: Optional[str] = None):
        """
        初始化钉钉机器人
        
        Args:
            webhook_url: 钉钉机器人webhook地址
            secret: 钉钉机器人密钥（可选）
        """
        self.webhook_url = webhook_url
        self.secret = secret
    
    def _generate_sign(self, timestamp: str) -> str:
        """
        生成签名
        
        Args:
            timestamp: 时间戳
            
        Returns:
            签名字符串
        """
        if not self.secret:
            return ""
            
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(
            self.secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign
    
    def _build_url(self) -> str:
        """
        构建请求URL
        
        Returns:
            完整的请求URL
        """
        if not self.secret:
            return self.webhook_url
            
        timestamp = str(round(time.time() * 1000))
        sign = self._generate_sign(timestamp)
        return f"{self.webhook_url}&timestamp={timestamp}&sign={sign}"
    
    async def send_text(self, content: str, at_mobiles: list = None, at_all: bool = False) -> Dict[str, Any]:
        """
        发送文本消息
        
        Args:
            content: 消息内容
            at_mobiles: @的手机号列表
            at_all: 是否@所有人
            
        Returns:
            发送结果
        """
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            },
            "at": {
                "atMobiles": at_mobiles or [],
                "isAtAll": at_all
            }
        }
        
        return await self._send_request(data)
    
    async def send_markdown(self, title: str, text: str, at_mobiles: list = None, at_all: bool = False) -> Dict[str, Any]:
        """
        发送Markdown消息
        
        Args:
            title: 消息标题
            text: Markdown内容
            at_mobiles: @的手机号列表
            at_all: 是否@所有人
            
        Returns:
            发送结果
        """
        data = {
            "msgtype": "markdown",
            "markdown": {
                "title": title,
                "text": text
            },
            "at": {
                "atMobiles": at_mobiles or [],
                "isAtAll": at_all
            }
        }
        
        return await self._send_request(data)
    
    async def send_test_report(self, report_data: Dict[str, Any], plan_name: str = "") -> Dict[str, Any]:
        """
        发送测试报告
        
        Args:
            report_data: 测试报告数据
            plan_name: 测试计划名称
            
        Returns:
            发送结果
        """
        # 构建Markdown格式的测试报告
        title = f"测试执行报告{' - ' + plan_name if plan_name else ''}"
        
        # 执行结果状态
        status_emoji = "✅" if report_data.get('execution_result') == 'success' else "❌"
        status_text = "执行成功" if report_data.get('execution_result') == 'success' else "执行失败"
        
        # 构建Markdown内容
        markdown_text = f"""
# {status_emoji} {title}

## 📊 执行摘要
- **执行结果**: {status_text}
- **总用例数**: {report_data.get('total_cases', 0)}
- **通过用例**: {report_data.get('passed_cases', 0)}
- **失败用例**: {report_data.get('failed_cases', 0)}
- **通过率**: {report_data.get('pass_rate', 0)}%
- **执行时间**: {self._format_execution_time(report_data.get('execution_time', 0))}

## 📋 用例详情
"""
        
        # 添加失败用例详情
        case_results = report_data.get('case_results', [])
        failed_cases = [case for case in case_results if not case.get('success', False)]
        
        if failed_cases:
            markdown_text += "\n### ❌ 失败用例:\n"
            for i, case in enumerate(failed_cases[:5], 1):  # 最多显示5个失败用例
                markdown_text += f"{i}. **{case.get('case_name', 'Unknown')}**\n"
                markdown_text += f"   - 状态码: {case.get('status_code', 'N/A')}\n"
                markdown_text += f"   - 响应时间: {case.get('response_time', 0)}ms\n"
                if case.get('error_message'):
                    markdown_text += f"   - 错误信息: {case.get('error_message')[:100]}...\n"
                markdown_text += "\n"
            
            if len(failed_cases) > 5:
                markdown_text += f"... 还有 {len(failed_cases) - 5} 个失败用例\n"
        else:
            markdown_text += "\n🎉 所有用例都执行成功！\n"
        
        markdown_text += f"\n---\n⏰ 报告生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
        
        return await self.send_markdown(title, markdown_text)
    
    def _format_execution_time(self, time_ms: int) -> str:
        """
        格式化执行时间
        
        Args:
            time_ms: 执行时间（毫秒）
            
        Returns:
            格式化后的时间字符串
        """
        if time_ms < 1000:
            return f"{time_ms}ms"
        elif time_ms < 60000:
            return f"{time_ms / 1000:.2f}s"
        else:
            minutes = time_ms // 60000
            seconds = (time_ms % 60000) / 1000
            return f"{minutes}m{seconds:.1f}s"
    
    async def _send_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            data: 请求数据
            
        Returns:
            响应结果
        """
        url = self._build_url()
        headers = {
            'Content-Type': 'application/json'
        }
        
        try:
            # 创建SSL上下文，跳过证书验证（仅用于测试）
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            connector = aiohttp.TCPConnector(ssl=ssl_context)

            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.post(url, json=data, headers=headers, timeout=30) as response:
                    result = await response.json()

                    if result.get('errcode') == 0:
                        logger.info("钉钉消息发送成功")
                        return {"success": True, "message": "发送成功", "data": result}
                    else:
                        logger.error(f"钉钉消息发送失败: {result}")
                        return {"success": False, "message": result.get('errmsg', '发送失败'), "data": result}
                        
        except asyncio.TimeoutError:
            logger.error("钉钉消息发送超时")
            return {"success": False, "message": "发送超时"}
        except Exception as e:
            logger.error(f"钉钉消息发送异常: {str(e)}")
            return {"success": False, "message": f"发送异常: {str(e)}"}


# 创建默认的钉钉机器人实例
default_dingtalk_bot = DingTalkBot(
    webhook_url="https://oapi.dingtalk.com/robot/send?access_token=a4ca5597fb378abffcf3c8f894cdf5909bc1382edc49304cd42a4e8ed9151e55"
)
