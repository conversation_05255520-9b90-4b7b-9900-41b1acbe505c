"""
Faker数据生成管理器 - 提供各种随机数据生成功能
"""
import json
import re
from typing import Dict, Any, List, Optional
from faker import Faker
from faker.providers import BaseProvider
import random
from datetime import datetime, timedelta
from loguru import logger


class CustomProvider(BaseProvider):
    """自定义Faker数据提供者"""
    
    def chinese_id_card(self):
        """生成中国身份证号"""
        # 地区代码（前6位）
        area_codes = ['110101', '310101', '440101', '500101', '120101', '210101']
        area_code = random.choice(area_codes)
        
        # 出生日期（8位）
        start_date = datetime(1950, 1, 1)
        end_date = datetime(2005, 12, 31)
        birth_date = start_date + timedelta(
            days=random.randint(0, (end_date - start_date).days)
        )
        birth_str = birth_date.strftime('%Y%m%d')
        
        # 顺序码（3位）
        sequence = f"{random.randint(1, 999):03d}"
        
        # 前17位
        id_17 = area_code + birth_str + sequence
        
        # 校验码计算
        weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        sum_val = sum(int(id_17[i]) * weights[i] for i in range(17))
        check_code = check_codes[sum_val % 11]
        
        return id_17 + check_code
    
    def chinese_bank_card(self):
        """生成中国银行卡号"""
        # 常见银行卡BIN码
        bin_codes = [
            '622202',  # 工商银行
            '622700',  # 建设银行
            '622848',  # 农业银行
            '621700',  # 中国银行
            '622588',  # 招商银行
        ]
        bin_code = random.choice(bin_codes)
        
        # 生成后面的数字
        remaining_digits = ''.join([str(random.randint(0, 9)) for _ in range(13)])
        
        return bin_code + remaining_digits


class FakerManager:
    """Faker数据生成管理器"""
    
    def __init__(self, locale='zh_CN'):
        """初始化Faker实例"""
        self.faker = Faker(locale)
        self.faker.add_provider(CustomProvider)
        
        # 数据类型配置
        self.data_types = {
            # 个人信息
            'personal': {
                'name': {'method': 'name', 'description': '姓名'},
                'first_name': {'method': 'first_name', 'description': '名字'},
                'last_name': {'method': 'last_name', 'description': '姓氏'},
                'male_name': {'method': 'name_male', 'description': '男性姓名'},
                'female_name': {'method': 'name_female', 'description': '女性姓名'},
                'id_card': {'method': 'chinese_id_card', 'description': '身份证号'},
                'chinese_id_card': {'method': 'chinese_id_card', 'description': '身份证号'},
                'ssn': {'method': 'ssn', 'description': '社会保障号'},
            },
            
            # 联系信息
            'contact': {
                'email': {'method': 'email', 'description': '邮箱地址'},
                'phone': {'method': 'phone_number', 'description': '电话号码'},
                'mobile': {'method': 'phone_number', 'description': '手机号码'},
                'address': {'method': 'address', 'description': '地址'},
                'city': {'method': 'city', 'description': '城市'},
                'province': {'method': 'province', 'description': '省份'},
                'postcode': {'method': 'postcode', 'description': '邮政编码'},
                'country': {'method': 'country', 'description': '国家'},
            },
            
            # 公司信息
            'company': {
                'company': {'method': 'company', 'description': '公司名称'},
                'job': {'method': 'job', 'description': '职位'},
                'company_email': {'method': 'company_email', 'description': '企业邮箱'},
            },
            
            # 网络信息
            'internet': {
                'url': {'method': 'url', 'description': '网址'},
                'domain': {'method': 'domain_name', 'description': '域名'},
                'ip': {'method': 'ipv4', 'description': 'IP地址'},
                'ipv6': {'method': 'ipv6', 'description': 'IPv6地址'},
                'mac_address': {'method': 'mac_address', 'description': 'MAC地址'},
                'user_agent': {'method': 'user_agent', 'description': '用户代理'},
                'username': {'method': 'user_name', 'description': '用户名'},
                'password': {'method': 'password', 'description': '密码'},
            },
            
            # 日期时间
            'datetime': {
                'date': {'method': 'date', 'description': '日期'},
                'time': {'method': 'time', 'description': '时间'},
                'datetime': {'method': 'date_time', 'description': '日期时间'},
                'timestamp': {'method': 'unix_time', 'description': '时间戳'},
                'future_date': {'method': 'future_date', 'description': '未来日期'},
                'past_date': {'method': 'past_date', 'description': '过去日期'},
                'year': {'method': 'year', 'description': '年份'},
                'month': {'method': 'month', 'description': '月份'},
                'day': {'method': 'day_of_month', 'description': '日期'},
            },
            
            # 数字
            'number': {
                'integer': {'method': 'random_int', 'description': '随机整数', 'params': {'min': 1, 'max': 1000}},
                'float': {'method': 'pyfloat', 'description': '随机浮点数', 'params': {'left_digits': 3, 'right_digits': 2, 'positive': True}},
                'digit': {'method': 'random_digit', 'description': '随机数字'},
                'digits': {'method': 'random_number', 'description': '随机数字串', 'params': {'digits': 6}},
                'integer4': {'method': 'random_int', 'description': '随机4位整数', 'params': {'min': 1000, 'max': 9999}},
                'integer5': {'method': 'random_int', 'description': '随机5位整数', 'params': {'min': 10000, 'max': 99999}},
                'integer6': {'method': 'random_int', 'description': '随机6位整数', 'params': {'min': 100000, 'max': 999999}},
                'integer7': {'method': 'random_int', 'description': '随机7位整数', 'params': {'min': 1000000, 'max': 9999999}},
                'integer8': {'method': 'random_int', 'description': '随机8位整数', 'params': {'min': 10000000, 'max': 99999999}}
            },
            
            # 文本
            'text': {
                'word': {'method': 'word', 'description': '单词'},
                'words': {'method': 'words', 'description': '多个单词', 'params': {'nb': 3}},
                'sentence': {'method': 'sentence', 'description': '句子'},
                'sentences': {'method': 'sentences', 'description': '多个句子', 'params': {'nb': 3}},
                'paragraph': {'method': 'paragraph', 'description': '段落'},
                'text': {'method': 'text', 'description': '文本', 'params': {'max_nb_chars': 200}},
                'uuid': {'method': 'uuid4', 'description': 'UUID'},
            },
            
            # 金融
            'finance': {
                'bank_card': {'method': 'chinese_bank_card', 'description': '银行卡号'},
                'chinese_bank_card': {'method': 'chinese_bank_card', 'description': '银行卡号'},
                'credit_card': {'method': 'credit_card_number', 'description': '信用卡号'},
                'currency_code': {'method': 'currency_code', 'description': '货币代码'},
                'price': {'method': 'pydecimal', 'description': '价格', 'params': {'left_digits': 4, 'right_digits': 2, 'positive': True}},
            },
            
            # 颜色
            'color': {
                'color_name': {'method': 'color_name', 'description': '颜色名称'},
                'hex_color': {'method': 'hex_color', 'description': '十六进制颜色'},
                'rgb_color': {'method': 'rgb_color', 'description': 'RGB颜色'},
            },
            
            # 文件
            'file': {
                'file_name': {'method': 'file_name', 'description': '文件名'},
                'file_extension': {'method': 'file_extension', 'description': '文件扩展名'},
                'mime_type': {'method': 'mime_type', 'description': 'MIME类型'},
            }
        }
    
    def get_data_types(self) -> Dict[str, Dict[str, Any]]:
        """获取所有数据类型配置"""
        return self.data_types
    
    def generate_data(self, data_type: str, category: str = None) -> Any:
        """
        生成指定类型的随机数据
        
        Args:
            data_type: 数据类型
            category: 数据分类
            
        Returns:
            生成的随机数据
        """
        try:
            # 查找数据类型配置
            config = None
            if category and category in self.data_types:
                if data_type in self.data_types[category]:
                    config = self.data_types[category][data_type]
            else:
                # 在所有分类中查找
                for cat_config in self.data_types.values():
                    if data_type in cat_config:
                        config = cat_config[data_type]
                        break
            
            if not config:
                logger.warning(f"未找到数据类型配置: {data_type}")
                return None
            
            method_name = config['method']
            params = config.get('params', {})
            
            # 调用Faker方法生成数据
            if hasattr(self.faker, method_name):
                method = getattr(self.faker, method_name)
                if params:
                    return method(**params)
                else:
                    return method()
            else:
                logger.warning(f"Faker方法不存在: {method_name}")
                return None
                
        except Exception as e:
            logger.error(f"生成随机数据失败: {str(e)}")
            return None
    
    def generate_sample_data(self) -> Dict[str, Dict[str, Any]]:
        """生成所有类型的示例数据"""
        samples = {}
        
        for category, types in self.data_types.items():
            samples[category] = {}
            for data_type, config in types.items():
                try:
                    sample_value = self.generate_data(data_type, category)
                    samples[category][data_type] = {
                        'description': config['description'],
                        'sample': str(sample_value) if sample_value is not None else 'N/A',
                        'variable': f'${{{data_type}}}'
                    }
                except Exception as e:
                    logger.error(f"生成示例数据失败 {category}.{data_type}: {str(e)}")
                    samples[category][data_type] = {
                        'description': config['description'],
                        'sample': 'Error',
                        'variable': f'${{{data_type}}}'
                    }
        
        return samples
    
    def replace_faker_variables(self, text: str) -> str:
        """
        替换文本中的Faker变量
        
        Args:
            text: 包含Faker变量的文本
            
        Returns:
            替换后的文本
        """
        if not text or not isinstance(text, str):
            return text
        
        # 匹配 ${variable_name} 格式的变量
        pattern = r'\$\{([^}]+)\}'
        
        def replace_variable(match):
            var_name = match.group(1)
            # 生成对应的随机数据
            value = self.generate_data(var_name)
            return str(value) if value is not None else match.group(0)
        
        return re.sub(pattern, replace_variable, text)


# 全局Faker管理器实例
faker_manager = FakerManager()
