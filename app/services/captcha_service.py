import logging
import httpx
import base64
import io
import re
from typing import Optional, Dict, Any, <PERSON><PERSON>
from jsonpath_ng import parse

# OCR相关导入
try:
    import ddddocr
    OCR_AVAILABLE = True
except ImportError as e:
    OCR_AVAILABLE = False
    logging.warning(f"ddddocr库导入失败: {e}，验证码识别功能将不可用")

logger = logging.getLogger(__name__)


class CaptchaService:
    """验证码服务"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=10.0)
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()
    
    async def get_captcha(
        self, 
        captcha_url: str,
        captcha_method: str = "GET",
        captcha_headers: Optional[Dict[str, Any]] = None,
        captcha_body: Optional[Dict[str, Any]] = None,
        captcha_image_path: str = "content.imageBase64",
        captcha_key_path: str = "content.codeKey",
        max_retries: int = 3
    ) -> Optional[Tuple[str, str]]:
        """
        获取验证码图片和Key
        
        Args:
            captcha_url: 验证码获取URL
            captcha_method: 请求方法
            captcha_headers: 请求头
            captcha_body: 请求体
            captcha_image_path: 验证码图片字段路径
            captcha_key_path: 验证码Key字段路径
            max_retries: 最大重试次数
            
        Returns:
            (image_base64, code_key) 元组，失败返回None
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"获取验证码，第 {attempt + 1} 次尝试")
                
                # 准备请求参数
                headers = captcha_headers or {}
                body = captcha_body or {}
                
                # 发送请求
                if captcha_method.upper() == "GET":
                    response = await self.client.get(captcha_url, headers=headers)
                elif captcha_method.upper() == "POST":
                    if headers.get("Content-Type") == "application/json" or not headers.get("Content-Type"):
                        response = await self.client.post(captcha_url, json=body, headers=headers)
                    else:
                        response = await self.client.post(captcha_url, data=body, headers=headers)
                elif captcha_method.upper() == "PUT":
                    response = await self.client.put(captcha_url, json=body, headers=headers)
                elif captcha_method.upper() == "DELETE":
                    response = await self.client.delete(captcha_url, headers=headers)
                else:
                    logger.error(f"不支持的请求方法: {captcha_method}")
                    continue
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"验证码API返回数据: {data}")
                    
                    # 解析验证码图片
                    image_base64 = self._extract_field(data, captcha_image_path)
                    if not image_base64:
                        logger.warning(f"无法提取验证码图片，路径: {captcha_image_path}")
                        continue
                    
                    # 解析验证码Key
                    code_key = self._extract_field(data, captcha_key_path)
                    if not code_key:
                        logger.warning(f"无法提取验证码Key，路径: {captcha_key_path}")
                        continue
                    
                    logger.info(f"成功获取验证码，codeKey: {code_key}")
                    return image_base64, code_key
                else:
                    logger.warning(f"验证码请求失败，状态码: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"获取验证码失败，第 {attempt + 1} 次尝试: {str(e)}")
                
        logger.error(f"获取验证码失败，已重试 {max_retries} 次")
        return None
    
    def _extract_field(self, data: Dict[str, Any], field_path: str) -> Optional[str]:
        """
        从数据中提取字段值
        
        Args:
            data: 响应数据
            field_path: 字段路径
            
        Returns:
            提取到的字段值
        """
        try:
            # 使用JSONPath解析
            parser = parse(field_path)
            matches = parser.find(data)
            if matches:
                return str(matches[0].value)
            else:
                logger.warning(f"JSONPath未找到匹配结果: {field_path}")
                return None
        except Exception as e:
            logger.error(f"解析字段路径失败: {field_path}, 错误: {str(e)}")
            return None
    
    async def recognize_captcha(self, image_base64: str, max_retries: int = 3) -> Optional[str]:
        """
        使用ddddocr识别验证码图片

        Args:
            image_base64: Base64编码的验证码图片
            max_retries: 最大重试次数

        Returns:
            识别出的验证码文本，失败返回None
        """
        if not OCR_AVAILABLE:
            logger.error("ddddocr库未安装，无法识别验证码")
            return None

        for attempt in range(max_retries):
            try:
                logger.info(f"开始识别验证码，第 {attempt + 1} 次尝试")

                # 解码Base64图片
                image_data = self._decode_base64_image(image_base64)
                if not image_data:
                    logger.error("Base64图片解码失败")
                    continue

                # 使用ddddocr识别
                recognized_text = self._ocr_with_ddddocr(image_data)

                if recognized_text:
                    # 清理识别结果
                    cleaned_text = self._clean_recognized_text(recognized_text)
                    if cleaned_text:
                        logger.info(f"验证码识别成功: {cleaned_text}")
                        return cleaned_text
                    else:
                        logger.warning(f"识别结果清理后为空: {recognized_text}")
                else:
                    logger.warning("ddddocr识别结果为空")

            except Exception as e:
                logger.error(f"验证码识别失败，第 {attempt + 1} 次尝试: {str(e)}")

        logger.error(f"验证码识别失败，已重试 {max_retries} 次")
        return None

    def _decode_base64_image(self, image_base64: str) -> Optional[bytes]:
        """
        解码Base64图片

        Args:
            image_base64: Base64编码的图片

        Returns:
            图片字节数据
        """
        try:
            # 处理data URL格式
            if image_base64.startswith('data:image/'):
                # 提取Base64部分
                base64_data = image_base64.split(',', 1)[1] if ',' in image_base64 else image_base64
            else:
                base64_data = image_base64

            # 解码Base64
            image_bytes = base64.b64decode(base64_data)
            return image_bytes

        except Exception as e:
            logger.error(f"Base64图片解码失败: {str(e)}")
            return None

    def _ocr_with_ddddocr(self, image_data: bytes) -> Optional[str]:
        """
        使用ddddocr识别验证码

        Args:
            image_data: 图片字节数据

        Returns:
            识别出的文本
        """
        try:
            # 创建ddddocr实例
            ocr = ddddocr.DdddOcr(show_ad=False)

            # 直接使用ddddocr识别图片字节数据
            recognized_text = ocr.classification(image_data)

            logger.info(f"ddddocr识别结果: {recognized_text}")
            return recognized_text

        except Exception as e:
            logger.error(f"ddddocr识别失败: {str(e)}")
            return None

    def _clean_recognized_text(self, text: str) -> Optional[str]:
        """
        清理识别出的文本

        Args:
            text: 原始识别文本

        Returns:
            清理后的文本
        """
        try:
            if not text:
                return None

            # 移除空白字符
            cleaned = text.strip()

            # 移除非字母数字字符
            cleaned = re.sub(r'[^a-zA-Z0-9]', '', cleaned)

            # 验证码长度通常在3-8位之间
            if len(cleaned) < 3 or len(cleaned) > 8:
                logger.warning(f"识别结果长度异常: {len(cleaned)} 位，内容: {cleaned}")
                return None

            return cleaned

        except Exception as e:
            logger.error(f"文本清理失败: {str(e)}")
            return None
    
    async def get_captcha_with_recognition(
        self,
        captcha_url: str,
        captcha_method: str = "GET", 
        captcha_headers: Optional[Dict[str, Any]] = None,
        captcha_body: Optional[Dict[str, Any]] = None,
        captcha_image_path: str = "content.imageBase64",
        captcha_key_path: str = "content.codeKey",
        max_retries: int = 3
    ) -> Optional[Tuple[str, str]]:
        """
        获取验证码并识别
        
        Args:
            captcha_url: 验证码获取URL
            captcha_method: 请求方法
            captcha_headers: 请求头
            captcha_body: 请求体
            captcha_image_path: 验证码图片字段路径
            captcha_key_path: 验证码Key字段路径
            max_retries: 最大重试次数
            
        Returns:
            (recognized_code, code_key) 元组，失败返回None
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"获取并识别验证码，第 {attempt + 1} 次尝试")
                
                # 获取验证码
                captcha_result = await self.get_captcha(
                    captcha_url=captcha_url,
                    captcha_method=captcha_method,
                    captcha_headers=captcha_headers,
                    captcha_body=captcha_body,
                    captcha_image_path=captcha_image_path,
                    captcha_key_path=captcha_key_path,
                    max_retries=1  # 内部不重试，由外层控制重试
                )
                
                if not captcha_result:
                    logger.warning(f"获取验证码失败，第 {attempt + 1} 次尝试")
                    continue
                
                image_base64, code_key = captcha_result
                
                # 识别验证码
                recognized_code = await self.recognize_captcha(image_base64, max_retries=1)
                
                if recognized_code:
                    logger.info(f"验证码识别成功: {recognized_code}, codeKey: {code_key}")
                    return recognized_code, code_key
                else:
                    logger.warning(f"验证码识别失败，第 {attempt + 1} 次尝试")
                    
            except Exception as e:
                logger.error(f"获取并识别验证码失败，第 {attempt + 1} 次尝试: {str(e)}")
        
        logger.error(f"获取并识别验证码失败，已重试 {max_retries} 次")
        return None


# 全局验证码服务实例
captcha_service = CaptchaService()
