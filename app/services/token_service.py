import logging
import json
import asyncio
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
import httpx
from jsonpath_ng import parse

from app.controllers.environment import environment_controller
from app.services.captcha_service import captcha_service

logger = logging.getLogger(__name__)


class TokenService:
    """Token自动获取服务"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()
    
    async def fetch_token(self, environment_id: int) -> Optional[str]:
        """
        获取指定环境的Token

        Args:
            environment_id: 环境ID

        Returns:
            获取到的Token字符串，失败返回None
        """
        try:
            # 获取环境配置
            env = await environment_controller.get(id=environment_id)

            if not env.token_url:
                logger.warning(f"环境 {env.name} 未配置Token获取URL")
                return None

            # 构建请求
            headers = env.token_headers or {}
            headers.setdefault('Content-Type', 'application/json')

            # 准备请求体
            request_body = env.token_body.copy() if env.token_body else {}

            # 初始化URL
            token_url = env.token_url

            # 检查是否需要验证码
            if env.enable_captcha and env.captcha_url:
                logger.info("🔐 环境配置了验证码，开始获取验证码...")
                logger.info(f"🔐 验证码地址: {env.captcha_url}")
                logger.info(f"🔐 验证码请求方式: {env.captcha_method or 'GET'}")

                # 获取并识别验证码
                captcha_result = await captcha_service.get_captcha_with_recognition(
                    captcha_url=env.captcha_url,
                    captcha_method=env.captcha_method or "GET",
                    captcha_headers=env.captcha_headers,
                    captcha_body=env.captcha_body,
                    captcha_image_path=env.captcha_image_path or "content.imageBase64",
                    captcha_key_path=env.captcha_key_path or "content.codeKey",
                    max_retries=3
                )

                if captcha_result:
                    recognized_code, code_key = captcha_result
                    logger.info(f"✅ 验证码获取成功: code={recognized_code}, codeKey={code_key}")

                    # 记录替换前的原始数据
                    logger.info(f"🔄 替换前的URL: {token_url}")
                    logger.info(f"🔄 替换前的请求体: {json.dumps(request_body, ensure_ascii=False, indent=2)}")

                    # 使用模板替换的方式将验证码信息添加到请求体中
                    request_body = self._replace_captcha_placeholders(
                        request_body, recognized_code, code_key
                    )
                    # 同时替换URL中的占位符
                    token_url = self._replace_url_placeholders(
                        token_url, recognized_code, code_key
                    )

                    logger.info(f"✅ 占位符替换完成")
                    logger.info(f"🔄 替换后的URL: {token_url}")
                    logger.info(f"🔄 替换后的请求体: {json.dumps(request_body, ensure_ascii=False, indent=2)}")
                else:
                    logger.error(f"❌ 验证码获取失败，无法继续获取Token")
                    return None
            else:
                logger.info("ℹ️ 环境未配置验证码，直接使用原始请求参数")
                logger.info(f"🔗 原始URL: {token_url}")
                logger.info(f"📦 原始请求体: {json.dumps(request_body, ensure_ascii=False, indent=2)}")

            # 记录详细的请求信息
            logger.info("=" * 80)
            logger.info(f"🚀 开始获取Token - 环境: {env.name} (ID: {environment_id})")
            logger.info("=" * 80)
            logger.info(f"📍 请求方式: {env.token_method}")
            logger.info(f"🔗 请求链接: {token_url}")
            logger.info(f"📋 请求头: {json.dumps(headers, ensure_ascii=False, indent=2)}")

            if request_body:
                logger.info(f"📦 请求体: {json.dumps(request_body, ensure_ascii=False, indent=2)}")
            else:
                logger.info("📦 请求体: 无")

            logger.info("-" * 80)

            # 发送请求
            response = await self.client.request(
                method=env.token_method,
                url=token_url,
                headers=headers,
                json=request_body if request_body else None
            )

            # 记录响应信息
            logger.info(f"📥 响应状态码: {response.status_code}")
            logger.info(f"📥 响应头: {dict(response.headers)}")

            if response.status_code != 200:
                logger.error(f"❌ 获取Token失败，状态码: {response.status_code}")
                logger.error(f"❌ 响应内容: {response.text}")
                logger.info("=" * 80)
                return None

            # 记录成功响应的内容
            response_text = response.text
            logger.info(f"✅ 响应内容: {response_text}")
            logger.info("-" * 80)
            
            # 解析响应
            response_data = response.json()
            logger.info(f"📊 解析后的响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")

            token = self._extract_token(response_data, env.token_field_name, env.token_field_path)

            if token:
                # 更新环境中的Token
                await environment_controller.update(
                    id=environment_id,
                    obj_in={
                        'token': token,
                        'last_token_refresh': datetime.now(timezone.utc)
                    }
                )
                logger.info(f"🎉 成功获取并更新环境 {env.name} 的Token")
                logger.info(f"🔑 获取到的Token: {token[:20]}..." if len(token) > 20 else f"🔑 获取到的Token: {token}")
                logger.info("=" * 80)
                return token
            else:
                logger.error(f"❌ 无法从响应中提取Token")
                logger.error(f"❌ Token字段名: {env.token_field_name}")
                logger.error(f"❌ Token路径: {env.token_field_path}")
                logger.error(f"❌ 响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                logger.info("=" * 80)
                return None
                
        except Exception as e:
            logger.error(f"❌ 获取Token时发生错误: {str(e)}")
            import traceback
            logger.error(f"❌ 错误堆栈: {traceback.format_exc()}")
            logger.info("=" * 80)
            return None
    
    def _extract_token(self, response_data: Dict[str, Any], field_name: str, field_path: Optional[str]) -> Optional[str]:
        """
        从响应数据中提取Token

        Args:
            response_data: 响应数据
            field_name: Token字段名称
            field_path: JSONPath表达式

        Returns:
            提取到的Token字符串
        """
        try:
            logger.info(f"开始提取Token - field_name: {field_name}, field_path: {field_path}")
            logger.info(f"响应数据类型: {type(response_data)}, 内容: {response_data}")

            if field_path and field_path.strip():
                # 使用JSONPath提取
                logger.info(f"使用JSONPath提取: {field_path}")
                jsonpath_expr = parse(field_path)
                matches = jsonpath_expr.find(response_data)
                logger.info(f"JSONPath匹配结果: {matches}")

                if matches:
                    token = str(matches[0].value)
                    logger.info(f"JSONPath提取成功，Token: {token}")
                    return token
                else:
                    logger.warning(f"JSONPath未找到匹配结果: {field_path}")
            else:
                logger.info(f"使用字段名提取: {field_name}")
                # 直接使用字段名提取
                if field_name in response_data:
                    token = str(response_data[field_name])
                    logger.info(f"直接字段提取成功，Token: {token}")
                    return token

                # 尝试从data字段中提取
                if 'data' in response_data and isinstance(response_data['data'], dict):
                    if field_name in response_data['data']:
                        token = str(response_data['data'][field_name])
                        logger.info(f"从data字段提取成功，Token: {token}")
                        return token

                # 尝试从content字段中提取
                if 'content' in response_data and isinstance(response_data['content'], dict):
                    if field_name in response_data['content']:
                        token = str(response_data['content'][field_name])
                        logger.info(f"从content字段提取成功，Token: {token}")
                        return token

            logger.warning(f"所有提取方法都失败了")
            return None

        except Exception as e:
            logger.error(f"提取Token时发生错误: {str(e)}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return None

    def _replace_captcha_placeholders(self, request_body: Dict[str, Any], recognized_code: str, code_key: str) -> Dict[str, Any]:
        """
        替换请求体中的验证码占位符

        Args:
            request_body: 原始请求体
            recognized_code: 识别出的验证码
            code_key: 验证码Key

        Returns:
            替换后的请求体
        """
        try:
            # 将请求体转换为JSON字符串进行替换
            request_body_str = json.dumps(request_body, ensure_ascii=False)
            logger.info(f"环境配置Token请求体原始内容: {request_body_str}")

            # 支持多种占位符格式
            placeholders = {
                '${code}': recognized_code,
                '${codekey}': code_key,
                '${codeKey}': code_key,  # 支持驼峰命名
                '${CODE}': recognized_code,  # 支持大写
                '${CODEKEY}': code_key,
                # 也支持不带$的格式
                '{code}': recognized_code,
                '{codekey}': code_key,
                '{codeKey}': code_key,
            }

            # 执行替换
            replaced_count = 0
            for placeholder, value in placeholders.items():
                if placeholder in request_body_str:
                    request_body_str = request_body_str.replace(placeholder, str(value))
                    logger.info(f"环境配置中替换占位符: {placeholder} -> {value}")
                    replaced_count += 1

            if replaced_count > 0:
                logger.info(f"环境配置中共替换了 {replaced_count} 个占位符")
                # 将替换后的字符串转换回字典
                updated_request_body = json.loads(request_body_str)
                logger.info(f"环境配置Token请求体替换后内容: {json.dumps(updated_request_body, ensure_ascii=False)}")
                return updated_request_body
            else:
                logger.info("环境配置中未发现占位符，使用原始请求体")
                return request_body

        except Exception as e:
            logger.error(f"替换环境配置中的验证码占位符时发生错误: {str(e)}")
            # 如果替换失败，回退到直接添加字段的方式
            logger.warning("占位符替换失败，回退到直接添加验证码字段的方式")
            request_body_copy = request_body.copy()
            request_body_copy['code'] = recognized_code
            request_body_copy['codeKey'] = code_key
            logger.info(f"回退方式添加验证码字段后的请求体: {json.dumps(request_body_copy, ensure_ascii=False)}")
            return request_body_copy

    def _replace_url_placeholders(self, url: str, recognized_code: str, code_key: str) -> str:
        """
        替换URL中的验证码占位符

        Args:
            url: 原始URL字符串
            recognized_code: 识别出的验证码
            code_key: 验证码Key

        Returns:
            替换后的URL字符串
        """
        try:
            logger.info(f"原始URL: {url}")

            # 支持多种占位符格式
            placeholders = {
                '${code}': recognized_code,
                '${codekey}': code_key,
                '${codeKey}': code_key,  # 支持驼峰命名
                '${CODE}': recognized_code,  # 支持大写
                '${CODEKEY}': code_key,
                # 也支持不带$的格式
                '{code}': recognized_code,
                '{codekey}': code_key,
                '{codeKey}': code_key,
            }

            # 执行替换
            updated_url = url
            replaced_count = 0
            for placeholder, value in placeholders.items():
                if placeholder in updated_url:
                    updated_url = updated_url.replace(placeholder, str(value))
                    replaced_count += 1
                    logger.info(f"替换URL占位符 {placeholder} -> {value}")

            if replaced_count > 0:
                logger.info(f"URL中共替换了 {replaced_count} 个占位符")
                logger.info(f"替换后的URL: {updated_url}")
            else:
                logger.info("URL中未发现占位符，使用原始URL")

            return updated_url

        except Exception as e:
            logger.error(f"替换URL中的验证码占位符时发生错误: {str(e)}")
            return url
    
    async def refresh_tokens_for_environments(self, environment_ids: list[int]) -> Dict[int, bool]:
        """
        批量刷新多个环境的Token
        
        Args:
            environment_ids: 环境ID列表
            
        Returns:
            环境ID到刷新结果的映射
        """
        results = {}
        
        for env_id in environment_ids:
            try:
                token = await self.fetch_token(env_id)
                results[env_id] = token is not None
            except Exception as e:
                logger.error(f"刷新环境 {env_id} Token失败: {str(e)}")
                results[env_id] = False
        
        return results
    
    async def get_environments_need_refresh(self) -> list[int]:
        """
        获取需要刷新Token的环境列表
        
        Returns:
            需要刷新Token的环境ID列表
        """
        try:
            # 获取所有启用自动刷新Token的环境
            environments = await environment_controller.model.filter(
                auto_refresh_token=True,
                is_active=True,
                token_url__isnull=False
            ).all()
            
            need_refresh = []
            now = datetime.now(timezone.utc)

            for env in environments:
                # 检查是否需要刷新
                if not env.last_token_refresh:
                    # 从未刷新过，需要刷新
                    need_refresh.append(env.id)
                    logger.info(f"环境 {env.id}({env.name}) 从未刷新过Token，添加到刷新列表")
                else:
                    # 确保last_token_refresh是UTC时间
                    last_refresh = env.last_token_refresh
                    if last_refresh.tzinfo is None:
                        # 如果是naive datetime，假设它是UTC时间
                        last_refresh = last_refresh.replace(tzinfo=timezone.utc)

                    # 检查是否超过刷新间隔
                    next_refresh_time = last_refresh + timedelta(seconds=env.token_refresh_interval)
                    logger.info(f"环境 {env.id}({env.name}) - 上次刷新: {last_refresh}, 下次刷新: {next_refresh_time}, 当前时间: {now}")

                    if now >= next_refresh_time:
                        need_refresh.append(env.id)
                        logger.info(f"环境 {env.id}({env.name}) 需要刷新Token")
                    else:
                        remaining_seconds = (next_refresh_time - now).total_seconds()
                        logger.debug(f"环境 {env.id}({env.name}) 还有 {remaining_seconds:.0f} 秒后需要刷新")
            
            return need_refresh
            
        except Exception as e:
            logger.error(f"获取需要刷新Token的环境列表失败: {str(e)}")
            return []


# 全局Token服务实例
token_service = TokenService()
