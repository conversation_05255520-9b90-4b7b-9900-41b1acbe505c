import time
import json
import httpx
from typing import Optional, Dict, Any
from tortoise.expressions import Q
from app.core.crud import CRUDBase
from app.models.admin import AIModelConfig
from app.models.enums import AIModelStatus, AIModelType
from app.schemas.ai_model_config import AIModelConfigCreate, AIModelConfigUpdate


class AIModelConfigController(CRUDBase[AIModelConfig, AIModelConfigCreate, AIModelConfigUpdate]):
    def __init__(self):
        super().__init__(model=AIModelConfig)

    async def create_ai_model_config(self, obj_in: AIModelConfigCreate, user_id: int) -> AIModelConfig:
        """创建AI模型配置"""
        # 如果设置为默认模型，先取消其他默认模型
        if obj_in.is_default:
            await self.model.filter(is_default=True).update(is_default=False)
        
        # 创建配置
        obj_data = obj_in.model_dump()
        obj_data["user_id"] = user_id
        obj = await self.model.create(**obj_data)
        return obj

    async def update_ai_model_config(self, id: int, obj_in: AIModelConfigUpdate) -> AIModelConfig:
        """更新AI模型配置"""
        obj = await self.get(id=id)
        
        # 如果设置为默认模型，先取消其他默认模型
        if obj_in.is_default:
            await self.model.filter(is_default=True).exclude(id=id).update(is_default=False)
        
        # 更新配置
        update_data = obj_in.model_dump(exclude_unset=True, exclude={"id"})
        await obj.update_from_dict(update_data)
        await obj.save()
        return obj

    async def get_default_config(self) -> Optional[AIModelConfig]:
        """获取默认AI模型配置"""
        return await self.model.filter(is_default=True, status=AIModelStatus.ACTIVE).first()

    async def get_configs_by_type(self, model_type: AIModelType) -> list[AIModelConfig]:
        """根据模型类型获取配置"""
        return await self.model.filter(model_type=model_type, status=AIModelStatus.ACTIVE).all()

    async def test_model_connection(self, config_id: int, test_message: str = "你好啊") -> Dict[str, Any]:
        """测试AI模型连接"""
        config = await self.get(id=config_id)
        
        start_time = time.time()
        try:
            response = await self._call_ai_model(config, test_message)
            response_time = time.time() - start_time
            
            return {
                "success": True,
                "response": response,
                "error": None,
                "response_time": round(response_time, 2)
            }
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "success": False,
                "response": None,
                "error": str(e),
                "response_time": round(response_time, 2)
            }

    async def call_ai_model(self, config: AIModelConfig, message: str) -> str:
        """调用AI模型API（公开方法）"""
        return await self._call_ai_model(config, message)

    async def _call_ai_model(self, config: AIModelConfig, message: str) -> str:
        """调用AI模型API"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.api_key}"
        }
        
        # 根据不同模型类型构建请求
        if config.model_type == AIModelType.OPENAI:
            return await self._call_openai_api(config, message, headers)
        elif config.model_type == AIModelType.OPENROUTER:
            return await self._call_openrouter_api(config, message, headers)
        elif config.model_type == AIModelType.VOLCENGINE:
            return await self._call_volcengine_api(config, message, headers)
        elif config.model_type == AIModelType.CLAUDE:
            return await self._call_claude_api(config, message, headers)
        elif config.model_type == AIModelType.QWEN:
            return await self._call_qwen_api(config, message, headers)
        elif config.model_type == AIModelType.ZHIPU:
            return await self._call_zhipu_api(config, message, headers)
        else:
            return await self._call_generic_api(config, message, headers)

    async def _call_openai_api(self, config: AIModelConfig, message: str, headers: Dict[str, str]) -> str:
        """调用OpenAI API"""
        url = config.api_url or "https://api.openai.com/v1/chat/completions"
        model_name = config.model_name or "gpt-3.5-turbo"

        # 检查是否是OpenRouter
        if "openrouter.ai" in url:
            # 为OpenRouter添加特殊头部
            headers.update({
                "HTTP-Referer": "https://vue-fastapi-admin.com",  # 可以配置为实际网站
                "X-Title": "Vue FastAPI Admin"  # 可以配置为实际应用名称
            })

        data = {
            "model": model_name,
            "messages": [{"role": "user", "content": message}],
            "max_tokens": config.max_tokens,
            "temperature": config.temperature
        }

        async with httpx.AsyncClient(timeout=config.timeout) as client:
            response = await client.post(url, headers=headers, json=data)
            response.raise_for_status()

            # 检查响应内容类型
            content_type = response.headers.get("content-type", "")
            if "application/json" not in content_type:
                raise ValueError(f"API返回非JSON响应，Content-Type: {content_type}, 响应内容: {response.text[:200]}")

            result = response.json()

            # 检查响应格式
            if "choices" not in result or not result["choices"]:
                raise ValueError(f"API响应格式错误: {result}")

            return result["choices"][0]["message"]["content"]

    async def _call_openrouter_api(self, config: AIModelConfig, message: str, headers: Dict[str, str]) -> str:
        """调用OpenRouter API"""
        # 确保URL包含完整的路径
        base_url = config.api_url or "https://openrouter.ai/api/v1"
        if not base_url.endswith("/chat/completions"):
            url = f"{base_url.rstrip('/')}/chat/completions"
        else:
            url = base_url
        model_name = config.model_name or "deepseek/deepseek-chat:free"

        # OpenRouter特殊头部 - 修复头部格式
        headers.update({
            "HTTP-Referer": "https://vue-fastapi-admin.com",
            "X-Title": "Vue FastAPI Admin",
            "Content-Type": "application/json"
        })

        data = {
            "model": model_name,
            "messages": [{"role": "user", "content": message}],
            "max_tokens": config.max_tokens,
            "temperature": config.temperature
        }

        # 如果有额外配置，合并到请求中
        if config.config_json:
            data.update(config.config_json)

        async with httpx.AsyncClient(timeout=config.timeout, follow_redirects=False) as client:
            try:
                response = await client.post(url, headers=headers, json=data)

                # 打印调试信息
                print(f"OpenRouter请求URL: {url}")
                print(f"OpenRouter请求头: {headers}")
                print(f"OpenRouter请求数据: {data}")
                print(f"OpenRouter响应状态: {response.status_code}")
                print(f"OpenRouter响应头: {dict(response.headers)}")
                print(f"OpenRouter响应内容: {response.text[:500]}")

                # 检查是否被重定向
                if response.status_code in [301, 302, 303, 307, 308]:
                    raise ValueError(f"请求被重定向到: {response.headers.get('location', '未知位置')}")

                response.raise_for_status()

                # 检查响应内容
                if not response.text.strip():
                    raise ValueError("API返回空响应")

                # 检查响应内容类型
                content_type = response.headers.get("content-type", "")
                if "application/json" not in content_type:
                    # 如果返回HTML，可能是API密钥无效或URL错误
                    if "text/html" in content_type:
                        raise ValueError(f"API返回HTML页面，可能是API密钥无效或URL错误。请检查API密钥和URL是否正确。响应内容: {response.text[:200]}")
                    else:
                        raise ValueError(f"API返回非JSON响应，Content-Type: {content_type}, 响应内容: {response.text[:200]}")

                result = response.json()

                # 检查响应格式
                if "choices" not in result or not result["choices"]:
                    raise ValueError(f"API响应格式错误: {result}")

                return result["choices"][0]["message"]["content"]

            except httpx.HTTPStatusError as e:
                error_detail = f"HTTP {e.response.status_code}: {e.response.text[:200]}"
                raise ValueError(f"OpenRouter API请求失败: {error_detail}")
            except json.JSONDecodeError as e:
                raise ValueError(f"OpenRouter API响应JSON解析失败: {str(e)}, 响应内容: {response.text[:200]}")
            except Exception as e:
                raise ValueError(f"OpenRouter API调用异常: {str(e)}")

    async def _call_volcengine_api(self, config: AIModelConfig, message: str, headers: Dict[str, str]) -> str:
        """调用火山引擎API"""
        # 火山引擎的API URL格式通常是 https://ark.cn-beijing.volces.com/api/v3/chat/completions
        base_url = config.api_url or "https://ark.cn-beijing.volces.com/api/v3"
        if not base_url.endswith("/chat/completions"):
            url = f"{base_url.rstrip('/')}/chat/completions"
        else:
            url = base_url

        model_name = config.model_name or "ep-20250326161223-fzfkk"

        # 火山引擎使用标准的OpenAI格式
        headers.update({
            "Content-Type": "application/json"
        })

        data = {
            "model": model_name,
            "messages": [{"role": "user", "content": message}],
            "max_tokens": config.max_tokens,
            "temperature": config.temperature
        }

        # 如果有额外配置，合并到请求中
        if config.config_json:
            data.update(config.config_json)

        async with httpx.AsyncClient(timeout=config.timeout) as client:
            try:
                response = await client.post(url, headers=headers, json=data)

                # 打印调试信息
                print(f"火山引擎请求URL: {url}")
                print(f"火山引擎请求头: {headers}")
                print(f"火山引擎请求数据: {data}")
                print(f"火山引擎响应状态: {response.status_code}")
                print(f"火山引擎响应头: {dict(response.headers)}")
                print(f"火山引擎响应内容: {response.text[:500]}")

                response.raise_for_status()

                # 检查响应内容类型
                content_type = response.headers.get("content-type", "")
                if "application/json" not in content_type:
                    raise ValueError(f"API返回非JSON响应，Content-Type: {content_type}, 响应内容: {response.text[:200]}")

                result = response.json()

                # 检查响应格式
                if "choices" not in result or not result["choices"]:
                    raise ValueError(f"API响应格式错误: {result}")

                return result["choices"][0]["message"]["content"]

            except httpx.HTTPStatusError as e:
                error_detail = f"HTTP {e.response.status_code}: {e.response.text[:200]}"
                raise ValueError(f"火山引擎API请求失败: {error_detail}")
            except json.JSONDecodeError as e:
                raise ValueError(f"火山引擎API响应JSON解析失败: {str(e)}, 响应内容: {response.text[:200]}")
            except Exception as e:
                raise ValueError(f"火山引擎API调用异常: {str(e)}")

    async def _call_claude_api(self, config: AIModelConfig, message: str, headers: Dict[str, str]) -> str:
        """调用Claude API"""
        url = config.api_url or "https://api.anthropic.com/v1/messages"
        model_name = config.model_name or "claude-3-sonnet-20240229"
        
        headers["anthropic-version"] = "2023-06-01"
        
        data = {
            "model": model_name,
            "max_tokens": config.max_tokens,
            "messages": [{"role": "user", "content": message}],
            "temperature": config.temperature
        }
        
        async with httpx.AsyncClient(timeout=config.timeout) as client:
            response = await client.post(url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            return result["content"][0]["text"]

    async def _call_qwen_api(self, config: AIModelConfig, message: str, headers: Dict[str, str]) -> str:
        """调用通义千问API"""
        url = config.api_url or "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        model_name = config.model_name or "qwen-turbo"
        
        headers["Authorization"] = f"Bearer {config.api_key}"
        
        data = {
            "model": model_name,
            "input": {"messages": [{"role": "user", "content": message}]},
            "parameters": {
                "max_tokens": config.max_tokens,
                "temperature": config.temperature
            }
        }
        
        async with httpx.AsyncClient(timeout=config.timeout) as client:
            response = await client.post(url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            return result["output"]["choices"][0]["message"]["content"]

    async def _call_zhipu_api(self, config: AIModelConfig, message: str, headers: Dict[str, str]) -> str:
        """调用智谱AI API"""
        url = config.api_url or "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        model_name = config.model_name or "glm-4"
        
        data = {
            "model": model_name,
            "messages": [{"role": "user", "content": message}],
            "max_tokens": config.max_tokens,
            "temperature": config.temperature
        }
        
        async with httpx.AsyncClient(timeout=config.timeout) as client:
            response = await client.post(url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]

    async def _call_generic_api(self, config: AIModelConfig, message: str, headers: Dict[str, str]) -> str:
        """调用通用API"""
        if not config.api_url:
            raise ValueError("自定义模型需要提供API地址")
        
        data = {
            "model": config.model_name or "default",
            "messages": [{"role": "user", "content": message}],
            "max_tokens": config.max_tokens,
            "temperature": config.temperature
        }
        
        # 如果有额外配置，合并到请求中
        if config.config_json:
            data.update(config.config_json)
        
        async with httpx.AsyncClient(timeout=config.timeout) as client:
            response = await client.post(config.api_url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            
            # 尝试从常见的响应格式中提取内容
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0].get("message", {}).get("content", "")
            elif "content" in result:
                return result["content"]
            elif "response" in result:
                return result["response"]
            else:
                return str(result)


ai_model_config_controller = AIModelConfigController()
