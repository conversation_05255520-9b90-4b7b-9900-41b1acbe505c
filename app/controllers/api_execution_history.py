from typing import List
from tortoise.expressions import Q
from app.core.crud import CRUDBase
from app.models.admin import ApiExecutionHistory
from app.schemas.api_execution_history import ApiExecutionHistoryCreate, ApiExecutionHistoryUpdate


class ApiExecutionHistoryController(CRUDBase[ApiExecutionHistory, ApiExecutionHistoryCreate, ApiExecutionHistoryUpdate]):
    def __init__(self):
        super().__init__(model=ApiExecutionHistory)

    async def get_user_recent_history(self, user_id: int, limit: int = 20) -> List[ApiExecutionHistory]:
        """获取用户最近的执行历史记录"""
        return await self.model.filter(user_id=user_id).order_by('-created_at').limit(limit)

    async def create_execution_record(self, record_data: ApiExecutionHistoryCreate) -> ApiExecutionHistory:
        """创建执行记录"""
        return await self.create(obj_in=record_data)


api_execution_history_controller = ApiExecutionHistoryController()
