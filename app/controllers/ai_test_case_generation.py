import json
import logging
import time
import uuid
from typing import List, Dict, Any, Optional
from tortoise.expressions import Q
from tortoise.transactions import in_transaction

from app.core.crud import CRUDBase
from app.models.admin import (
    AITestCaseGeneration,
    TestCase,
    Project,
    ProjectModule,
    PromptTemplate,
    AIModelConfig
)
from app.schemas.ai_test_case_generation import (
    AITestCaseGenerationCreate,
    AITestCaseGenerationUpdate,
    AITestCaseGenerationRequest,
    AITestCaseGenerationResult,
    GeneratedTestCase,
    SaveGeneratedTestCases
)
from app.controllers.prompt_template import prompt_template_controller
from app.controllers.ai_model_config import ai_model_config_controller
from app.controllers.test_case import TestCaseController
from app.schemas.test_case import TestCaseCreate

logger = logging.getLogger(__name__)


class AITestCaseGenerationController(CRUDBase[AITestCaseGeneration, AITestCaseGenerationCreate, AITestCaseGenerationUpdate]):
    def __init__(self):
        super().__init__(model=AITestCaseGeneration)
        self.test_case_controller = TestCaseController()

    async def generate_test_cases(self, request: AITestCaseGenerationRequest, user_id: int) -> AITestCaseGenerationResult:
        """生成功能测试用例"""
        start_time = time.time()
        
        # 获取AI模型配置
        if request.ai_model_config_id:
            ai_config = await ai_model_config_controller.get(id=request.ai_model_config_id)
            ai_model_config_id = request.ai_model_config_id
        else:
            ai_config = await ai_model_config_controller.get_default_config()
            if not ai_config:
                raise ValueError("未找到可用的AI模型配置，请先配置AI模型")
            ai_model_config_id = ai_config.id

        # 创建生成任务记录
        task_data = {
            "task_name": request.task_name,
            "requirement_description": request.requirement_description,
            "prompt_template_id": request.prompt_template_id,
            "ai_model_config_id": ai_model_config_id,
            "project_id": request.project_id,
            "module_id": request.module_id,
            "status": "generating",
            "user_id": user_id
        }
        
        task = await self.model.create(**task_data)
        
        try:
            # 获取项目信息
            project = await Project.get(id=request.project_id)
            project_name = project.name
            
            # 获取模块信息（如果有）
            module_name = None
            if request.module_id:
                module = await ProjectModule.get(id=request.module_id)
                module_name = module.name
            
            # 获取提示词模板
            if request.prompt_template_id:
                template = await prompt_template_controller.get(id=request.prompt_template_id)
            else:
                template = await prompt_template_controller.get_default_template("functional_test_case")
                if not template:
                    raise ValueError("未找到可用的功能测试用例生成提示词模板")
            
            # AI模型配置已经在前面获取了，直接使用
            
            # 准备模板变量
            template_variables = {
                "requirement_description": request.requirement_description,
                "project_name": project_name,
                "module_name": module_name,
                "generate_count": request.generate_count
            }
            
            # 渲染提示词
            rendered_prompt = prompt_template_controller.render_prompt(
                template.prompt_content, 
                template_variables
            )
            
            # 调用AI模型生成测试用例
            ai_response = await ai_model_config_controller.call_ai_model(ai_config, rendered_prompt)
            
            # 解析AI响应
            generated_cases = self._parse_ai_response(ai_response)
            
            generation_time = time.time() - start_time
            
            # 更新任务状态，保存生成的测试用例内容
            await task.update_from_dict({
                "status": "completed",
                "generated_count": len(generated_cases),
                "generated_cases": [case.model_dump() for case in generated_cases],
                "generation_time": int(generation_time)
            })
            await task.save()
            
            # 增加模板使用次数
            await prompt_template_controller.increment_usage_count(template.id)
            
            return AITestCaseGenerationResult(
                success=True,
                task_id=task.id,
                generated_cases=generated_cases,
                saved_count=0,  # 还未保存
                generation_time=generation_time
            )
            
        except Exception as e:
            generation_time = time.time() - start_time
            error_message = str(e)
            
            # 更新任务状态为失败
            await task.update_from_dict({
                "status": "failed",
                "error_message": error_message,
                "generation_time": int(generation_time)
            })
            await task.save()
            
            logger.error(f"生成测试用例失败: {error_message}")
            
            return AITestCaseGenerationResult(
                success=False,
                task_id=task.id,
                generated_cases=[],
                saved_count=0,
                error_message=error_message,
                generation_time=generation_time
            )

    def _parse_ai_response(self, ai_response: str) -> List[GeneratedTestCase]:
        """解析AI响应，提取测试用例"""
        try:
            # 尝试从响应中提取JSON部分
            response = ai_response.strip()
            
            # 查找JSON代码块
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                else:
                    json_str = response[start:].strip()
            elif response.startswith("[") and response.endswith("]"):
                json_str = response
            else:
                # 尝试查找数组开始和结束
                start = response.find("[")
                end = response.rfind("]")
                if start != -1 and end != -1:
                    json_str = response[start:end+1]
                else:
                    raise ValueError("无法从AI响应中提取有效的JSON格式")
            
            # 解析JSON
            cases_data = json.loads(json_str)
            
            if not isinstance(cases_data, list):
                raise ValueError("AI响应格式错误：期望JSON数组")
            
            generated_cases = []
            for case_data in cases_data:
                if not isinstance(case_data, dict):
                    continue
                
                # 验证必需字段
                required_fields = ["case_name", "test_steps", "expected_result"]
                if not all(field in case_data for field in required_fields):
                    continue
                
                # 创建测试用例对象，添加临时唯一ID
                test_case = GeneratedTestCase(
                    id=str(uuid.uuid4()),  # 生成临时唯一ID
                    case_name=case_data.get("case_name", ""),
                    case_level=case_data.get("case_level", "medium"),
                    precondition=case_data.get("precondition"),
                    test_steps=case_data.get("test_steps", ""),
                    expected_result=case_data.get("expected_result", ""),
                    is_smoke=case_data.get("is_smoke", False)
                )
                generated_cases.append(test_case)
            
            return generated_cases
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}, 响应内容: {ai_response}")
            raise ValueError(f"AI响应JSON格式错误: {str(e)}")
        except Exception as e:
            logger.error(f"解析AI响应失败: {str(e)}")
            raise ValueError(f"解析AI响应失败: {str(e)}")

    async def save_generated_test_cases(self, save_request: SaveGeneratedTestCases, user_id: int) -> int:
        """保存生成的测试用例到数据库"""
        saved_count = 0
        
        # 获取任务信息
        task = await self.get(id=save_request.task_id)
        
        async with in_transaction():
            for case in save_request.cases:
                try:
                    # 创建测试用例
                    case_create = TestCaseCreate(
                        case_name=case.case_name,
                        case_level=case.case_level,
                        precondition=case.precondition,
                        test_steps=case.test_steps,
                        expected_result=case.expected_result,
                        is_smoke=case.is_smoke,
                        status="pending",  # 待审核
                        source="ai",  # AI生成
                        project_id=task.project_id,
                        module_id=task.module_id
                    )

                    await self.test_case_controller.create_with_case_number(
                        obj_in=case_create,
                        user_id=user_id
                    )
                    saved_count += 1
                    
                except Exception as e:
                    logger.error(f"保存测试用例失败: {case.case_name}, 错误: {str(e)}")
                    continue
        
        return saved_count

    async def get_generation_history(
        self,
        user_id: int = None,
        project_id: int = None,
        page: int = 1,
        page_size: int = 10
    ) -> tuple[int, List[Dict[str, Any]]]:
        """获取生成历史，包含创建人信息"""
        q = Q()
        if user_id:
            q &= Q(user_id=user_id)
        if project_id:
            q &= Q(project_id=project_id)

        total, generation_objs = await self.list(
            page=page,
            page_size=page_size,
            search=q,
            order=["-created_at"]
        )

        # 转换为字典并添加创建人信息
        result_list = []
        for obj in generation_objs:
            obj_dict = await obj.to_dict()

            # 获取创建人信息
            try:
                from app.models.admin import User
                user = await User.get(id=obj.user_id)
                obj_dict["creator_name"] = user.username
            except:
                obj_dict["creator_name"] = "未知用户"

            result_list.append(obj_dict)

        return total, result_list

    async def get_task_detail(self, task_id: int) -> Dict[str, Any]:
        """获取任务详情，包含项目、模块和创建人信息"""
        task = await self.get(id=task_id)
        task_dict = await task.to_dict()

        # 获取项目名称
        try:
            project = await Project.get(id=task.project_id)
            task_dict["project_name"] = project.name
        except:
            task_dict["project_name"] = "未知项目"

        # 获取模块名称
        if task.module_id:
            try:
                module = await ProjectModule.get(id=task.module_id)
                task_dict["module_name"] = module.name
            except:
                task_dict["module_name"] = "未知模块"
        else:
            task_dict["module_name"] = None

        # 获取创建人信息
        try:
            from app.models.admin import User
            user = await User.get(id=task.user_id)
            task_dict["creator_name"] = user.username
        except:
            task_dict["creator_name"] = "未知用户"

        return task_dict


# 创建全局实例
ai_test_case_generation_controller = AITestCaseGenerationController()
