import logging
from datetime import datetime
from typing import List, <PERSON>ple, Optional, Dict, Any
from tortoise.expressions import Q
from tortoise.transactions import in_transaction

from app.core.crud import CRUDBase
from app.models.admin import FunctionalTestPlan, FunctionalTestPlanCase, TestCase, User, Project
from app.schemas.functional_test_plan import FunctionalTestPlanCreate, FunctionalTestPlanUpdate

logger = logging.getLogger(__name__)


class FunctionalTestPlanController(CRUDBase[FunctionalTestPlan, FunctionalTestPlanCreate, FunctionalTestPlanUpdate]):
    def __init__(self):
        super().__init__(model=FunctionalTestPlan)

    async def create_with_user(self, obj_in: FunctionalTestPlanCreate, user_id: int) -> FunctionalTestPlan:
        """创建功能测试计划并关联用户"""
        obj_data = obj_in.dict()
        obj_data['user_id'] = user_id
        return await self.model.create(**obj_data)

    async def list_by_project_with_user_info(
        self, 
        project_id: int, 
        page: int = 1, 
        page_size: int = 10, 
        search: Q = None
    ) -> Tuple[int, List[Dict[str, Any]]]:
        """根据项目ID获取功能测试计划列表，包含用户信息"""
        query = self.model.filter(project_id=project_id)
        if search:
            query = query.filter(search)
        
        total = await query.count()
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 获取分页数据
        plans = await query.offset(offset).limit(page_size).order_by('-created_at')
        
        # 转换为字典并添加用户信息
        result = []
        for plan in plans:
            plan_dict = await plan.to_dict()
            
            # 获取创建人信息
            try:
                user = await User.get(id=plan.user_id)
                plan_dict['creator_name'] = user.alias or user.username
            except:
                plan_dict['creator_name'] = '未知用户'
            
            # 获取项目信息
            try:
                project = await Project.get(id=plan.project_id)
                plan_dict['project_name'] = project.name
            except:
                plan_dict['project_name'] = '未知项目'
            
            result.append(plan_dict)
        
        return total, result

    async def list_with_user_info(
        self, 
        page: int = 1, 
        page_size: int = 10, 
        search: Q = None
    ) -> Tuple[int, List[Dict[str, Any]]]:
        """获取所有功能测试计划列表，包含用户信息"""
        query = self.model.all()
        if search:
            query = query.filter(search)
        
        total = await query.count()
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 获取分页数据
        plans = await query.offset(offset).limit(page_size).order_by('-created_at')
        
        # 转换为字典并添加用户信息
        result = []
        for plan in plans:
            plan_dict = await plan.to_dict()
            
            # 获取创建人信息
            try:
                user = await User.get(id=plan.user_id)
                plan_dict['creator_name'] = user.alias or user.username
            except:
                plan_dict['creator_name'] = '未知用户'
            
            # 获取项目信息
            try:
                project = await Project.get(id=plan.project_id)
                plan_dict['project_name'] = project.name
            except:
                plan_dict['project_name'] = '未知项目'
            
            result.append(plan_dict)
        
        return total, result

    async def copy_plan(self, plan_id: int, user_id: int) -> FunctionalTestPlan:
        """复制功能测试计划"""
        async with in_transaction():
            # 获取原计划
            original_plan = await self.get(id=plan_id)
            
            # 创建新计划
            new_plan_data = {
                'plan_name': f"{original_plan.plan_name}_副本",
                'level': original_plan.level,
                'status': 'not_started',
                'description': original_plan.description,
                'project_id': original_plan.project_id,
                'user_id': user_id
            }
            new_plan = await self.model.create(**new_plan_data)
            
            # 复制关联的测试用例
            original_cases = await FunctionalTestPlanCase.filter(plan_id=plan_id).order_by('execution_order')
            for case in original_cases:
                await FunctionalTestPlanCase.create(
                    plan_id=new_plan.id,
                    case_id=case.case_id,
                    execution_order=case.execution_order
                )
            
            return new_plan

    async def get_plan_cases(self, plan_id: int) -> List[Dict[str, Any]]:
        """获取测试计划关联的测试用例"""
        # 获取计划关联的用例
        plan_cases = await FunctionalTestPlanCase.filter(plan_id=plan_id).order_by('execution_order')
        
        result = []
        for plan_case in plan_cases:
            try:
                # 获取测试用例详情
                test_case = await TestCase.get(id=plan_case.case_id)
                case_dict = await test_case.to_dict()
                
                # 添加关联信息
                case_dict['plan_case_id'] = plan_case.id
                case_dict['execution_order'] = plan_case.execution_order
                case_dict['execution_status'] = plan_case.execution_status
                
                # 获取创建人信息
                if test_case.user_id:
                    try:
                        user = await User.get(id=test_case.user_id)
                        case_dict['creator_name'] = user.alias or user.username
                    except:
                        case_dict['creator_name'] = '未知用户'
                else:
                    case_dict['creator_name'] = '未知用户'
                
                result.append(case_dict)
            except Exception as e:
                logger.error(f"获取测试用例失败: {e}")
                continue
        
        return result

    async def add_cases_to_plan(self, plan_id: int, case_ids: List[int]) -> None:
        """添加测试用例到测试计划"""
        async with in_transaction():
            # 获取当前最大执行顺序
            max_order = await FunctionalTestPlanCase.filter(plan_id=plan_id).count()

            for i, case_id in enumerate(case_ids):
                # 检查是否已存在
                exists = await FunctionalTestPlanCase.filter(plan_id=plan_id, case_id=case_id).exists()
                if not exists:
                    await FunctionalTestPlanCase.create(
                        plan_id=plan_id,
                        case_id=case_id,
                        execution_order=max_order + i + 1,
                        execution_status='passed'  # 新增用例时状态默认为"通过"
                    )

    async def remove_cases_from_plan(self, plan_id: int, case_ids: List[int]) -> None:
        """从测试计划中移除测试用例"""
        async with in_transaction():
            await FunctionalTestPlanCase.filter(plan_id=plan_id, case_id__in=case_ids).delete()
            
            # 重新排序剩余用例
            remaining_cases = await FunctionalTestPlanCase.filter(plan_id=plan_id).order_by('execution_order')
            for i, case in enumerate(remaining_cases):
                case.execution_order = i + 1
                await case.save()

    async def update_case_order(self, plan_id: int, case_orders: List[Dict[str, int]]) -> None:
        """更新测试用例执行顺序"""
        async with in_transaction():
            for order_data in case_orders:
                case_id = order_data['case_id']
                execution_order = order_data['execution_order']

                await FunctionalTestPlanCase.filter(
                    plan_id=plan_id,
                    case_id=case_id
                ).update(execution_order=execution_order)

    async def update_case_status(self, plan_id: int, case_id: int, execution_status: str) -> None:
        """更新测试用例执行状态"""
        await FunctionalTestPlanCase.filter(
            plan_id=plan_id,
            case_id=case_id
        ).update(execution_status=execution_status)

    async def get_approved_test_cases(self, project_id: int, plan_id: int = None) -> List[Dict[str, Any]]:
        """获取项目下已审核的功能测试用例，可选择过滤已添加到指定计划的用例"""
        query = TestCase.filter(project_id=project_id, status='approved')

        # 如果指定了计划ID，则过滤掉已添加到该计划的用例
        if plan_id:
            added_case_ids = await FunctionalTestPlanCase.filter(plan_id=plan_id).values_list('case_id', flat=True)
            if added_case_ids:
                query = query.exclude(id__in=added_case_ids)

        test_cases = await query.order_by('-created_at')

        result = []
        for test_case in test_cases:
            case_dict = await test_case.to_dict()

            # 获取创建人信息
            if test_case.user_id:
                try:
                    user = await User.get(id=test_case.user_id)
                    case_dict['creator_name'] = user.alias or user.username
                except:
                    case_dict['creator_name'] = '未知用户'
            else:
                case_dict['creator_name'] = '未知用户'

            result.append(case_dict)

        return result

    async def search_approved_test_cases(self, project_id: int, plan_id: int = None, case_name: str = None) -> List[Dict[str, Any]]:
        """搜索项目下已审核的功能测试用例"""
        query = TestCase.filter(project_id=project_id, status='approved')

        # 如果指定了计划ID，则过滤掉已添加到该计划的用例
        if plan_id:
            added_case_ids = await FunctionalTestPlanCase.filter(plan_id=plan_id).values_list('case_id', flat=True)
            if added_case_ids:
                query = query.exclude(id__in=added_case_ids)

        # 如果指定了用例名称，则进行模糊搜索
        if case_name:
            query = query.filter(case_name__icontains=case_name)

        test_cases = await query.order_by('-created_at')

        result = []
        for test_case in test_cases:
            case_dict = await test_case.to_dict()

            # 获取创建人信息
            if test_case.user_id:
                try:
                    user = await User.get(id=test_case.user_id)
                    case_dict['creator_name'] = user.alias or user.username
                except:
                    case_dict['creator_name'] = '未知用户'
            else:
                case_dict['creator_name'] = '未知用户'

            result.append(case_dict)

        return result

    async def get_statistics(self) -> List[Dict[str, Any]]:
        """获取功能测试计划统计数据，按项目分组"""
        try:
            # 获取所有项目
            projects = await Project.all()
            
            statistics = []
            for project in projects:
                # 统计该项目的测试计划数据
                total_plans = await FunctionalTestPlan.filter(project_id=project.id).count()
                not_started_plans = await FunctionalTestPlan.filter(project_id=project.id, status='not_started').count()
                in_progress_plans = await FunctionalTestPlan.filter(project_id=project.id, status='in_progress').count()
                completed_plans = await FunctionalTestPlan.filter(project_id=project.id, status='completed').count()
                
                statistics.append({
                    'project_id': project.id,
                    'project_name': project.name,
                    'total_plans': total_plans,
                    'not_started_plans': not_started_plans,
                    'in_progress_plans': in_progress_plans,
                    'completed_plans': completed_plans
                })
            
            return statistics
        except Exception as e:
            logger.error(f"获取统计数据失败: {str(e)}")
            return []


functional_test_plan_controller = FunctionalTestPlanController()
