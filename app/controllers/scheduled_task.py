import logging
from datetime import datetime
from typing import List, Tuple, Optional, Dict, Any
from tortoise.expressions import Q
from croniter import croniter

from app.core.crud import CRUDBase
from app.models.admin import ScheduledTask, ApiTestPlan, User
from app.schemas.scheduled_task import ScheduledTaskCreate, ScheduledTaskUpdate

logger = logging.getLogger(__name__)


class ScheduledTaskController(CRUDBase[ScheduledTask, ScheduledTaskCreate, ScheduledTaskUpdate]):
    def __init__(self):
        super().__init__(model=ScheduledTask)

    async def create_with_user(self, obj_in: ScheduledTaskCreate, user_id: int) -> ScheduledTask:
        """创建定时任务并设置创建人"""
        # 验证cron表达式
        if not self.validate_cron_expression(obj_in.cron_expression):
            raise ValueError("无效的Cron表达式")
        
        # 计算下次执行时间
        next_run_time = self.get_next_run_time(obj_in.cron_expression)
        
        obj_dict = obj_in.model_dump()
        obj_dict['creator_id'] = user_id
        obj_dict['next_run_time'] = next_run_time
        
        task = self.model(**obj_dict)
        await task.save()
        return task

    async def list_with_user_info(self, page: int, page_size: int, search: Q = Q()) -> Tuple[int, List[Dict[str, Any]]]:
        """获取定时任务列表并包含用户信息"""
        query = self.model.filter(search)
        total = await query.count()
        
        tasks = await query.offset((page - 1) * page_size).limit(page_size).order_by('-created_at')
        
        result = []
        for task in tasks:
            task_dict = await task.to_dict()
            
            # 获取创建人信息
            try:
                creator = await User.get(id=task.creator_id)
                task_dict['creator_name'] = creator.username
            except:
                task_dict['creator_name'] = '未知用户'
            
            # 获取测试计划信息
            try:
                plan = await ApiTestPlan.get(id=task.plan_id)
                task_dict['plan_name'] = plan.plan_name
            except:
                task_dict['plan_name'] = '未知计划'
            
            result.append(task_dict)
        
        return total, result

    async def list_by_plan(self, plan_id: int) -> List[Dict[str, Any]]:
        """获取指定测试计划的定时任务"""
        tasks = await self.model.filter(plan_id=plan_id).order_by('-created_at')
        
        result = []
        for task in tasks:
            task_dict = await task.to_dict()
            
            # 获取创建人信息
            try:
                creator = await User.get(id=task.creator_id)
                task_dict['creator_name'] = creator.username
            except:
                task_dict['creator_name'] = '未知用户'
            
            result.append(task_dict)
        
        return result

    async def update_task(self, task_id: int, obj_in: ScheduledTaskUpdate) -> ScheduledTask:
        """更新定时任务"""
        task = await self.get(id=task_id)
        
        update_data = obj_in.model_dump(exclude_unset=True)
        
        # 如果更新了cron表达式，需要重新计算下次执行时间
        if 'cron_expression' in update_data:
            if not self.validate_cron_expression(update_data['cron_expression']):
                raise ValueError("无效的Cron表达式")
            update_data['next_run_time'] = self.get_next_run_time(update_data['cron_expression'])
        
        await self.update(id=task_id, obj_in=update_data)
        return await self.get(id=task_id)

    async def toggle_task_status(self, task_id: int) -> ScheduledTask:
        """切换任务启用状态"""
        task = await self.get(id=task_id)
        new_status = not task.is_active
        
        update_data = {'is_active': new_status}
        if new_status:
            # 如果启用任务，重新计算下次执行时间
            update_data['next_run_time'] = self.get_next_run_time(task.cron_expression)
        else:
            # 如果禁用任务，清空下次执行时间
            update_data['next_run_time'] = None
        
        await self.update(id=task_id, obj_in=update_data)
        return await self.get(id=task_id)

    async def update_run_info(self, task_id: int, success: bool = True):
        """更新任务执行信息"""
        task = await self.get(id=task_id)
        
        update_data = {
            'last_run_time': datetime.now(),
            'run_count': task.run_count + 1
        }
        
        # 计算下次执行时间
        if task.is_active:
            update_data['next_run_time'] = self.get_next_run_time(task.cron_expression)
        
        await self.update(id=task_id, obj_in=update_data)

    @staticmethod
    def validate_cron_expression(cron_expr: str) -> bool:
        """验证cron表达式是否有效"""
        try:
            croniter(cron_expr)
            return True
        except Exception:
            return False

    @staticmethod
    def get_next_run_time(cron_expr: str) -> Optional[datetime]:
        """根据cron表达式计算下次执行时间"""
        try:
            cron = croniter(cron_expr, datetime.now())
            return cron.get_next(datetime)
        except Exception:
            return None

    async def get_active_tasks(self) -> List[ScheduledTask]:
        """获取所有启用的定时任务"""
        return await self.model.filter(is_active=True).all()

    async def get_statistics(self) -> Dict[str, Any]:
        """获取定时任务统计数据"""
        try:
            from app.models.admin import ApiTestPlan, Project

            # 基础统计
            total_tasks = await self.model.all().count()
            active_tasks = await self.model.filter(is_active=True).count()
            inactive_tasks = total_tasks - active_tasks

            # 执行统计
            all_tasks = await self.model.all()
            total_executions = sum(task.run_count for task in all_tasks)

            # 最近7天的任务执行情况
            from datetime import datetime, timedelta
            import pytz

            # 使用UTC时间避免时区问题
            utc_now = datetime.now(pytz.UTC)
            seven_days_ago = utc_now - timedelta(days=7)

            # 获取所有任务，然后在Python中过滤，避免数据库时区比较问题
            all_recent_tasks = await self.model.filter(
                last_run_time__isnull=False
            ).all()

            recent_tasks = []
            for task in all_recent_tasks:
                if task.last_run_time:
                    # 确保数据库时间也是UTC
                    task_time = task.last_run_time
                    if task_time.tzinfo is None:
                        # 如果是naive datetime，假设它是UTC
                        task_time = pytz.UTC.localize(task_time)
                    elif task_time.tzinfo != pytz.UTC:
                        # 转换为UTC
                        task_time = task_time.astimezone(pytz.UTC)

                    if task_time >= seven_days_ago:
                        recent_tasks.append(task)

            # 按项目分组的统计
            project_stats = []
            projects = await Project.all()

            for project in projects:
                # 获取该项目的测试计划
                project_plans = await ApiTestPlan.filter(project_id=project.id).all()
                plan_ids = [plan.id for plan in project_plans]

                if plan_ids:
                    # 该项目的定时任务
                    project_tasks = await self.model.filter(plan_id__in=plan_ids).all()
                    project_active_tasks = [task for task in project_tasks if task.is_active]

                    # 计算成功率（基于最近执行的任务）
                    success_count = 0
                    total_recent_executions = 0

                    for task in project_tasks:
                        if task.last_run_time:
                            # 处理时区问题
                            task_time = task.last_run_time
                            if task_time.tzinfo is None:
                                task_time = pytz.UTC.localize(task_time)
                            elif task_time.tzinfo != pytz.UTC:
                                task_time = task_time.astimezone(pytz.UTC)

                            if task_time >= seven_days_ago:
                                total_recent_executions += 1
                                # 这里简化处理，实际应该根据执行结果判断
                                # 暂时基于是否有执行时间来判断成功
                                success_count += 1

                    success_rate = (success_count / total_recent_executions * 100) if total_recent_executions > 0 else 0

                    project_stats.append({
                        'project_id': project.id,
                        'project_name': project.name,
                        'total_tasks': len(project_tasks),
                        'active_tasks': len(project_active_tasks),
                        'recent_executions': total_recent_executions,
                        'success_rate': round(success_rate, 2)
                    })

            # 最近执行的任务
            recent_executions = []
            recent_tasks_with_execution = await self.model.filter(
                last_run_time__isnull=False
            ).order_by('-last_run_time').limit(10)

            for task in recent_tasks_with_execution:
                try:
                    plan = await ApiTestPlan.get(id=task.plan_id)
                    project = await Project.get(id=plan.project_id)

                    recent_executions.append({
                        'task_id': task.id,
                        'task_name': task.task_name,
                        'plan_name': plan.plan_name,
                        'project_name': project.name,
                        'last_run_time': task.last_run_time.isoformat() if task.last_run_time else None,
                        'run_count': task.run_count
                    })
                except:
                    continue

            return {
                'overview': {
                    'total_tasks': total_tasks,
                    'active_tasks': active_tasks,
                    'inactive_tasks': inactive_tasks,
                    'total_executions': total_executions
                },
                'project_stats': project_stats,
                'recent_executions': recent_executions,
                'task_status_distribution': {
                    'active': active_tasks,
                    'inactive': inactive_tasks
                }
            }

        except Exception as e:
            logger.error(f"获取定时任务统计数据失败: {str(e)}")
            return {
                'overview': {
                    'total_tasks': 0,
                    'active_tasks': 0,
                    'inactive_tasks': 0,
                    'total_executions': 0
                },
                'project_stats': [],
                'recent_executions': [],
                'task_status_distribution': {
                    'active': 0,
                    'inactive': 0
                }
            }


# 创建控制器实例
scheduled_task_controller = ScheduledTaskController()
