import json
import logging
import time
from typing import List
from jinja2 import Template
from app.core.crud import CRUDBase
from app.schemas.api_import import (
    ApiImportCreate,
    ApiImportUpdate,
    ApiImportAIGenerateRequest,
    ApiImportAIGenerateResult,
    ApiTestCasePreview,
    SaveApiTestCasesRequest
)
from app.models.admin import ApiImport, Project, ApiTestCase
from app.controllers.prompt_template import prompt_template_controller
from app.controllers.ai_model_config import ai_model_config_controller
from app.controllers.api_test_case import api_test_case_controller

logger = logging.getLogger(__name__)


class ApiImportController(CRUDBase[ApiImport, ApiImportCreate, ApiImportUpdate]):
    def __init__(self):
        super().__init__(model=ApiImport)

    async def create_api_import(self, obj_in: ApiImportCreate) -> ApiImport:
        obj = await self.create(obj_in)
        return obj

    async def ai_generate_test_cases(self, request: ApiImportAIGenerateRequest, user_id: int) -> ApiImportAIGenerateResult:
        """使用AI生成接口测试用例"""
        try:
            # 获取API导入记录
            api_import = await self.get(id=request.api_import_id)

            # 获取项目信息
            project = await Project.get(id=api_import.project_id)

            # 获取AI模型配置
            if request.ai_model_config_id:
                ai_config = await ai_model_config_controller.get(id=request.ai_model_config_id)
            else:
                ai_config = await ai_model_config_controller.get_default_config()
                if not ai_config:
                    raise ValueError("未找到可用的AI模型配置")

            # 获取提示词模板
            if request.prompt_template_id:
                template = await prompt_template_controller.get(id=request.prompt_template_id)
            else:
                template = await prompt_template_controller.get_default_template("api_test_case")
                if not template:
                    raise ValueError("未找到可用的接口测试用例生成提示词模板")

            # 准备模板变量
            template_variables = {
                "api_name": api_import.api_name,
                "method": api_import.method,
                "url_path": api_import.url_path,
                "params_list": api_import.params_list or "无参数",
                "project_name": project.name,
                "generate_count": request.generate_count
            }

            # 渲染提示词
            jinja_template = Template(template.prompt_content)
            rendered_prompt = jinja_template.render(**template_variables)

            # 调用AI模型
            ai_response = await ai_model_config_controller.call_ai_model(ai_config, rendered_prompt)

            # 解析AI响应
            generated_cases = self._parse_ai_response(ai_response)

            # 更新模板使用次数
            await prompt_template_controller.increment_usage_count(template.id)

            return ApiImportAIGenerateResult(
                success=True,
                generated_cases=generated_cases,
                ai_response=ai_response
            )

        except Exception as e:
            logger.error(f"AI生成接口测试用例失败: {str(e)}")
            return ApiImportAIGenerateResult(
                success=False,
                generated_cases=[],
                error_message=str(e)
            )

    def _parse_ai_response(self, ai_response: str) -> List[ApiTestCasePreview]:
        """解析AI响应，提取测试用例"""
        try:
            # 尝试从响应中提取JSON
            import re

            # 查找JSON代码块
            json_match = re.search(r'```json\s*(.*?)\s*```', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 如果没有代码块，尝试直接解析整个响应
                json_str = ai_response.strip()

            # 解析JSON
            cases_data = json.loads(json_str)

            if not isinstance(cases_data, list):
                raise ValueError("AI响应格式错误：应该是JSON数组")

            generated_cases = []
            for case_data in cases_data:
                try:
                    case = ApiTestCasePreview(
                        case_name=case_data.get("case_name", ""),
                        method=case_data.get("method", "GET"),
                        url=case_data.get("url", ""),
                        params=case_data.get("params"),
                        body=case_data.get("body"),
                        expected_result=case_data.get("expected_result"),
                        is_smoke=case_data.get("is_smoke", False)
                    )
                    generated_cases.append(case)
                except Exception as e:
                    logger.warning(f"解析单个测试用例失败: {e}, 数据: {case_data}")
                    continue

            return generated_cases

        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            # 返回空列表而不是抛出异常
            return []

    async def save_generated_test_cases(self, request: SaveApiTestCasesRequest, user_id: int) -> int:
        """保存生成的接口测试用例"""
        try:
            # 获取API导入记录
            api_import = await self.get(id=request.api_import_id)

            saved_count = 0
            for case_preview in request.selected_cases:
                try:
                    # 创建接口测试用例数据
                    case_data = {
                        "case_name": case_preview.case_name,
                        "method": case_preview.method,
                        "url": case_preview.url,
                        "params": case_preview.params,
                        "body": case_preview.body,
                        "expected_result": case_preview.expected_result,
                        "is_smoke": case_preview.is_smoke,
                        "status": "pending",
                        "source": "ai",
                        "project_id": case_preview.project_id or api_import.project_id,  # 优先使用传入的项目ID
                        "module_id": case_preview.module_id,  # 使用传入的模块ID
                    }

                    # 使用接口测试用例控制器创建
                    from app.schemas.api_test_case import ApiTestCaseCreate
                    case_create = ApiTestCaseCreate(**case_data)
                    await api_test_case_controller.create_with_case_number(case_create, user_id)
                    saved_count += 1

                except Exception as e:
                    logger.error(f"保存测试用例失败: {e}, 用例数据: {case_preview}")
                    continue

            return saved_count

        except Exception as e:
            logger.error(f"保存生成的测试用例失败: {e}")
            raise


api_import_controller = ApiImportController()
