import json
import logging
from datetime import datetime
from typing import Dict, Any
import httpx
from app.core.crud import CRUDBase
from app.schemas.api_request import ApiRequestCreate, ApiRequestUpdate
from app.models.admin import ApiRequest

logger = logging.getLogger(__name__)


class ApiRequestController(CRUDBase[ApiRequest, ApiRequestCreate, ApiRequestUpdate]):
    def __init__(self):
        super().__init__(model=ApiRequest)

    async def create_api_request(self, obj_in: ApiRequestCreate) -> ApiRequest:
        obj = await self.create(obj_in)
        return obj

    async def execute_api_request(self, api_request_id: int) -> Dict[str, Any]:
        """执行API请求"""
        try:
            # 获取API请求记录
            api_request = await self.get(id=api_request_id)

            # 构建请求参数
            url = api_request.url
            method = api_request.method.upper()

            # 处理请求参数
            params = {}
            if api_request.params:
                if isinstance(api_request.params, str):
                    try:
                        params = json.loads(api_request.params)
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析params: {api_request.params}")
                elif isinstance(api_request.params, dict):
                    params = api_request.params

            # 处理请求头
            headers = {}
            if api_request.headers:
                if isinstance(api_request.headers, str):
                    try:
                        headers = json.loads(api_request.headers)
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析headers: {api_request.headers}")
                elif isinstance(api_request.headers, dict):
                    headers = api_request.headers

            # 处理请求体
            data = None
            if api_request.body and method in ['POST', 'PUT', 'PATCH']:
                try:
                    data = json.loads(api_request.body)
                except json.JSONDecodeError:
                    data = api_request.body

            # 记录开始时间
            start_time = datetime.now()

            # 发送HTTP请求
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.request(
                    method=method,
                    url=url,
                    params=params,
                    headers=headers,
                    json=data if isinstance(data, dict) else None,
                    content=data if isinstance(data, str) else None
                )

            # 计算响应时间
            end_time = datetime.now()
            response_time = int((end_time - start_time).total_seconds() * 1000)

            # 处理响应数据
            try:
                response_data = response.json()
            except:
                response_data = response.text

            # 构建执行结果
            execution_result = {
                "success": True,
                "status_code": response.status_code,
                "response_headers": dict(response.headers),
                "response_body": response_data,
                "execution_time": response_time,
                "request_info": {
                    "url": url,
                    "method": method,
                    "params": params,
                    "headers": headers,
                    "body": data
                }
            }

            # 更新执行次数和最后执行时间
            await api_request.update_from_dict({
                "execution_count": api_request.execution_count + 1,
                "last_executed": end_time
            }).save()

            logger.info(f"API请求执行成功: {api_request.api_name}, 状态码: {response.status_code}")
            return execution_result

        except httpx.RequestError as e:
            logger.error(f"HTTP请求错误: {str(e)}")
            # 返回错误结果而不是抛出异常
            return {
                "success": False,
                "status_code": None,
                "response_headers": {},
                "response_body": None,
                "execution_time": 0,
                "error_message": f"请求失败: {str(e)}"
            }
        except Exception as e:
            logger.error(f"执行API请求失败: {str(e)}")
            # 返回错误结果而不是抛出异常
            return {
                "success": False,
                "status_code": None,
                "response_headers": {},
                "response_body": None,
                "execution_time": 0,
                "error_message": f"执行失败: {str(e)}"
            }


api_request_controller = ApiRequestController()
