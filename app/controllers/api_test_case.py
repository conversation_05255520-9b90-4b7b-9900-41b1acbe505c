import re
import json
import logging
from datetime import datetime
from typing import List, Dict, Any
import httpx
from tortoise.expressions import Q
from app.core.crud import CRUDBase
from app.models.admin import ApiTestCase, Project, Environment
from app.schemas.api_test_case import ApiTestCaseCreate, ApiTestCaseUpdate, ApiTestCaseCopy
from app.utils.assertion_executor import assertion_executor
from app.utils.variable_manager import VariableManager
from pypinyin import pinyin, Style

logger = logging.getLogger(__name__)


class ApiTestCaseController(CRUDBase[ApiTestCase, ApiTestCaseCreate, ApiTestCaseUpdate]):
    def __init__(self):
        super().__init__(model=ApiTestCase)

    async def generate_case_number(self, project_id: int) -> str:
        """生成用例编号：项目名称拼音首字母_数字"""
        # 获取项目信息
        project = await Project.get(id=project_id)
        project_name = project.name

        # 处理项目名称生成前缀
        def get_initials(name: str) -> str:
            """获取名称的拼音首字母或英文字母"""
            letters = []
            for char in name:
                if '\u4e00' <= char <= '\u9fff':  # 中文字符
                    # 获取拼音首字母（小写）
                    py = pinyin(char, style=Style.FIRST_LETTER)
                    if py and py[0]:
                        letters.append(py[0][0].lower())
                elif char.isalpha():  # 英文字符
                    letters.append(char.lower())
            return ''.join(letters)

        # 获取项目名称的拼音首字母
        prefix = get_initials(project_name)

        # 如果前缀为空则使用默认值
        if not prefix:
            prefix = "tc"
        # 限制前缀长度（建议6-8个字符）
        prefix = prefix[:8]

        # 查找该项目下最大的编号
        existing_cases = await self.model.filter(project_id=project_id).order_by("-case_number")

        max_number = 0
        if existing_cases:
            for case in existing_cases:
                match = re.search(r'_(\d+)$', case.case_number)
                if match:
                    max_number = max(max_number, int(match.group(1)))

        # 生成新编号
        new_number = max_number + 1
        return f"{prefix}_{new_number:05d}"

    async def create_with_case_number(self, obj_in: ApiTestCaseCreate, user_id: int = None) -> ApiTestCase:
        """创建测试用例并自动生成用例编号"""
        # 生成用例编号
        case_number = await self.generate_case_number(obj_in.project_id)

        # 创建数据字典
        create_data = obj_in.model_dump()
        create_data["case_number"] = case_number
        if user_id:
            create_data["user_id"] = user_id

        # 处理JSON字段，如果为空字符串则设为None
        if create_data.get("params") == "":
            create_data["params"] = None

        # 创建测试用例
        obj = await self.model.create(**create_data)
        return obj

    async def copy_test_case(self, copy_data: ApiTestCaseCopy, user_id: int = None) -> ApiTestCase:
        """复制测试用例"""
        # 获取原测试用例
        original_case = await self.model.get(id=copy_data.id)

        # 生成新的用例编号
        case_number = await self.generate_case_number(copy_data.project_id)

        # 创建复制的测试用例
        create_data = {
            "case_number": case_number,
            "case_name": copy_data.case_name,
            "method": original_case.method,
            "url": original_case.url,
            "params": original_case.params,
            "body": original_case.body,
            "expected_result": original_case.expected_result,
            "is_smoke": original_case.is_smoke,
            "status": "pending",  # 复制的用例状态重置为待审核
            "source": original_case.source,
            "project_id": copy_data.project_id,
            "module_id": original_case.module_id,
        }

        if user_id:
            create_data["user_id"] = user_id

        obj = await self.model.create(**create_data)
        return obj

    async def list_by_project(self, project_id: int, page: int = 1, page_size: int = 10,
                              search: Q = None) -> tuple[int, List[ApiTestCase]]:
        """根据项目ID获取测试用例列表"""
        q = Q(project_id=project_id)
        if search:
            q &= search

        # 按用例编号排序（提取数字部分进行排序）
        total = await self.model.filter(q).count()

        # 获取所有数据然后在Python中排序（因为数据库排序复杂）
        all_cases = await self.model.filter(q).all()

        # 自定义排序函数
        def sort_key(case):
            match = re.search(r'_(\d+)$', case.case_number)
            return int(match.group(1)) if match else 0

        sorted_cases = sorted(all_cases, key=sort_key)

        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        page_cases = sorted_cases[start:end]

        return total, page_cases

    async def list_with_user_info(self, page: int = 1, page_size: int = 10, search: Q = None) -> tuple[
        int, List[Dict[str, Any]]]:
        """获取测试用例列表并包含用户信息"""
        from app.models.admin import User

        total = await self.model.filter(search or Q()).count()

        # 获取测试用例数据
        test_cases = await self.model.filter(search or Q()).offset((page - 1) * page_size).limit(page_size).all()

        # 获取所有相关的用户ID
        user_ids = [case.user_id for case in test_cases if case.user_id]
        users = {}
        if user_ids:
            user_objs = await User.filter(id__in=user_ids).all()
            users = {user.id: user.username for user in user_objs}

        # 构建返回数据
        result = []
        for case in test_cases:
            case_dict = await case.to_dict()
            case_dict['username'] = users.get(case.user_id, '') if case.user_id else ''
            result.append(case_dict)

        return total, result

    async def list_by_project_with_user_info(self, project_id: int, page: int = 1, page_size: int = 10,
                                             search: Q = None) -> tuple[int, List[Dict[str, Any]]]:
        """根据项目ID获取测试用例列表并包含用户信息"""
        from app.models.admin import User

        q = Q(project_id=project_id)
        if search:
            q &= search

        # 按用例编号排序（提取数字部分进行排序）
        total = await self.model.filter(q).count()

        # 获取所有数据然后在Python中排序（因为数据库排序复杂）
        all_cases = await self.model.filter(q).all()

        # 自定义排序函数
        def sort_key(case):
            match = re.search(r'_(\d+)$', case.case_number)
            return int(match.group(1)) if match else 0

        sorted_cases = sorted(all_cases, key=sort_key)

        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        page_cases = sorted_cases[start:end]

        # 获取所有相关的用户ID
        user_ids = [case.user_id for case in page_cases if case.user_id]
        users = {}
        if user_ids:
            user_objs = await User.filter(id__in=user_ids).all()
            users = {user.id: user.username for user in user_objs}

        # 构建返回数据
        result = []
        for case in page_cases:
            case_dict = await case.to_dict()
            case_dict['username'] = users.get(case.user_id, '') if case.user_id else ''
            result.append(case_dict)

        return total, result

    async def execute_test_case(self, test_case_id: int, environment_id: int,
                               variable_manager: VariableManager = None) -> Dict[str, Any]:
        """执行测试用例"""
        try:
            # 获取测试用例
            test_case = await self.get(id=test_case_id)

            # 获取环境配置
            environment = await Environment.get(id=environment_id)

            # 如果没有传入变量管理器，创建一个新的
            if variable_manager is None:
                variable_manager = VariableManager()

            # 构建完整的URL
            # 判断是否使用HTTPS（端口443或主机名包含https）
            if environment.port == 443 or 'https' in environment.host.lower():
                protocol = "https"
                default_port = 443
            else:
                protocol = "http"
                default_port = 80

            # 清理主机名（移除可能的协议前缀）
            clean_host = environment.host.replace('http://', '').replace('https://', '')

            base_url = f"{protocol}://{clean_host}"
            if environment.port and environment.port != default_port:
                base_url = f"{base_url}:{environment.port}"

            # 准备测试用例数据进行变量替换
            test_case_data = {
                'url': test_case.url,
                'params': test_case.params,
                'body': test_case.body
            }

            # 使用变量管理器处理测试用例数据
            processed_data = variable_manager.process_test_case_data(test_case_data)

            # 确保用例URL以/开头
            case_url = processed_data['url']
            if not case_url.startswith('/'):
                case_url = f"/{case_url}"

            # 构建完整URL
            full_url = f"{base_url}{case_url}"

            # 处理请求参数（已经过变量替换）
            params = {}
            if processed_data['params']:
                if isinstance(processed_data['params'], str):
                    try:
                        params = json.loads(processed_data['params'])
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析params: {processed_data['params']}")
                elif isinstance(processed_data['params'], dict):
                    params = processed_data['params']

            # 处理请求头
            headers = {}
            if environment.token:
                # 使用环境配置中的token字段名，如果没有配置则默认使用'token'
                token_field_name = getattr(environment, 'token_field_name', 'token') or 'token'
                token_value = environment.token
                # 如果存在前缀且不为空，则拼接前缀和token
                if environment.prefix and environment.prefix.strip():
                    token_value = f"{environment.prefix} {token_value}"
                headers[token_field_name] = token_value

            # 处理请求体（已经过变量替换）
            data = None
            if processed_data['body'] and test_case.method.upper() in ['POST', 'PUT', 'PATCH']:
                if isinstance(processed_data['body'], str):
                    try:
                        data = json.loads(processed_data['body'])
                    except json.JSONDecodeError:
                        data = processed_data['body']
                else:
                    data = processed_data['body']

            # 记录开始时间
            start_time = datetime.now()

            # 发送HTTP请求
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.request(
                    method=test_case.method.upper(),
                    url=full_url,
                    params=params,
                    headers=headers,
                    json=data if isinstance(data, dict) else None,
                    content=data if isinstance(data, str) else None
                )

            # 计算响应时间
            end_time = datetime.now()
            response_time = int((end_time - start_time).total_seconds() * 1000)

            # 处理响应数据
            try:
                response_data = response.json()
            except:
                response_data = response.text

            # 构建执行结果
            execution_result = {
                "success": True,
                "status_code": response.status_code,
                "response_headers": dict(response.headers),
                "response_body": response_data,
                "execution_time": response_time,
                "request_info": {
                    "url": full_url,
                    "method": test_case.method.upper(),
                    "params": params,
                    "headers": headers,
                    "body": data
                }
            }

            # 执行断言
            assertions = []
            if test_case.expected_result:
                try:
                    assertions = json.loads(test_case.expected_result)
                except json.JSONDecodeError:
                    logger.warning(f"测试用例 {test_case.case_name} 的断言配置不是有效的JSON格式")

            if assertions:
                assertion_results = assertion_executor.execute_assertions(assertions, execution_result)
                execution_result["assertion_results"] = assertion_results
                logger.info(f"断言执行完成: {assertion_results['passed_count']}/{assertion_results['total_count']} 通过")

                # 如果断言失败，标记为失败但保留所有响应信息
                if not assertion_results.get('all_passed', True):
                    execution_result["success"] = False
                    execution_result["error_message"] = f"断言失败: {assertion_results['failed_count']}/{assertion_results['total_count']} 个断言未通过"

            # 提取变量
            variable_extracts = []
            if test_case.variable_extracts:
                try:
                    variable_extracts = json.loads(test_case.variable_extracts)
                except json.JSONDecodeError:
                    logger.warning(f"测试用例 {test_case.case_name} 的变量提取配置不是有效的JSON格式")

            if variable_extracts:
                extracted_vars = variable_manager.extract_variables(variable_extracts, execution_result)
                execution_result["extracted_variables"] = extracted_vars
                logger.info(f"变量提取完成: 提取了 {len(extracted_vars)} 个变量")

            logger.info(f"测试用例执行完成: {test_case.case_name}, 状态码: {response.status_code}, 成功: {execution_result['success']}")
            return execution_result

        except httpx.RequestError as e:
            logger.error(f"HTTP请求错误: {str(e)}")
            return {
                "success": False,
                "status_code": None,
                "response_headers": {},
                "response_body": None,
                "execution_time": 0,
                "error_message": f"请求失败: {str(e)}"
            }
        except Exception as e:
            logger.error(f"执行测试用例失败: {str(e)}")
            return {
                "success": False,
                "status_code": None,
                "response_headers": {},
                "response_body": None,
                "execution_time": 0,
                "error_message": f"执行失败: {str(e)}"
            }


# 创建控制器实例
api_test_case_controller = ApiTestCaseController()
