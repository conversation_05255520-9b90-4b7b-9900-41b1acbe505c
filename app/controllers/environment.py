from typing import List, Optional
from tortoise.expressions import Q
from app.core.crud import CRUDBase
from app.models.admin import Environment
from app.schemas.environment import EnvironmentCreate, EnvironmentUpdate, EnvironmentCopy


class EnvironmentController(CRUDBase[Environment, EnvironmentCreate, EnvironmentUpdate]):
    def __init__(self):
        super().__init__(model=Environment)

    async def get_environments_by_project(
        self, 
        project_id: int, 
        page: int = 1, 
        page_size: int = 10,
        name: str = "",
        env_type: str = ""
    ) -> tuple[int, List[Environment]]:
        """根据项目ID获取环境配置列表"""
        q = Q(project_id=project_id)
        
        if name:
            q &= Q(name__contains=name)
        if env_type:
            q &= Q(env_type=env_type)
            
        return await self.list(page=page, page_size=page_size, search=q, order=["env_type", "name"])

    async def copy_environment(self, copy_data: EnvironmentCopy) -> Environment:
        """复制环境配置"""
        # 获取源环境
        source_env = await self.get(id=copy_data.id)
        source_dict = await source_env.to_dict()
        
        # 移除不需要复制的字段
        source_dict.pop('id', None)
        source_dict.pop('created_at', None)
        source_dict.pop('updated_at', None)
        
        # 设置新的名称和项目ID
        source_dict['name'] = copy_data.name
        if copy_data.project_id:
            source_dict['project_id'] = copy_data.project_id
            
        # 创建新环境
        create_data = EnvironmentCreate(**source_dict)
        return await self.create(obj_in=create_data)

    async def check_name_exists(self, name: str, project_id: int, exclude_id: Optional[int] = None) -> bool:
        """检查环境名称在项目中是否已存在"""
        q = Q(name=name, project_id=project_id)
        if exclude_id:
            q &= ~Q(id=exclude_id)
        return await self.model.filter(q).exists()


environment_controller = EnvironmentController()
