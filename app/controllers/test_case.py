import re
from typing import List, Dict, Any
from tortoise.expressions import Q
from app.core.crud import CRUDBase
from app.models.admin import TestCase, Project
from app.schemas.test_case import TestCaseCreate, TestCaseUpdate, TestCaseCopy
from pypinyin import pinyin, Style


class TestCaseController(CRUDBase[TestCase, TestCaseCreate, TestCaseUpdate]):
    def __init__(self):
        super().__init__(model=TestCase)

    def get_project_initials(self, project_name: str) -> str:
        """获取项目名称的拼音首字母"""
        try:
            # 将中文转换为拼音首字母
            initials = []
            for char in project_name:
                if '\u4e00' <= char <= '\u9fff':  # 判断是否为中文字符
                    py = pinyin(char, style=Style.FIRST_LETTER, strict=False)
                    if py and py[0]:
                        initials.append(py[0][0].lower())
                elif char.isalpha():  # 英文字符直接取首字母
                    initials.append(char.lower())
            
            result = ''.join(initials)
            return result if result else 'tc'  # 默认返回tc
        except Exception:
            return 'tc'

    async def generate_case_number(self, project_id: int) -> str:
        """生成用例编号"""
        try:
            # 获取项目信息
            project = await Project.get(id=project_id)
            project_initials = self.get_project_initials(project.name)
            
            # 查找该项目下最大的用例编号
            existing_cases = await self.model.filter(project_id=project_id).all()
            
            max_number = 0
            pattern = rf'^{re.escape(project_initials)}_(\d+)$'
            
            for case in existing_cases:
                match = re.match(pattern, case.case_number)
                if match:
                    number = int(match.group(1))
                    max_number = max(max_number, number)
            
            # 生成新的编号
            new_number = max_number + 1
            return f"{project_initials}_{new_number}"
            
        except Exception as e:
            # 如果出错，使用时间戳作为后缀
            import time
            return f"tc_{int(time.time())}"

    async def create_with_case_number(self, obj_in: TestCaseCreate, user_id: int = None) -> TestCase:
        """创建测试用例并自动生成用例编号"""
        # 生成用例编号
        case_number = await self.generate_case_number(obj_in.project_id)

        # 创建数据字典
        create_data = obj_in.model_dump()
        create_data["case_number"] = case_number
        if user_id:
            create_data["user_id"] = user_id

        # 创建测试用例
        obj = await self.model.create(**create_data)
        return obj

    async def copy_test_case(self, copy_data: TestCaseCopy, user_id: int = None) -> TestCase:
        """复制测试用例"""
        # 获取原测试用例
        source_case = await self.get(id=copy_data.id)
        source_dict = await source_case.to_dict()
        
        # 移除不需要复制的字段
        source_dict.pop('id', None)
        source_dict.pop('case_number', None)
        source_dict.pop('created_at', None)
        source_dict.pop('updated_at', None)
        
        # 设置新的名称和项目ID
        source_dict['case_name'] = copy_data.case_name
        source_dict['project_id'] = copy_data.project_id
        if user_id:
            source_dict['user_id'] = user_id
        
        # 生成新的用例编号
        case_number = await self.generate_case_number(copy_data.project_id)
        source_dict['case_number'] = case_number

        # 创建新测试用例
        obj = await self.model.create(**source_dict)
        return obj

    async def list_by_project(self, project_id: int, page: int = 1, page_size: int = 10,
                              search: Q = None) -> tuple[int, List[TestCase]]:
        """根据项目ID获取测试用例列表"""
        q = Q(project_id=project_id)
        if search:
            q &= search

        # 按用例编号排序（提取数字部分进行排序）
        total = await self.model.filter(q).count()

        # 获取所有数据然后在Python中排序（因为数据库排序复杂）
        all_cases = await self.model.filter(q).all()

        # 自定义排序函数
        def sort_key(case):
            match = re.search(r'_(\d+)$', case.case_number)
            return int(match.group(1)) if match else 0

        sorted_cases = sorted(all_cases, key=sort_key)

        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        page_cases = sorted_cases[start:end]

        return total, page_cases

    async def list_with_user_info(self, page: int = 1, page_size: int = 10, search: Q = None) -> tuple[
        int, List[Dict[str, Any]]]:
        """获取测试用例列表并包含用户信息"""
        from app.models.admin import User

        total = await self.model.filter(search or Q()).count()

        # 获取测试用例数据
        test_cases = await self.model.filter(search or Q()).offset((page - 1) * page_size).limit(page_size).all()

        # 获取所有相关的用户ID
        user_ids = [case.user_id for case in test_cases if case.user_id]
        users = {}
        if user_ids:
            user_objs = await User.filter(id__in=user_ids).all()
            users = {user.id: user.username for user in user_objs}

        # 构建返回数据
        result = []
        for case in test_cases:
            case_dict = await case.to_dict()
            case_dict['username'] = users.get(case.user_id, '') if case.user_id else ''
            result.append(case_dict)

        return total, result

    async def list_by_project_with_user_info(self, project_id: int, page: int = 1, page_size: int = 10,
                                             search: Q = None) -> tuple[int, List[Dict[str, Any]]]:
        """根据项目ID获取测试用例列表并包含用户信息"""
        from app.models.admin import User

        q = Q(project_id=project_id)
        if search:
            q &= search

        # 按用例编号排序（提取数字部分进行排序）
        total = await self.model.filter(q).count()

        # 获取所有数据然后在Python中排序（因为数据库排序复杂）
        all_cases = await self.model.filter(q).all()

        # 自定义排序函数
        def sort_key(case):
            match = re.search(r'_(\d+)$', case.case_number)
            return int(match.group(1)) if match else 0

        sorted_cases = sorted(all_cases, key=sort_key)

        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        page_cases = sorted_cases[start:end]

        # 获取所有相关的用户ID
        user_ids = [case.user_id for case in page_cases if case.user_id]
        users = {}
        if user_ids:
            user_objs = await User.filter(id__in=user_ids).all()
            users = {user.id: user.username for user in user_objs}

        # 构建返回数据
        result = []
        for case in page_cases:
            case_dict = await case.to_dict()
            case_dict['username'] = users.get(case.user_id, '') if case.user_id else ''
            result.append(case_dict)

        return total, result


# 创建控制器实例
test_case_controller = TestCaseController()
