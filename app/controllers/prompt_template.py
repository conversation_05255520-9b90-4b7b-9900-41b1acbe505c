import logging
import time
from typing import Optional, Dict, Any
from jinja2 import Template, TemplateError
from tortoise.expressions import Q

from app.core.crud import CRUDBase
from app.models.admin import PromptTemplate, AIModelConfig
from app.schemas.prompt_template import (
    PromptTemplateCreate, 
    PromptTemplateUpdate,
    PromptTemplateTest,
    PromptTemplateTestResponse
)
from app.controllers.ai_model_config import ai_model_config_controller

logger = logging.getLogger(__name__)


class PromptTemplateController(CRUDBase[PromptTemplate, PromptTemplateCreate, PromptTemplateUpdate]):
    def __init__(self):
        super().__init__(model=PromptTemplate)

    async def create_prompt_template(self, obj_in: PromptTemplateCreate, user_id: int) -> PromptTemplate:
        """创建提示词模板"""
        # 如果设置为默认模板，先取消同分类的其他默认模板
        if obj_in.is_default:
            await self.model.filter(category=obj_in.category, is_default=True).update(is_default=False)
        
        # 创建模板
        obj_data = obj_in.model_dump()
        obj_data["user_id"] = user_id
        obj = await self.model.create(**obj_data)
        return obj

    async def update_prompt_template(self, id: int, obj_in: PromptTemplateUpdate) -> PromptTemplate:
        """更新提示词模板"""
        obj = await self.get(id=id)
        
        # 如果设置为默认模板，先取消同分类的其他默认模板
        if obj_in.is_default:
            category = obj_in.category or obj.category
            await self.model.filter(category=category, is_default=True).exclude(id=id).update(is_default=False)
        
        # 更新模板
        update_data = obj_in.model_dump(exclude_unset=True, exclude={"id"})
        await obj.update_from_dict(update_data)
        await obj.save()
        return obj

    async def get_default_template(self, category: str) -> Optional[PromptTemplate]:
        """获取指定分类的默认提示词模板"""
        return await self.model.filter(category=category, is_default=True, is_active=True).first()

    async def get_templates_by_category(self, category: str) -> list[PromptTemplate]:
        """根据分类获取提示词模板"""
        return await self.model.filter(category=category, is_active=True).order_by("-created_at").all()

    async def copy_template(self, template_id: int, new_name: str, user_id: int) -> PromptTemplate:
        """复制提示词模板"""
        original = await self.get(id=template_id)
        
        # 创建副本
        copy_data = {
            "name": new_name,
            "category": original.category,
            "description": original.description,
            "prompt_content": original.prompt_content,
            "variables": original.variables,
            "is_active": True,
            "is_default": False,
            "user_id": user_id
        }
        
        copy_obj = await self.model.create(**copy_data)
        return copy_obj

    async def increment_usage_count(self, template_id: int) -> None:
        """增加使用次数"""
        from tortoise.expressions import F
        await self.model.filter(id=template_id).update(usage_count=F("usage_count") + 1)

    def render_prompt(self, prompt_content: str, variables: Dict[str, Any]) -> str:
        """渲染提示词模板"""
        try:
            template = Template(prompt_content)
            return template.render(**variables)
        except TemplateError as e:
            raise ValueError(f"模板渲染失败: {str(e)}")

    async def test_template(self, test_request: PromptTemplateTest) -> PromptTemplateTestResponse:
        """测试提示词模板"""
        start_time = time.time()
        
        try:
            # 获取模板
            template = await self.get(id=test_request.template_id)
            
            # 渲染提示词
            rendered_prompt = self.render_prompt(template.prompt_content, test_request.variables)
            
            # 获取AI模型配置
            if test_request.ai_model_config_id:
                ai_config = await ai_model_config_controller.get(id=test_request.ai_model_config_id)
            else:
                ai_config = await ai_model_config_controller.get_default_config()
                if not ai_config:
                    return PromptTemplateTestResponse(
                        success=False,
                        rendered_prompt=rendered_prompt,
                        error_message="未找到可用的AI模型配置"
                    )
            
            # 调用AI模型
            ai_response = await ai_model_config_controller.call_ai_model(ai_config, rendered_prompt)
            
            response_time = time.time() - start_time
            
            return PromptTemplateTestResponse(
                success=True,
                rendered_prompt=rendered_prompt,
                ai_response=ai_response,
                response_time=response_time
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"测试提示词模板失败: {str(e)}")
            
            return PromptTemplateTestResponse(
                success=False,
                rendered_prompt=rendered_prompt if 'rendered_prompt' in locals() else "",
                error_message=str(e),
                response_time=response_time
            )

    async def get_categories(self) -> list[str]:
        """获取所有分类"""
        result = await self.model.all().distinct().values_list("category", flat=True)
        return list(result)


# 创建全局实例
prompt_template_controller = PromptTemplateController()


# 初始化默认的功能测试用例生成提示词模板
async def init_default_prompt_templates():
    """初始化默认的提示词模板"""
    # 检查是否已存在接口测试用例生成模板
    existing_api = await prompt_template_controller.model.filter(
        category="api_test_case",
        name="默认接口测试用例生成模板"
    ).exists()

    if not existing_api:
        api_template = {
            "name": "默认接口测试用例生成模板",
            "category": "api_test_case",
            "description": "用于生成接口测试用例的默认提示词模板",
            "prompt_content": """You are a professional API test engineer. Please generate API test cases based on the following interface information.

Interface Information:
- API Name: {{ api_name }}
- Request Method: {{ method }}
- API Path: {{ url_path }}
- Request Parameters: {{ params_list }}

Project Information:
- Project Name: {{ project_name }}

Please generate {{ generate_count }} API test cases. Each test case must contain the following fields:
1. case_name: Test case name (concise and clear)
2. method: Request method (consistent with the interface)
3. url: Request URL (complete interface path)
4. params: Request parameters (JSON format string, null if no parameters)
5. body: Request body (JSON format string, usually null for GET requests)
6. expected_result: Assertion configuration (JSON format string, including status code and response body assertions)
7. is_smoke: Whether it is a smoke test case (true/false)

Please return in JSON array format, ensuring correct JSON format:
```json
[
  {
    "case_name": "Normal Request Test",
    "method": "GET",
    "url": "/api/v1/users",
    "params": "{\\"page\\": 1, \\"size\\": 10}",
    "body": null,
    "expected_result": "{\\"status_code\\": 200, \\"response_body\\": {\\"contains\\": [\\"data\\", \\"total\\"]}}",
    "is_smoke": true
  }
]
```

Notes:
- Test cases should cover normal flow, abnormal parameters, boundary values and other scenarios
- Parameters and request body should comply with interface specifications
- Assertion configuration should include status code and key response field verification
- Core interfaces can be marked as smoke test cases
- Ensure correct JSON format, quotes in strings should be escaped""",
            "variables": {
                "api_name": {"type": "string", "description": "接口名称", "required": True},
                "method": {"type": "string", "description": "请求方法", "required": True},
                "url_path": {"type": "string", "description": "接口路径", "required": True},
                "params_list": {"type": "string", "description": "请求参数", "required": False},
                "project_name": {"type": "string", "description": "项目名称", "required": True},
                "generate_count": {"type": "integer", "description": "生成数量", "required": True, "default": 3}
            },
            "is_active": True,
            "is_default": True,
            "user_id": 1  # 系统用户
        }

        await prompt_template_controller.model.create(**api_template)
        logger.info("已创建默认接口测试用例生成提示词模板")

    # 检查是否已存在功能测试用例生成模板
    existing_functional = await prompt_template_controller.model.filter(
        category="functional_test_case",
        name="默认功能测试用例生成模板"
    ).exists()

    if not existing_functional:
        functional_template = {
            "name": "默认功能测试用例生成模板",
            "category": "functional_test_case",
            "description": "用于生成功能测试用例的默认提示词模板",
            "prompt_content": """你是一个专业的软件测试工程师，请根据以下需求描述生成功能测试用例。

需求描述：
{{ requirement_description }}

项目信息：
- 项目名称：{{ project_name }}
{% if module_name %}- 模块名称：{{ module_name }}{% endif %}

请生成 {{ generate_count }} 个功能测试用例，每个测试用例必须包含以下字段：
1. case_name: 测试用例名称（简洁明了）
2. case_level: 用例等级（high/medium/low）
3. precondition: 前置条件（可选，如果没有则为空）
4. test_steps: 测试步骤（详细的操作步骤，用数字编号）
5. expected_result: 预期结果（明确的验证点）
6. is_smoke: 是否冒烟用例（true/false）

请以JSON数组格式返回，确保JSON格式正确：
```json
[
  {
    "case_name": "测试用例名称",
    "case_level": "medium",
    "precondition": "前置条件",
    "test_steps": "1. 步骤一\\n2. 步骤二\\n3. 步骤三",
    "expected_result": "预期结果描述",
    "is_smoke": false
  }
]
```

注意事项：
- 测试用例要覆盖正常流程、异常流程和边界条件
- 测试步骤要具体可执行
- 预期结果要明确可验证
- 根据功能重要性合理分配用例等级
- 核心功能可标记为冒烟用例""",
            "variables": {
                "requirement_description": {"type": "string", "description": "需求描述", "required": True},
                "project_name": {"type": "string", "description": "项目名称", "required": True},
                "module_name": {"type": "string", "description": "模块名称", "required": False},
                "generate_count": {"type": "integer", "description": "生成数量", "required": True, "default": 5}
            },
            "is_active": True,
            "is_default": True,
            "user_id": 1  # 系统用户
        }

        await prompt_template_controller.model.create(**functional_template)
        logger.info("已创建默认功能测试用例生成提示词模板")
