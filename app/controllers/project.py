from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi.exceptions import HTTPException
from tortoise.expressions import Q
from app.core.crud import CRUDBase
from app.schemas.project import ProjectCreate, ProjectUpdate, ModuleCreate, ModuleUpdate
from app.models.admin import Project, ProjectModule


class ProjectController(CRUDBase[Project, ProjectCreate, ProjectUpdate]):
    def __init__(self):
        super().__init__(model=Project)

    async def get_by_name(self, name: str) -> Optional[Project]:
        return await self.model.filter(name=name).first()

    async def create_project(self, obj_in: ProjectCreate) -> Project:
        print(f"Checking project name: {obj_in.name}")  # 添加调试日志
        db_obj = await self.get_by_name(obj_in.name)
        print(f"Existing project: {db_obj}")  # 添加调试日志
        if db_obj:
            raise HTTPException(status_code=400, detail="项目已存在")
        return await self.create(obj_in)

    async def get_project_list(self, name: str = None) -> List[Dict[str, Any]]:
        """获取项目列表（不分页）"""
        q = Q()
        if name:
            q &= Q(name__contains=name)

        projects = await self.model.filter(q).order_by("id").all()
        return [await project.to_dict() for project in projects]

    async def get_module_tree(self, project_id: int, name: str = None) -> List[Dict[str, Any]]:
        """获取项目下的模块树"""
        q = Q(project_id=project_id)
        if name:
            q &= Q(name__contains=name)

        modules = await ProjectModule.filter(q).order_by("order", "id").all()
        module_list = [await module.to_dict() for module in modules]

        # 构建树形结构
        return self._build_tree(module_list, 0)

    def _build_tree(self, items: List[Dict[str, Any]], parent_id: int) -> List[Dict[str, Any]]:
        """构建树形结构"""
        tree = []
        for item in items:
            if item['parent_id'] == parent_id:
                children = self._build_tree(items, item['id'])
                if children:
                    item['children'] = children
                tree.append(item)
        return tree

    async def create_module(self, obj_in: ModuleCreate) -> ProjectModule:
        """创建模块"""
        module_data = obj_in.dict()
        return await ProjectModule.create(**module_data)

    async def update_module(self, module_id: int, obj_in: ModuleUpdate) -> ProjectModule:
        """更新模块"""
        module = await ProjectModule.get_or_none(id=module_id)
        if not module:
            raise HTTPException(status_code=404, detail="模块不存在")

        update_data = obj_in.dict(exclude={'id'})
        await module.update_from_dict(update_data).save()
        return module

    async def delete_module(self, module_id: int) -> bool:
        """删除模块"""
        module = await ProjectModule.get_or_none(id=module_id)
        if not module:
            raise HTTPException(status_code=404, detail="模块不存在")

        # 检查是否有子模块
        children_count = await ProjectModule.filter(parent_id=module_id).count()
        if children_count > 0:
            raise HTTPException(status_code=400, detail="该模块下存在子模块，无法删除")

        await module.delete()
        return True

    async def check_can_delete_module(self, module_id: int) -> Dict[str, Any]:
        """检查模块是否可以删除"""
        module = await ProjectModule.get_or_none(id=module_id)
        if not module:
            return {"can_delete": False, "reason": "模块不存在"}

        # 检查是否有子模块
        children_count = await ProjectModule.filter(parent_id=module_id).count()
        if children_count > 0:
            return {"can_delete": False, "reason": "该模块下存在子模块"}

        return {"can_delete": True}


project_controller = ProjectController()
