from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from app.models.enums import EnvironmentType


class EnvironmentBase(BaseModel):
    name: str = Field(..., description="环境名称")
    env_type: EnvironmentType = Field(..., description="环境类型")
    host: str = Field(..., description="主机地址")
    port: Optional[int] = Field(default=None, description="端口号")
    token: Optional[str] = Field(None, description="访问令牌")
    prefix: Optional[str] = Field(None, description="令牌前缀")
    description: Optional[str] = Field(None, description="环境描述")
    project_id: int = Field(..., description="所属项目ID")
    is_active: bool = Field(default=True, description="是否启用")
    # Token自动获取配置
    token_url: Optional[str] = Field(None, description="Token获取URL")
    token_method: str = Field(default="POST", description="Token获取请求方式")
    token_headers: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Token获取请求头")
    token_body: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Token获取请求体")
    token_field_name: str = Field(default="token", description="Token字段名称")
    token_field_path: Optional[str] = Field(None, description="Token字段路径(JSONPath)")
    auto_refresh_token: bool = Field(default=False, description="是否自动刷新Token")
    token_refresh_interval: int = Field(default=3600, description="Token刷新间隔(秒)")
    # 验证码配置
    enable_captcha: bool = Field(default=False, description="是否启用验证码")
    captcha_url: Optional[str] = Field(None, description="验证码获取URL")
    captcha_method: str = Field(default="GET", description="验证码获取请求方式")
    captcha_headers: Optional[Dict[str, Any]] = Field(default_factory=dict, description="验证码获取请求头")
    captcha_body: Optional[Dict[str, Any]] = Field(default_factory=dict, description="验证码获取请求体")
    captcha_image_path: str = Field(default="content.imageBase64", description="验证码图片字段路径(JSONPath)")
    captcha_key_path: str = Field(default="content.codeKey", description="验证码Key字段路径(JSONPath)")


class EnvironmentCreate(EnvironmentBase):
    pass


class EnvironmentUpdate(EnvironmentBase):
    id: int


class EnvironmentCopy(BaseModel):
    id: int = Field(..., description="源环境ID")
    name: str = Field(..., description="新环境名称")
    project_id: Optional[int] = Field(None, description="目标项目ID，不传则复制到同一项目")
