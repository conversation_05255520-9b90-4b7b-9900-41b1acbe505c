from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel


class FunctionalTestPlanBase(BaseModel):
    plan_name: str
    level: str = "medium"
    status: str = "not_started"
    description: Optional[str] = None
    project_id: int


class FunctionalTestPlanCreate(FunctionalTestPlanBase):
    pass


class FunctionalTestPlanUpdate(BaseModel):
    id: int
    plan_name: str
    level: str = "medium"
    status: str = "not_started"
    description: Optional[str] = None
    project_id: int


class FunctionalTestPlanCopy(BaseModel):
    id: int


class FunctionalTestPlanCaseBase(BaseModel):
    plan_id: int
    case_id: int
    execution_order: int = 0
    execution_status: str = "pending"


class FunctionalTestPlanCaseCreate(FunctionalTestPlanCaseBase):
    pass


class FunctionalTestPlanCaseUpdate(BaseModel):
    id: int
    plan_id: int
    case_id: int
    execution_order: int = 0
    execution_status: str = "pending"


class AddTestCasesToPlan(BaseModel):
    plan_id: int
    case_ids: List[int]


class RemoveTestCasesFromPlan(BaseModel):
    plan_id: int
    case_ids: List[int]


class CaseOrder(BaseModel):
    case_id: int
    execution_order: int


class UpdateCaseOrder(BaseModel):
    plan_id: int
    case_orders: List[CaseOrder]


class UpdateCaseStatus(BaseModel):
    plan_id: int
    case_id: int
    execution_status: str
