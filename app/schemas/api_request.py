from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class ApiRequestBase(BaseModel):
    api_name: str
    method: str
    url: str
    project_id: int
    module_id: Optional[int] = None
    params: Optional[str] = None
    headers: Optional[str] = None
    body: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    is_favorite: Optional[bool] = False
    user_id: Optional[int] = None


class ApiRequestCreate(ApiRequestBase):
    pass


class ApiRequestUpdate(BaseModel):
    id: int
    api_name: str
    url: str
    method: str
    project_id: str
    module_id: Optional[int] = None
    description: Optional[str] = None
    category: Optional[str] = None
    params: Optional[str] = None  # 修改为可选字段
    headers: Optional[str] = None  # 修改为可选字段
    body: Optional[str] = None
    is_favorite: bool = False
    execution_count: int = 0
    last_executed: Optional[datetime] = None
