from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    status: str
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    manager: Optional[str] = None
    budget: Optional[float] = 0


class ProjectCreate(ProjectBase):
    pass


class ProjectUpdate(ProjectBase):
    id: int


class ModuleBase(BaseModel):
    name: str
    description: Optional[str] = None
    project_id: int
    parent_id: int = 0
    order: int = 0
    status: str = "active"


class ModuleCreate(ModuleBase):
    pass


class ModuleUpdate(ModuleBase):
    id: int
