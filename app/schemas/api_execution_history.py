from typing import Optional
from pydantic import BaseModel, Field
from app.models.enums import MethodType


class ApiExecutionHistoryBase(BaseModel):
    user_id: int = Field(..., description="用户ID")
    method: MethodType = Field(..., description="请求方法")
    url: str = Field(..., description="请求URL")
    params: Optional[dict] = Field(None, description="请求参数")
    headers: Optional[dict] = Field(None, description="请求头")
    body: Optional[str] = Field(None, description="请求体")
    status_code: Optional[int] = Field(None, description="响应状态码")
    response_time: Optional[int] = Field(None, description="响应时间(毫秒)")
    success: bool = Field(default=True, description="是否成功")
    # 新增响应相关字段
    response_headers: Optional[dict] = Field(None, description="响应头")
    response_body: Optional[str] = Field(None, description="响应体")
    response_size: Optional[str] = Field(None, description="响应大小")
    response_status_text: Optional[str] = Field(None, description="响应状态文本")


class ApiExecutionHistoryCreate(ApiExecutionHistoryBase):
    pass


class ApiExecutionHistoryUpdate(ApiExecutionHistoryBase):
    id: int
