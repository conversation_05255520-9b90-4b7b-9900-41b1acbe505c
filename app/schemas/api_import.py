from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class ApiImportBase(BaseModel):
    api_name: str
    method: str
    url_path: str
    params_list: str
    status: Optional[str] = None
    project_id: int


class ApiImportCreate(BaseModel):
    api_name: str
    method: str
    url_path: str
    params_list: str
    status: Optional[str] = None
    project_id: int


class ApiImportUpdate(BaseModel):
    id: int
    api_name: str
    url_path: str
    method: str
    project_id: int
    params_list: str
    status: Optional[str]


class ApiImportAIGenerateRequest(BaseModel):
    """API导入AI生成测试用例请求"""
    api_import_id: int = Field(..., description="API导入记录ID")
    generate_count: int = Field(3, description="生成数量", ge=1, le=10)
    prompt_template_id: Optional[int] = Field(None, description="提示词模板ID，不传则使用默认模板")
    ai_model_config_id: Optional[int] = Field(None, description="AI模型配置ID，不传则使用默认模型")


class ApiTestCasePreview(BaseModel):
    """接口测试用例预览"""
    case_name: str = Field(..., description="用例名称")
    method: str = Field(..., description="请求方法")
    url: str = Field(..., description="请求URL")
    params: Optional[str] = Field(None, description="请求参数")
    body: Optional[str] = Field(None, description="请求体")
    expected_result: Optional[str] = Field(None, description="断言配置")
    is_smoke: bool = Field(False, description="是否冒烟用例")
    project_id: Optional[int] = Field(None, description="所属项目ID")
    module_id: Optional[int] = Field(None, description="所属模块ID")


class ApiImportAIGenerateResult(BaseModel):
    """API导入AI生成结果"""
    success: bool = Field(..., description="是否成功")
    generated_cases: List[ApiTestCasePreview] = Field([], description="生成的测试用例")
    error_message: Optional[str] = Field(None, description="错误信息")
    ai_response: Optional[str] = Field(None, description="AI原始响应")


class SaveApiTestCasesRequest(BaseModel):
    """保存接口测试用例请求"""
    api_import_id: int = Field(..., description="API导入记录ID")
    selected_cases: List[ApiTestCasePreview] = Field(..., description="选中的测试用例")