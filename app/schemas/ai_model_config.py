from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from app.models.enums import AIModelType, AIModelStatus


class AIModelConfigBase(BaseModel):
    """AI模型配置基础模型"""
    model_config = {"protected_namespaces": ()}

    name: str = Field(..., description="模型名称", max_length=100)
    model_type: AIModelType = Field(..., description="模型类型")
    api_key: str = Field(..., description="API密钥", max_length=500)
    api_url: Optional[str] = Field(None, description="API地址", max_length=500)
    model_name: Optional[str] = Field(None, description="具体模型名称", max_length=100)
    max_tokens: int = Field(4096, description="最大token数", ge=1, le=100000)
    temperature: float = Field(0.7, description="温度参数", ge=0.0, le=2.0)
    timeout: int = Field(30, description="超时时间(秒)", ge=1, le=300)
    status: AIModelStatus = Field(AIModelStatus.INACTIVE, description="状态")
    is_default: bool = Field(False, description="是否默认模型")
    description: Optional[str] = Field(None, description="模型描述")
    config_json: Optional[Dict[str, Any]] = Field(None, description="额外配置参数")


class AIModelConfigCreate(AIModelConfigBase):
    """创建AI模型配置"""
    pass


class AIModelConfigUpdate(BaseModel):
    """更新AI模型配置"""
    model_config = {"protected_namespaces": ()}

    id: int = Field(..., description="配置ID")
    name: Optional[str] = Field(None, description="模型名称", max_length=100)
    model_type: Optional[AIModelType] = Field(None, description="模型类型")
    api_key: Optional[str] = Field(None, description="API密钥", max_length=500)
    api_url: Optional[str] = Field(None, description="API地址", max_length=500)
    model_name: Optional[str] = Field(None, description="具体模型名称", max_length=100)
    max_tokens: Optional[int] = Field(None, description="最大token数", ge=1, le=100000)
    temperature: Optional[float] = Field(None, description="温度参数", ge=0.0, le=2.0)
    timeout: Optional[int] = Field(None, description="超时时间(秒)", ge=1, le=300)
    status: Optional[AIModelStatus] = Field(None, description="状态")
    is_default: Optional[bool] = Field(None, description="是否默认模型")
    description: Optional[str] = Field(None, description="模型描述")
    config_json: Optional[Dict[str, Any]] = Field(None, description="额外配置参数")


class AIModelConfigResponse(AIModelConfigBase):
    """AI模型配置响应"""
    model_config = {"protected_namespaces": (), "from_attributes": True}

    id: int = Field(..., description="配置ID")
    user_id: int = Field(..., description="创建用户ID")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class AIModelTestRequest(BaseModel):
    """AI模型测试请求"""
    config_id: int = Field(..., description="配置ID")
    test_message: str = Field("Hello, this is a test message.", description="测试消息")


class AIModelTestResponse(BaseModel):
    """AI模型测试响应"""
    success: bool = Field(..., description="测试是否成功")
    response: Optional[str] = Field(None, description="模型响应")
    error: Optional[str] = Field(None, description="错误信息")
    response_time: Optional[float] = Field(None, description="响应时间(秒)")
