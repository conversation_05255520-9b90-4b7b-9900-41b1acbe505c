from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class DatabaseConnectionBase(BaseModel):
    name: str = Field(..., description="连接名称")
    db_type: str = Field(..., description="数据库类型")
    host: str = Field(..., description="主机地址")
    port: int = Field(..., description="端口号")
    database: str = Field(..., description="数据库名称")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    charset: str = Field(default="utf8mb4", description="字符集")
    description: Optional[str] = Field(None, description="连接描述")
    is_active: bool = Field(default=True, description="是否启用")
    max_connections: int = Field(default=10, description="最大连接数")
    connection_timeout: int = Field(default=30, description="连接超时时间(秒)")


class DatabaseConnectionCreate(DatabaseConnectionBase):
    pass


class DatabaseConnectionUpdate(BaseModel):
    name: Optional[str] = Field(None, description="连接名称")
    db_type: Optional[str] = Field(None, description="数据库类型")
    host: Optional[str] = Field(None, description="主机地址")
    port: Optional[int] = Field(None, description="端口号")
    database: Optional[str] = Field(None, description="数据库名称")
    username: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    charset: Optional[str] = Field(None, description="字符集")
    description: Optional[str] = Field(None, description="连接描述")
    is_active: Optional[bool] = Field(None, description="是否启用")
    max_connections: Optional[int] = Field(None, description="最大连接数")
    connection_timeout: Optional[int] = Field(None, description="连接超时时间(秒)")


class DatabaseConnectionOut(DatabaseConnectionBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    # 隐藏密码字段
    password: str = Field(default="****", description="密码")

    class Config:
        from_attributes = True


class QueryExecuteRequest(BaseModel):
    sql_content: str = Field(..., description="SQL语句内容")
    database_connection_id: int = Field(..., description="数据库连接ID")
    query_name: Optional[str] = Field(None, description="查询名称")
    limit: Optional[int] = Field(default=1000, description="结果限制行数")


class QueryResult(BaseModel):
    columns: List[str] = Field(..., description="列名列表")
    data: List[List[Any]] = Field(..., description="查询结果数据")
    total_count: int = Field(..., description="总行数")
    execution_time: int = Field(..., description="执行时间(毫秒)")
    affected_rows: Optional[int] = Field(None, description="影响行数")


class QueryHistoryOut(BaseModel):
    id: int
    query_name: Optional[str]
    sql_content: str
    database_connection_id: int
    database_connection_name: Optional[str] = Field(None, description="数据库连接名称")
    execution_time: Optional[int]
    affected_rows: Optional[int]
    result_count: Optional[int]
    status: str
    error_message: Optional[str]
    is_favorite: bool
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TableInfo(BaseModel):
    table_name: str = Field(..., description="表名")
    table_comment: Optional[str] = Field(None, description="表注释")
    table_type: str = Field(..., description="表类型")


class ColumnInfo(BaseModel):
    column_name: str = Field(..., description="列名")
    data_type: str = Field(..., description="数据类型")
    is_nullable: str = Field(..., description="是否可空")
    column_default: Optional[str] = Field(None, description="默认值")
    column_comment: Optional[str] = Field(None, description="列注释")
    character_maximum_length: Optional[int] = Field(None, description="最大长度")
    is_primary_key: bool = Field(default=False, description="是否主键")


class DatabaseSchema(BaseModel):
    tables: List[TableInfo] = Field(..., description="表信息列表")


class TableSchema(BaseModel):
    table_name: str = Field(..., description="表名")
    columns: List[ColumnInfo] = Field(..., description="列信息列表")


class ConnectionTestResult(BaseModel):
    success: bool = Field(..., description="连接是否成功")
    message: str = Field(..., description="连接结果消息")
    server_version: Optional[str] = Field(None, description="服务器版本")
    connection_time: Optional[int] = Field(None, description="连接耗时(毫秒)")
