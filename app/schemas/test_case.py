from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class TestCaseBase(BaseModel):
    case_name: str
    case_level: str = "medium"
    precondition: Optional[str] = None
    test_steps: str
    expected_result: str
    is_smoke: Optional[bool] = False
    status: Optional[str] = "pending"
    source: Optional[str] = "manual"
    project_id: int
    module_id: Optional[int] = None


class TestCaseCreate(TestCaseBase):
    pass


class TestCaseUpdate(BaseModel):
    id: int
    case_name: str
    case_level: str = "medium"
    precondition: Optional[str] = None
    test_steps: str
    expected_result: str
    is_smoke: Optional[bool] = False
    status: Optional[str] = "pending"
    source: Optional[str] = "manual"
    project_id: int
    module_id: Optional[int] = None


class TestCaseCopy(BaseModel):
    id: int
    case_name: str
    project_id: int
