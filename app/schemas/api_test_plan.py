from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel


class ApiTestPlanBase(BaseModel):
    plan_name: str
    level: str = "medium"
    status: str = "not_started"
    description: Optional[str] = None
    project_id: int
    environment_id: Optional[int] = None
    enable_dingtalk: Optional[bool] = False


class ApiTestPlanCreate(ApiTestPlanBase):
    pass


class ApiTestPlanUpdate(BaseModel):
    id: int
    plan_name: str
    level: str = "medium"
    status: str = "not_started"
    description: Optional[str] = None
    project_id: int
    environment_id: Optional[int] = None
    enable_dingtalk: Optional[bool] = False


class ApiTestPlanCopy(BaseModel):
    id: int


class ApiTestPlanCaseBase(BaseModel):
    plan_id: int
    case_id: int
    execution_order: int = 0


class ApiTestPlanCaseCreate(ApiTestPlanCaseBase):
    pass


class ApiTestPlanCaseUpdate(BaseModel):
    id: int
    plan_id: int
    case_id: int
    execution_order: int = 0
    execution_result: Optional[str] = None
    response_time: Optional[int] = None
    error_message: Optional[str] = None


class AddTestCasesToPlan(BaseModel):
    plan_id: int
    case_ids: List[int]


class RemoveTestCasesFromPlan(BaseModel):
    plan_id: int
    case_ids: List[int]


class ExecuteTestPlan(BaseModel):
    plan_id: int
    environment_id: int


class CaseOrder(BaseModel):
    case_id: int
    execution_order: int


class UpdateCaseOrder(BaseModel):
    plan_id: int
    case_orders: List[CaseOrder]
