from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class JsonFormatRequest(BaseModel):
    """JSON格式化请求"""
    json_data: str = Field(..., description="要格式化的JSON字符串")
    indent: int = Field(default=2, description="缩进空格数", ge=0, le=8)
    sort_keys: bool = Field(default=False, description="是否排序键名")


class JsonValidateRequest(BaseModel):
    """JSON验证请求"""
    json_data: str = Field(..., description="要验证的JSON字符串")


class JsonConvertRequest(BaseModel):
    """JSON转换请求"""
    data: str = Field(..., description="要转换的数据")
    source_format: str = Field(..., description="源格式", pattern="^(json|xml|yaml|csv)$")
    target_format: str = Field(..., description="目标格式", pattern="^(json|xml|yaml|csv)$")
    csv_delimiter: str = Field(default=",", description="CSV分隔符")


class JsonPathRequest(BaseModel):
    """JSONPath查询请求"""
    json_data: str = Field(..., description="JSON数据")
    json_path: str = Field(..., description="JSONPath表达式")


class JsonSchemaValidateRequest(BaseModel):
    """JSON Schema验证请求"""
    json_data: str = Field(..., description="要验证的JSON数据")
    json_schema: str = Field(..., description="JSON Schema")


class JsonCompareRequest(BaseModel):
    """JSON对比请求"""
    json1: str = Field(..., description="第一个JSON")
    json2: str = Field(..., description="第二个JSON")


class JsonFormatResponse(BaseModel):
    """JSON格式化响应"""
    formatted_json: str = Field(..., description="格式化后的JSON")
    original_size: int = Field(..., description="原始大小（字符数）")
    formatted_size: int = Field(..., description="格式化后大小（字符数）")
    compression_ratio: float = Field(..., description="压缩比")


class JsonValidateResponse(BaseModel):
    """JSON验证响应"""
    is_valid: bool = Field(..., description="是否有效")
    error_message: Optional[str] = Field(None, description="错误信息")
    error_line: Optional[int] = Field(None, description="错误行号")
    error_column: Optional[int] = Field(None, description="错误列号")


class JsonConvertResponse(BaseModel):
    """JSON转换响应"""
    converted_data: str = Field(..., description="转换后的数据")
    source_format: str = Field(..., description="源格式")
    target_format: str = Field(..., description="目标格式")


class JsonPathResponse(BaseModel):
    """JSONPath查询响应"""
    results: List[Any] = Field(..., description="查询结果")
    count: int = Field(..., description="结果数量")


class JsonSchemaValidateResponse(BaseModel):
    """JSON Schema验证响应"""
    is_valid: bool = Field(..., description="是否符合Schema")
    errors: List[str] = Field(default=[], description="验证错误列表")


class JsonCompareResponse(BaseModel):
    """JSON对比响应"""
    is_equal: bool = Field(..., description="是否相等")
    differences: List[Dict[str, Any]] = Field(default=[], description="差异列表")


class JsonStatsResponse(BaseModel):
    """JSON统计响应"""
    total_keys: int = Field(..., description="总键数")
    total_values: int = Field(..., description="总值数")
    max_depth: int = Field(..., description="最大深度")
    data_types: Dict[str, int] = Field(..., description="数据类型统计")
    size_info: Dict[str, int] = Field(..., description="大小信息")
