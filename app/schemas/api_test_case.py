from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class ApiTestCaseBase(BaseModel):
    case_name: str
    method: str
    url: str
    params: Optional[str] = None
    body: Optional[str] = None
    expected_result: Optional[str] = None
    variable_extracts: Optional[str] = None
    is_smoke: Optional[bool] = False
    status: Optional[str] = "pending"
    source: Optional[str] = "manual"
    project_id: int
    module_id: Optional[int] = None


class ApiTestCaseCreate(ApiTestCaseBase):
    pass


class ApiTestCaseUpdate(BaseModel):
    id: int
    case_name: str
    method: str
    url: str
    params: Optional[str] = None
    body: Optional[str] = None
    expected_result: Optional[str] = None
    variable_extracts: Optional[str] = None
    is_smoke: Optional[bool] = False
    status: Optional[str] = "pending"
    source: Optional[str] = "manual"
    project_id: int
    module_id: Optional[int] = None


class ApiTestCaseCopy(BaseModel):
    id: int
    case_name: str
    project_id: int
