from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class ScheduledTaskBase(BaseModel):
    task_name: str = Field(..., description="任务名称")
    plan_id: int = Field(..., description="测试计划ID")
    cron_expression: str = Field(..., description="Cron表达式")
    is_active: bool = Field(True, description="是否启用")
    description: Optional[str] = Field(None, description="任务描述")


class ScheduledTaskCreate(ScheduledTaskBase):
    pass


class ScheduledTaskUpdate(BaseModel):
    id: int = Field(..., description="任务ID")
    task_name: Optional[str] = Field(None, description="任务名称")
    cron_expression: Optional[str] = Field(None, description="Cron表达式")
    is_active: Optional[bool] = Field(None, description="是否启用")
    description: Optional[str] = Field(None, description="任务描述")


class ScheduledTaskResponse(ScheduledTaskBase):
    id: int
    last_run_time: Optional[datetime]
    next_run_time: Optional[datetime]
    run_count: int
    creator_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
