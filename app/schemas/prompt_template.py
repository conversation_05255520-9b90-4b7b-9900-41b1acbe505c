from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class PromptTemplateBase(BaseModel):
    """提示词模板基础模型"""
    name: str = Field(..., description="模板名称", max_length=100)
    category: str = Field(..., description="模板分类", max_length=50)
    description: Optional[str] = Field(None, description="模板描述")
    prompt_content: str = Field(..., description="提示词内容")
    variables: Optional[Dict[str, Any]] = Field(None, description="变量定义")
    is_active: bool = Field(True, description="是否启用")
    is_default: bool = Field(False, description="是否默认模板")


class PromptTemplateCreate(PromptTemplateBase):
    """创建提示词模板"""
    pass


class PromptTemplateUpdate(BaseModel):
    """更新提示词模板"""
    id: int = Field(..., description="模板ID")
    name: Optional[str] = Field(None, description="模板名称", max_length=100)
    category: Optional[str] = Field(None, description="模板分类", max_length=50)
    description: Optional[str] = Field(None, description="模板描述")
    prompt_content: Optional[str] = Field(None, description="提示词内容")
    variables: Optional[Dict[str, Any]] = Field(None, description="变量定义")
    is_active: Optional[bool] = Field(None, description="是否启用")
    is_default: Optional[bool] = Field(None, description="是否默认模板")


class PromptTemplateResponse(PromptTemplateBase):
    """提示词模板响应"""
    model_config = {"from_attributes": True}
    
    id: int = Field(..., description="模板ID")
    usage_count: int = Field(..., description="使用次数")
    user_id: int = Field(..., description="创建用户ID")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class PromptTemplateCopy(BaseModel):
    """复制提示词模板"""
    id: int = Field(..., description="模板ID")
    name: str = Field(..., description="新模板名称")


class PromptTemplateTest(BaseModel):
    """测试提示词模板"""
    template_id: int = Field(..., description="模板ID")
    variables: Dict[str, Any] = Field(..., description="变量值")
    ai_model_config_id: Optional[int] = Field(None, description="AI模型配置ID，不传则使用默认模型")


class PromptTemplateTestResponse(BaseModel):
    """提示词模板测试响应"""
    success: bool = Field(..., description="是否成功")
    rendered_prompt: str = Field(..., description="渲染后的提示词")
    ai_response: Optional[str] = Field(None, description="AI响应内容")
    error_message: Optional[str] = Field(None, description="错误信息")
    response_time: Optional[float] = Field(None, description="响应时间(秒)")
