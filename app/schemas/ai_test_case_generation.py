from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class AITestCaseGenerationBase(BaseModel):
    """AI测试用例生成基础模型"""
    task_name: str = Field(..., description="任务名称", max_length=200)
    requirement_description: str = Field(..., description="需求描述")
    prompt_template_id: Optional[int] = Field(None, description="使用的提示词模板ID")
    ai_model_config_id: Optional[int] = Field(None, description="使用的AI模型配置ID，不传则使用默认模型")
    project_id: int = Field(..., description="所属项目ID")
    module_id: Optional[int] = Field(None, description="所属模块ID")


class AITestCaseGenerationCreate(AITestCaseGenerationBase):
    """创建AI测试用例生成任务"""
    pass


class AITestCaseGenerationUpdate(BaseModel):
    """更新AI测试用例生成任务"""
    id: int = Field(..., description="任务ID")
    task_name: Optional[str] = Field(None, description="任务名称", max_length=200)
    requirement_description: Optional[str] = Field(None, description="需求描述")
    prompt_template_id: Optional[int] = Field(None, description="使用的提示词模板ID")
    ai_model_config_id: Optional[int] = Field(None, description="使用的AI模型配置ID")
    project_id: Optional[int] = Field(None, description="所属项目ID")
    module_id: Optional[int] = Field(None, description="所属模块ID")


class AITestCaseGenerationResponse(AITestCaseGenerationBase):
    """AI测试用例生成响应"""
    model_config = {"from_attributes": True}
    
    id: int = Field(..., description="任务ID")
    generated_count: int = Field(..., description="生成的测试用例数量")
    status: str = Field(..., description="生成状态")
    error_message: Optional[str] = Field(None, description="错误信息")
    generation_time: Optional[int] = Field(None, description="生成耗时(秒)")
    user_id: int = Field(..., description="创建用户ID")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class AITestCaseGenerationRequest(BaseModel):
    """AI测试用例生成请求"""
    task_name: str = Field(..., description="任务名称", max_length=200)
    requirement_description: str = Field(..., description="需求描述")
    project_id: int = Field(..., description="所属项目ID")
    module_id: Optional[int] = Field(None, description="所属模块ID")
    prompt_template_id: Optional[int] = Field(None, description="使用的提示词模板ID，不传则使用默认模板")
    ai_model_config_id: Optional[int] = Field(None, description="使用的AI模型配置ID，不传则使用默认模型")
    generate_count: int = Field(5, description="期望生成的测试用例数量", ge=1, le=20)


class GeneratedTestCase(BaseModel):
    """生成的测试用例"""
    id: Optional[str] = Field(None, description="临时唯一标识符，用于前端选择")
    case_name: str = Field(..., description="用例名称")
    case_level: str = Field("medium", description="用例等级：low-低，medium-中，high-高")
    precondition: Optional[str] = Field(None, description="前置条件")
    test_steps: str = Field(..., description="测试步骤")
    expected_result: str = Field(..., description="预期结果")
    is_smoke: bool = Field(False, description="是否冒烟用例")


class AITestCaseGenerationResult(BaseModel):
    """AI测试用例生成结果"""
    success: bool = Field(..., description="是否成功")
    task_id: int = Field(..., description="任务ID")
    generated_cases: List[GeneratedTestCase] = Field([], description="生成的测试用例列表")
    saved_count: int = Field(0, description="成功保存的测试用例数量")
    error_message: Optional[str] = Field(None, description="错误信息")
    generation_time: Optional[float] = Field(None, description="生成耗时(秒)")


class TestCasePreview(BaseModel):
    """测试用例预览"""
    cases: List[GeneratedTestCase] = Field(..., description="测试用例列表")
    total_count: int = Field(..., description="总数量")


class SaveGeneratedTestCases(BaseModel):
    """保存生成的测试用例"""
    task_id: int = Field(..., description="任务ID")
    cases: List[GeneratedTestCase] = Field(..., description="要保存的测试用例列表")
