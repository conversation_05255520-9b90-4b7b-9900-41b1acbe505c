# Vue-FastAPI 测试平台依赖库
# 更新时间: 2025-01-23

# ===== 核心框架 =====
fastapi==0.111.0                    # 高性能异步Web框架
uvicorn==0.30.6                     # ASGI服务器
pydantic==2.9.0                     # 数据验证和序列化
pydantic-settings==2.4.0            # Pydantic配置管理
pydantic_core==2.23.0               # Pydantic核心库
annotated-types==0.6.0              # 类型注解支持

# ===== 数据库相关 =====Ï
tortoise-orm==0.20.1                # 异步ORM框架
aerich==0.7.2                       # 数据库迁移工具
aiosqlite==0.17.0                   # SQLite异步驱动
pypika-tortoise==0.1.6              # SQL查询构建器
aiomysql==0.2.0                     # MySQL异步驱动（新增）

# ===== 身份认证与安全 =====
PyJWT==2.9.0                        # JWT令牌处理
passlib==1.7.4                      # 密码哈希
argon2-cffi==23.1.0                 # Argon2密码哈希算法
argon2-cffi-bindings==21.2.0        # Argon2绑定库
email_validator==2.2.0              # 邮箱验证

# ===== HTTP客户端与网络 =====
httpx==0.27.0                       # 异步HTTP客户端
httpcore==1.0.5                     # HTTP核心库
h11==0.14.0                         # HTTP/1.1协议实现
httptools==0.6.1                    # HTTP解析工具
certifi==2024.7.4                   # SSL证书验证
idna==3.8                           # 国际化域名处理
sniffio==1.3.1                      # 异步库检测
anyio==4.4.0                        # 异步兼容层

# ===== 数据处理与分析 =====
pandas==2.0.3                       # 数据分析库
openpyxl==3.1.2                     # Excel文件处理
jsonpath-ng==1.6.1                  # JSON路径查询
jsonschema==4.23.0                  # JSON模式验证
pypinyin==0.51.0                    # 中文拼音转换

# ===== 图像处理与OCR =====
Pillow==11.2.1                      # 图像处理库
ddddocr==1.5.6                      # 简单易用的验证码识别库

# ===== 任务调度与定时任务 =====
apscheduler==3.10.4                 # 任务调度器
croniter==1.4.1                     # Cron表达式解析

# ===== AI与机器学习 =====
langchain==0.1.0                    # LangChain框架（新增）
langchain-core==0.1.0               # LangChain核心库（新增）
langchain-community==0.0.10         # LangChain社区组件（新增）
openai==1.12.0                      # OpenAI API客户端（新增）
tiktoken==0.5.2                     # OpenAI分词器（新增）

# ===== 日志与监控 =====
loguru==0.7.2                       # 现代化日志库

# ===== 开发工具与代码质量 =====
isort==5.13.2                       # 导入排序
ruff==0.0.281                       # 快速Python代码检查器
mypy-extensions==1.0.0              # MyPy扩展


# ===== 时间与日期处理 =====
pytz==2024.1                        # 时区处理
tzdata==2024.1                      # 时区数据
iso8601==1.1.0                      # ISO8601日期解析

# ===== JSON与序列化 =====
orjson==3.10.7                      # 高性能JSON库
ujson==5.10.0                       # 超快JSON库

# ===== 模板与文档 =====
Jinja2==3.1.4                       # 模板引擎
MarkupSafe==2.1.5                   # 安全标记处理
markdown-it-py==3.0.0               # Markdown解析器
mdurl==0.1.2                        # Markdown URL处理

# ===== 终端与CLI =====
rich==13.8.0                        # 富文本终端输出
Pygments==2.18.0                    # 语法高亮
click==8.1.7                        # CLI框架
typer==0.12.5                       # 现代CLI框架
shellingham==1.5.4                  # Shell检测

# ===== 配置与环境 =====
python-dotenv==1.0.1                # 环境变量加载
PyYAML==6.0.2                       # YAML解析
tomlkit==0.13.2                     # TOML处理

# ===== 系统与工具 =====
setuptools==70.3.0                  # Python包管理工具
packaging==24.1                     # 包版本处理
pathspec==0.12.1                    # 路径规范处理
platformdirs==4.2.2                 # 平台目录
typing_extensions==4.12.2           # 类型扩展
dictdiffer==0.9.0                   # 字典差异比较

# ===== 异步与并发 =====
uvloop==0.20.0                      # 高性能事件循环
watchfiles==0.23.0                  # 文件监控
websockets==13.0                    # WebSocket支持

# ===== 加密与安全 =====
cffi==1.17.0                        # C外部函数接口
pycparser==2.22                     # C解析器

# ===== FastAPI扩展 =====
fastapi-cli==0.0.5                  # FastAPI命令行工具

# ===== 缓存与存储 =====
redis==5.0.1                        # Redis客户端（新增）
aioredis==2.0.1                     # 异步Redis客户端（新增）

# ===== 通知服务 =====
requests==2.31.0                    # HTTP请求库（新增，用于钉钉通知）

# ===== 数据验证与转换 =====
dnspython==2.6.1                    # DNS解析库
