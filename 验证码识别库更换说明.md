# 验证码识别库更换说明

## 更换概述

已成功将验证码识别功能从复杂的 OpenCV + Tesseract 方案替换为更简单易用的 ddddocr 库。

## 更改内容

### 1. 依赖库更换

**移除的库：**
- `pytesseract==0.3.13` - Tesseract OCR Python包装器
- `opencv-python-headless==4.11.0.86` - 图像处理库（无GUI版本）

**新增的库：**
- `ddddocr==1.5.6` - 简单易用的验证码识别库

**保留的库：**
- `Pillow==11.2.1` - 图像处理库（ddddocr也需要）

### 2. 代码更改

**文件：`app/services/captcha_service.py`**

1. **导入部分更改：**
   ```python
   # 旧代码
   import cv2
   import numpy as np
   from PIL import Image
   
   # 新代码
   import ddddocr
   ```

2. **识别方法简化：**
   - 移除了复杂的图像预处理逻辑（高斯模糊、二值化、形态学操作等）
   - 移除了模拟OCR识别方法
   - 新增了简单的 `_ocr_with_ddddocr()` 方法

3. **核心识别代码：**
   ```python
   def _ocr_with_ddddocr(self, image_data: bytes) -> Optional[str]:
       """使用ddddocr识别验证码"""
       try:
           # 创建ddddocr实例
           ocr = ddddocr.DdddOcr(show_ad=False)
           
           # 直接使用ddddocr识别图片字节数据
           recognized_text = ocr.classification(image_data)
           
           return recognized_text
       except Exception as e:
           logger.error(f"ddddocr识别失败: {str(e)}")
           return None
   ```

### 3. 测试验证

**测试文件：`test_captcha_ocr.py`**
- 更新了测试代码以适配新的ddddocr实现
- 测试结果显示ddddocr能够成功识别验证码

**测试结果：**
```
✅ Base64解码成功，字节长度: 1068
✅ ddddocr识别成功: 1234
```

## ddddocr 库优势

### 1. 简单易用
- **无需复杂配置**：开箱即用，无需安装额外的系统依赖
- **API简洁**：只需几行代码即可完成验证码识别
- **无需图像预处理**：内置了图像处理逻辑

### 2. 功能强大
- **支持多种验证码类型**：
  - 数字验证码
  - 字母验证码
  - 数字+字母混合验证码
  - 简单的中文验证码
- **识别准确率高**：基于深度学习模型训练

### 3. 部署友好
- **体积小**：相比OpenCV+Tesseract方案，依赖更少
- **跨平台**：支持Windows、Linux、macOS
- **容器化友好**：Docker部署时无需安装额外系统包

## 使用方法

### 基本用法
```python
import ddddocr

# 创建识别器
ocr = ddddocr.DdddOcr(show_ad=False)

# 识别验证码（传入图片字节数据）
result = ocr.classification(image_bytes)
print(f"识别结果: {result}")
```

### 在项目中的集成
验证码识别功能已完全集成到现有的Token自动刷新流程中：

1. **获取验证码**：从API获取验证码图片和codeKey
2. **识别验证码**：使用ddddocr自动识别验证码内容
3. **自动登录**：将识别结果用于登录请求

## 注意事项

1. **首次使用**：ddddocr首次运行时会下载模型文件，可能需要一些时间
2. **网络环境**：如果网络受限，可以手动下载模型文件到本地
3. **识别准确率**：对于复杂的验证码（如严重扭曲、噪点很多），可能需要多次重试

## 总结

通过使用ddddocr库，我们成功简化了验证码识别的实现：
- **代码量减少**：从100+行复杂的图像处理代码简化为20行
- **依赖减少**：移除了OpenCV和Tesseract等重型依赖
- **维护性提升**：代码更简洁，更容易维护和调试
- **功能增强**：支持更多类型的验证码识别

这次更换大大提升了验证码识别功能的可用性和可维护性。
